#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能采样测试脚本
通过采样方法快速评估大规模性能，避免长时间运行
"""

import json
import logging
import os
import sys
import time
import random
import math
from datetime import datetime
from typing import Dict, List, Any

# 导入项目模块
from config import Config
from neo4j_connection import Neo4jConnection
from text_processor import process_json_chunk, reset_database, close_resources

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartSamplingTester:
    """智能采样测试器"""
    
    def __init__(self):
        self.neo4j_conn = None
        self.results = []
        
    def setup_neo4j_connection(self):
        """建立Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info(f"成功连接到 Neo4j")
            return True
        except Exception as e:
            logger.error(f"Neo4j连接失败: {e}")
            return False
    
    def load_data(self, json_file_path: str) -> List[Dict]:
        """加载数据"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            nodes = data.get("nodes", [])
            logger.info(f"加载数据: {len(nodes)} 个节点")
            return nodes
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return []
    
    def sampling_test(self, all_nodes: List[Dict], target_size: int, sample_size: int) -> Dict:
        """采样测试"""
        logger.info(f"开始采样测试: 目标{target_size}个节点，采样{sample_size}个")
        
        # 随机采样
        if len(all_nodes) < target_size:
            logger.warning(f"数据量不足: {len(all_nodes)} < {target_size}")
            sample_nodes = all_nodes[:sample_size]
        else:
            target_nodes = random.sample(all_nodes, target_size)
            sample_nodes = random.sample(target_nodes, sample_size)
        
        # 清空数据库
        self.neo4j_conn.clear_database()
        reset_database(self.neo4j_conn)
        
        start_time = time.perf_counter()
        start_datetime = datetime.now()
        
        try:
            # 处理采样节点
            results = []
            processed_count = 0
            failed_count = 0
            
            # 分小批次处理
            small_batch_size = 3
            for i in range(0, len(sample_nodes), small_batch_size):
                small_batch = sample_nodes[i:i + small_batch_size]
                
                batch_result = process_json_chunk(
                    neo4j_conn=self.neo4j_conn,
                    data=small_batch,
                    crawl_timestamp=datetime.now().isoformat(),
                    source_type="sampling_test",
                    metrics={"ratings": 4.0}
                )
                
                results.extend(batch_result)
                
                for result in batch_result:
                    if result["status"] == "success":
                        processed_count += 1
                    else:
                        failed_count += 1
                
                # 显示进度
                current_processed = min(i + small_batch_size, len(sample_nodes))
                elapsed = time.perf_counter() - start_time
                speed = current_processed / elapsed if elapsed > 0 else 0
                
                logger.info(f"采样进度: {current_processed}/{len(sample_nodes)} | "
                           f"耗时: {elapsed:.1f}秒 | 速度: {speed:.3f}节点/秒")
            
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            # 计算采样结果
            sample_api_calls = sample_size * (sample_size - 1) // 2
            target_api_calls = target_size * (target_size - 1) // 2
            
            # 推算全量性能
            avg_time_per_api = duration / sample_api_calls if sample_api_calls > 0 else 0
            estimated_total_time = target_api_calls * avg_time_per_api
            
            result = {
                "test_type": "sampling",
                "target_size": target_size,
                "sample_size": sample_size,
                "sample_duration_seconds": duration,
                "sample_api_calls": sample_api_calls,
                "avg_time_per_api": avg_time_per_api,
                "target_api_calls": target_api_calls,
                "estimated_total_seconds": estimated_total_time,
                "estimated_total_minutes": estimated_total_time / 60,
                "estimated_total_hours": estimated_total_time / 3600,
                "sample_speed": sample_size / duration if duration > 0 else 0,
                "estimated_target_speed": target_size / estimated_total_time if estimated_total_time > 0 else 0,
                "success_count": processed_count,
                "failed_count": failed_count,
                "success_rate": processed_count / sample_size * 100 if sample_size > 0 else 0,
                "start_time": start_datetime.isoformat(),
                "end_time": datetime.now().isoformat()
            }
            
            logger.info(f"采样测试完成:")
            logger.info(f"  采样耗时: {duration:.1f}秒")
            logger.info(f"  平均API时间: {avg_time_per_api:.3f}秒")
            logger.info(f"  预估{target_size}个节点总时间: {estimated_total_time/60:.1f}分钟")
            logger.info(f"  预估处理速度: {result['estimated_target_speed']:.3f}节点/秒")
            
            return result
            
        except Exception as e:
            logger.error(f"采样测试失败: {e}")
            return {
                "test_type": "sampling",
                "target_size": target_size,
                "sample_size": sample_size,
                "status": "failed",
                "error": str(e)
            }
    
    def run_smart_sampling_tests(self, json_file_path: str):
        """运行智能采样测试"""
        logger.info("🚀 开始智能采样测试")
        
        # 加载数据
        all_nodes = self.load_data(json_file_path)
        if not all_nodes:
            logger.error("无法加载数据")
            return
        
        # 采样测试配置
        sampling_configs = [
            {"target": 300, "sample": 30},   # 10%采样，预计3分钟
            {"target": 500, "sample": 50},   # 10%采样，预计8分钟  
            {"target": 1000, "sample": 50},  # 5%采样，预计8分钟
        ]
        
        logger.info(f"采样测试配置: {len(sampling_configs)} 个测试")
        for config in sampling_configs:
            logger.info(f"  {config['target']}个节点 -> 采样{config['sample']}个")
        
        # 执行采样测试
        for i, config in enumerate(sampling_configs):
            logger.info(f"\n{'='*60}")
            logger.info(f"采样测试 {i+1}/{len(sampling_configs)}")
            logger.info(f"目标: {config['target']}个节点，采样: {config['sample']}个")
            logger.info(f"{'='*60}")
            
            result = self.sampling_test(all_nodes, config["target"], config["sample"])
            self.results.append(result)
            
            # 短暂休息
            if i < len(sampling_configs) - 1:
                logger.info("休息5秒...")
                time.sleep(5)
        
        # 保存结果
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """保存结果"""
        try:
            results_file = "smart_sampling_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"采样测试结果已保存: {results_file}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def print_summary(self):
        """打印结果摘要"""
        logger.info("\n" + "="*80)
        logger.info("智能采样测试结果摘要")
        logger.info("="*80)
        
        for result in self.results:
            if result.get("status") != "failed":
                target = result["target_size"]
                sample = result["sample_size"]
                sample_time = result["sample_duration_seconds"]
                estimated_time = result["estimated_total_minutes"]
                estimated_hours = result["estimated_total_hours"]
                speed = result["estimated_target_speed"]
                
                logger.info(f"🔸 {target}个节点 (采样{sample}个):")
                logger.info(f"   采样耗时: {sample_time:.1f}秒")
                logger.info(f"   预估总时间: {estimated_time:.1f}分钟 ({estimated_hours:.1f}小时)")
                logger.info(f"   预估速度: {speed:.3f}节点/秒")
                logger.info(f"   API调用: {result['target_api_calls']:,}次")
                logger.info("")
        
        # 与已有数据对比
        logger.info("📊 与实际测试数据对比:")
        logger.info("-" * 40)
        
        actual_data = [
            {"size": 10, "time": 100, "speed": 0.100},
            {"size": 50, "time": 414, "speed": 0.121},
            {"size": 100, "time": 767, "speed": 0.130},
            {"size": 200, "time": 1730, "speed": 0.116}
        ]
        
        for data in actual_data:
            logger.info(f"  {data['size']}个节点: {data['time']}秒, {data['speed']:.3f}节点/秒 (实际)")
        
        logger.info("="*80)
    
    def cleanup(self):
        """清理资源"""
        if self.neo4j_conn:
            self.neo4j_conn.close()
        close_resources()

def main():
    """主函数"""
    tester = SmartSamplingTester()
    
    try:
        # 建立连接
        if not tester.setup_neo4j_connection():
            logger.error("无法建立Neo4j连接")
            return
        
        # 数据文件路径
        json_file_path = os.path.join("data", "tibet_1000_real_attractions.json")
        if not os.path.exists(json_file_path):
            logger.error(f"数据文件不存在: {json_file_path}")
            return
        
        # 运行采样测试
        tester.run_smart_sampling_tests(json_file_path)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
