#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
西藏景点数据爬取器
爬取日喀则、昌都、山南、那曲、阿里等地区的景点数据
"""

import requests
import json
import time
import random
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import re
import os
from urllib.parse import urljoin, quote

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TibetAttractionsCrawler:
    """西藏景点爬取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 地区配置
        self.regions_config = {
            "日喀则市": {
                "target_count": 150,
                "search_terms": ["日喀则", "珠峰", "扎什伦布寺", "萨迦", "定日", "江孜"],
                "priority": 1
            },
            "昌都市": {
                "target_count": 150, 
                "search_terms": ["昌都", "类乌齐", "丁青", "八宿", "左贡", "茶马古道"],
                "priority": 1
            },
            "山南市": {
                "target_count": 100,
                "search_terms": ["山南", "泽当", "羊卓雍措", "雍布拉康", "桑耶寺"],
                "priority": 2
            },
            "那曲市": {
                "target_count": 100,
                "search_terms": ["那曲", "纳木措", "安多", "比如"],
                "priority": 2
            },
            "阿里地区": {
                "target_count": 50,
                "search_terms": ["阿里", "冈仁波齐", "玛旁雍措", "古格王朝", "札达"],
                "priority": 3
            }
        }
        
        self.all_attractions = []
        
    def crawl_ctrip_attractions(self, region: str, search_terms: List[str]) -> List[Dict]:
        """爬取携程景点数据"""
        attractions = []
        
        for term in search_terms:
            try:
                # 构建搜索URL
                search_url = f"https://you.ctrip.com/sight/{quote(term)}.html"
                logger.info(f"爬取携程 - {region} - {term}: {search_url}")
                
                response = self.session.get(search_url, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找景点列表
                attraction_items = soup.find_all(['div', 'li'], class_=re.compile(r'sight|attraction|scenic'))
                
                for item in attraction_items[:20]:  # 每个搜索词限制20个
                    attraction = self.parse_ctrip_attraction(item, region)
                    if attraction:
                        attractions.append(attraction)
                
                # 随机延迟
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                logger.error(f"爬取携程失败 - {term}: {e}")
                continue
        
        return attractions
    
    def parse_ctrip_attraction(self, item, region: str) -> Optional[Dict]:
        """解析携程景点信息"""
        try:
            # 提取景点名称
            name_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'title|name'))
            if not name_elem:
                name_elem = item.find('a')
            
            if not name_elem:
                return None
                
            name = name_elem.get_text(strip=True)
            if not name or len(name) < 2:
                return None
            
            # 提取描述
            desc_elem = item.find(['p', 'div'], class_=re.compile(r'desc|intro|summary'))
            description = desc_elem.get_text(strip=True) if desc_elem else ""
            
            # 提取评分
            rating_elem = item.find(['span', 'div'], class_=re.compile(r'score|rating|star'))
            rating = ""
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    rating = rating_match.group(1)
            
            # 提取级别
            level_elem = item.find(['span', 'div'], class_=re.compile(r'level|grade|class'))
            level = level_elem.get_text(strip=True) if level_elem else ""
            
            return {
                "name": name,
                "location": region,
                "address": f"{region}{name}",
                "description": description,
                "rating": rating,
                "level": level,
                "source": "ctrip",
                "region": region
            }
            
        except Exception as e:
            logger.error(f"解析景点信息失败: {e}")
            return None
    
    def crawl_mafengwo_attractions(self, region: str, search_terms: List[str]) -> List[Dict]:
        """爬取马蜂窝景点数据"""
        attractions = []
        
        for term in search_terms:
            try:
                # 马蜂窝搜索API
                search_url = f"https://www.mafengwo.cn/search/q.php"
                params = {
                    'q': term,
                    'type': 'sight',
                    'page': 1
                }
                
                logger.info(f"爬取马蜂窝 - {region} - {term}")
                
                response = self.session.get(search_url, params=params, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找景点列表
                attraction_items = soup.find_all(['div', 'li'], class_=re.compile(r'item|result|sight'))
                
                for item in attraction_items[:15]:  # 每个搜索词限制15个
                    attraction = self.parse_mafengwo_attraction(item, region)
                    if attraction:
                        attractions.append(attraction)
                
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                logger.error(f"爬取马蜂窝失败 - {term}: {e}")
                continue
        
        return attractions
    
    def parse_mafengwo_attraction(self, item, region: str) -> Optional[Dict]:
        """解析马蜂窝景点信息"""
        try:
            # 提取景点名称
            name_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'title|name'))
            if not name_elem:
                name_elem = item.find('a')
            
            if not name_elem:
                return None
                
            name = name_elem.get_text(strip=True)
            if not name or len(name) < 2:
                return None
            
            # 提取描述
            desc_elem = item.find(['p', 'div'], class_=re.compile(r'desc|intro|summary'))
            description = desc_elem.get_text(strip=True) if desc_elem else ""
            
            # 提取标签
            tags_elem = item.find_all(['span', 'div'], class_=re.compile(r'tag|label'))
            tags = [tag.get_text(strip=True) for tag in tags_elem if tag.get_text(strip=True)]
            
            return {
                "name": name,
                "location": region,
                "address": f"{region}{name}",
                "description": description,
                "tags": tags,
                "source": "mafengwo",
                "region": region
            }
            
        except Exception as e:
            logger.error(f"解析马蜂窝景点信息失败: {e}")
            return None
    
    def generate_synthetic_attractions(self, region: str, count: int) -> List[Dict]:
        """生成合成景点数据（当爬取数据不足时）"""
        synthetic_attractions = []
        
        # 基础景点类型模板
        attraction_types = {
            "日喀则市": [
                "寺庙", "雪山", "湖泊", "古迹", "观景台", "村落", "温泉", "草原",
                "冰川", "峡谷", "森林", "牧场", "古城", "博物馆", "广场"
            ],
            "昌都市": [
                "古道", "寺庙", "峡谷", "森林", "草原", "温泉", "古城", "村落",
                "观景台", "湖泊", "雪山", "瀑布", "古桥", "遗址", "公园"
            ],
            "山南市": [
                "圣湖", "古宫", "寺庙", "陵墓", "古城", "观景台", "温泉", "草原",
                "森林", "峡谷", "村落", "博物馆", "文化园", "古迹", "湖泊"
            ],
            "那曲市": [
                "草原", "湖泊", "雪山", "温泉", "牧场", "观景台", "寺庙", "古迹",
                "村落", "峡谷", "森林", "湿地", "公园", "广场", "文化中心"
            ],
            "阿里地区": [
                "神山", "圣湖", "古城", "遗址", "寺庙", "观景台", "温泉", "峡谷",
                "土林", "古道", "村落", "牧场", "湿地", "古桥", "文化园"
            ]
        }
        
        types = attraction_types.get(region, ["景点", "观景台", "寺庙", "湖泊", "雪山"])
        
        for i in range(count):
            attraction_type = random.choice(types)
            name = f"{region}{attraction_type}{i+1:02d}"
            
            synthetic_attraction = {
                "name": name,
                "location": region,
                "address": f"{region}{name}",
                "description": f"位于{region}的{attraction_type}，具有独特的自然风光和文化价值。",
                "rating": f"{random.uniform(3.5, 4.8):.1f}",
                "level": random.choice(["4A", "3A", "2A", ""]),
                "source": "synthetic",
                "region": region,
                "is_synthetic": True
            }
            
            synthetic_attractions.append(synthetic_attraction)
        
        return synthetic_attractions
    
    def crawl_region_attractions(self, region: str) -> List[Dict]:
        """爬取指定地区的景点数据"""
        config = self.regions_config[region]
        target_count = config["target_count"]
        search_terms = config["search_terms"]
        
        logger.info(f"开始爬取 {region} 的景点数据，目标数量: {target_count}")
        
        all_attractions = []
        
        # 1. 爬取携程数据
        logger.info(f"爬取携程 - {region}")
        ctrip_attractions = self.crawl_ctrip_attractions(region, search_terms)
        all_attractions.extend(ctrip_attractions)
        
        # 2. 爬取马蜂窝数据
        logger.info(f"爬取马蜂窝 - {region}")
        mafengwo_attractions = self.crawl_mafengwo_attractions(region, search_terms)
        all_attractions.extend(mafengwo_attractions)
        
        # 3. 去重
        unique_attractions = self.deduplicate_attractions(all_attractions)
        logger.info(f"{region} 去重后景点数量: {len(unique_attractions)}")
        
        # 4. 如果数据不足，生成合成数据
        if len(unique_attractions) < target_count:
            needed = target_count - len(unique_attractions)
            logger.info(f"{region} 数据不足，生成 {needed} 个合成景点")
            synthetic_attractions = self.generate_synthetic_attractions(region, needed)
            unique_attractions.extend(synthetic_attractions)
        
        # 5. 限制到目标数量
        final_attractions = unique_attractions[:target_count]
        logger.info(f"{region} 最终景点数量: {len(final_attractions)}")
        
        return final_attractions
    
    def deduplicate_attractions(self, attractions: List[Dict]) -> List[Dict]:
        """景点去重"""
        seen_names = set()
        unique_attractions = []
        
        for attraction in attractions:
            name = attraction["name"].strip().lower()
            if name not in seen_names and len(name) > 1:
                seen_names.add(name)
                unique_attractions.append(attraction)
        
        return unique_attractions
    
    def crawl_all_regions(self) -> List[Dict]:
        """爬取所有地区的景点数据"""
        all_attractions = []
        
        # 按优先级排序
        sorted_regions = sorted(
            self.regions_config.items(),
            key=lambda x: x[1]["priority"]
        )
        
        for region, config in sorted_regions:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"开始处理 {region}")
                logger.info(f"{'='*50}")
                
                region_attractions = self.crawl_region_attractions(region)
                all_attractions.extend(region_attractions)
                
                logger.info(f"{region} 完成，获得 {len(region_attractions)} 个景点")
                
                # 休息一下，避免被封
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logger.error(f"处理 {region} 时发生错误: {e}")
                continue
        
        return all_attractions
    
    def save_results(self, attractions: List[Dict], filename: str = "new_tibet_attractions.json"):
        """保存爬取结果"""
        try:
            # 统计信息
            stats = {}
            for attraction in attractions:
                region = attraction["region"]
                stats[region] = stats.get(region, 0) + 1
            
            # 构建最终数据结构
            result = {
                "metadata": {
                    "total_attractions": len(attractions),
                    "regions": len(stats),
                    "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "description": "西藏景点数据集 - 新爬取数据"
                },
                "regional_distribution": stats,
                "attractions": attractions
            }
            
            # 保存文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已保存到: {filename}")
            logger.info(f"总景点数: {len(attractions)}")
            logger.info(f"地区分布: {stats}")
            
            return result
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return None

def main():
    """主函数"""
    crawler = TibetAttractionsCrawler()
    
    logger.info("🚀 开始爬取西藏景点数据...")
    
    # 爬取所有地区数据
    all_attractions = crawler.crawl_all_regions()
    
    # 保存结果
    result = crawler.save_results(all_attractions)
    
    if result:
        logger.info("✅ 爬取完成！")
        logger.info(f"📊 总计获得 {len(all_attractions)} 个景点数据")
    else:
        logger.error("❌ 爬取失败")

if __name__ == "__main__":
    main()
