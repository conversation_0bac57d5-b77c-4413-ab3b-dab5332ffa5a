#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查500个景点数据集
"""

import json
import os

def check_500_data():
    """检查500个景点数据"""
    project_dir = os.path.dirname(os.path.abspath(__file__))
    json_file_path = os.path.join(project_dir, "data", "merged_500_knowledge_graph.json")
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        nodes = data.get("nodes", [])
        metadata = data.get("metadata", {})
        
        print("🎯 500个景点数据集检查报告")
        print("=" * 60)
        print(f"JSON文件路径: {json_file_path}")
        print(f"总节点数: {len(nodes)}")
        print(f"元数据: {metadata}")
        
        # 统计数据来源
        source_count = {}
        location_count = {}
        
        for node in nodes:
            source = node.get("source", "unknown")
            location = node.get("location", "unknown")
            
            source_count[source] = source_count.get(source, 0) + 1
            location_count[location] = location_count.get(location, 0) + 1
        
        print("\n📊 数据来源分布:")
        for source, count in source_count.items():
            print(f"  {source}: {count} 个节点")
        
        print("\n🏙️ 地区分布:")
        for location, count in location_count.items():
            print(f"  {location}: {count} 个节点")
        
        # 显示前10个节点
        print("\n📋 前10个节点信息:")
        for i, node in enumerate(nodes[:10]):
            name = node.get('name', 'N/A')
            location = node.get('location', 'N/A')
            source = node.get('source', 'N/A')
            print(f"{i+1:>2}. {name:<20} | {location:<10} | {source}")
        
        # 检查批次测试需求
        batch_sizes = [10, 50, 100, 150, 200, 250, 300, 400, 500]
        max_batch = max(batch_sizes)
        
        print(f"\n🚀 批次测试准备情况:")
        print(f"最大批次大小: {max_batch}")
        print(f"当前数据量: {len(nodes)}")
        
        if len(nodes) >= max_batch:
            print("✅ 数据量充足，可以进行所有批次测试")
            print(f"📈 测试批次: {batch_sizes}")
            
            # 预估API调用次数
            print(f"\n🔢 预估API调用次数:")
            total_api_calls = 0
            for batch_size in batch_sizes:
                api_calls = batch_size * (batch_size - 1) // 2
                total_api_calls += api_calls
                print(f"  {batch_size:>3}个节点: {api_calls:>8,} 次API调用")
            
            print(f"  总计: {total_api_calls:>8,} 次API调用")
            
            # 预估时间（基于之前的测试结果）
            avg_api_time = 0.1  # 平均每次API调用0.1秒
            estimated_time = total_api_calls * avg_api_time
            print(f"\n⏱️ 预估总时间: {estimated_time:.0f}秒 ({estimated_time/60:.1f}分钟 / {estimated_time/3600:.1f}小时)")
            
        else:
            print("⚠️ 数据量不足")
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {json_file_path}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
    except Exception as e:
        print(f"❌ 检查数据时发生错误: {e}")

if __name__ == "__main__":
    check_500_data()
