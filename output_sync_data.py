#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🎯 同步批量处理实验完整数据（以秒为单位）")
print("=" * 80)
print(f"{'批次大小':<8} {'处理时间(秒)':<12} {'处理速度':<12} {'每节点时间(秒)':<14} {'成功率':<8}")
print("-" * 80)

# 实验数据
data = [
    (10, 117.98, 0.085, 11.80, "100%"),
    (50, 555.91, 0.090, 11.12, "100%"),
    (100, 1207.37, 0.083, 12.07, "100%"),
    (150, 1568.88, 0.096, 10.46, "100%"),
    (200, 1909.31, 0.105, 9.55, "100%"),
    (250, 2167.16, 0.115, 8.67, "100%")
]

total_nodes = 0
total_time = 0
total_speed = 0
total_per_node = 0

for batch_size, time_sec, speed, per_node, success_rate in data:
    print(f"{batch_size:<8} {time_sec:<12.0f} {speed:<12.3f} {per_node:<14.1f} {success_rate:<8}")
    total_nodes += batch_size
    total_time += time_sec
    total_speed += speed
    total_per_node += per_node

print("-" * 80)
print(f"总计: {total_nodes} 个节点")
print(f"总时间: {total_time:.0f} 秒 ({total_time/60:.1f} 分钟)")
print(f"平均速度: {total_speed/len(data):.3f} 节点/秒")
print(f"平均每节点: {total_per_node/len(data):.1f} 秒")
print("整体成功率: 100%")

print("\n" + "=" * 80)
print("📊 详细时间数据（秒）:")
print("=" * 80)
for batch_size, time_sec, speed, per_node, success_rate in data:
    print(f"{batch_size:>3}个节点: {time_sec:>7.0f}秒 | 速度: {speed:.3f}节点/秒 | 每节点: {per_node:.1f}秒")

print("\n" + "=" * 80)
print("🚀 异步对比预期（基于同步基准）:")
print("=" * 80)
print("预期时间提升: 2-5倍 (异步时间 = 同步时间 ÷ 2~5)")
print("预期速度提升: 2-5倍 (异步速度 = 同步速度 × 2~5)")
print("\n示例预期（以3倍提升计算）:")
for batch_size, time_sec, speed, per_node, success_rate in data:
    async_time = time_sec / 3
    async_speed = speed * 3
    print(f"{batch_size:>3}个节点: {async_time:>7.0f}秒 (vs {time_sec:.0f}秒) | 速度: {async_speed:.3f}节点/秒 (vs {speed:.3f}节点/秒)")
