#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实西藏景点数据爬虫
从携程、马蜂窝等网站爬取真实的西藏景点数据
"""

import requests
import json
import time
import random
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import re
import os
from urllib.parse import urljoin, quote
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealTibetCrawler:
    """真实西藏景点数据爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 目标地区和数量
        self.target_regions = {
            "日喀则": {"target": 150, "keywords": ["日喀则", "珠峰", "扎什伦布寺", "萨迦", "定日", "江孜", "亚东"]},
            "昌都": {"target": 150, "keywords": ["昌都", "类乌齐", "丁青", "八宿", "左贡", "芒康", "茶马古道"]},
            "山南": {"target": 100, "keywords": ["山南", "泽当", "羊卓雍措", "雍布拉康", "桑耶寺", "琼结"]},
            "那曲": {"target": 100, "keywords": ["那曲", "纳木措", "安多", "比如", "班戈", "申扎"]},
            "阿里": {"target": 50, "keywords": ["阿里", "冈仁波齐", "玛旁雍措", "古格王朝", "札达", "普兰"]},
        }
        
        self.all_attractions = []
        
    def setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            driver = webdriver.Chrome(options=chrome_options)
            return driver
        except Exception as e:
            logger.error(f"设置Selenium失败: {e}")
            return None
    
    def crawl_ctrip_region(self, region: str, keywords: List[str]) -> List[Dict]:
        """爬取携程指定地区的景点"""
        attractions = []
        
        for keyword in keywords:
            try:
                # 携程景点搜索URL
                search_url = f"https://you.ctrip.com/sight/{quote(keyword)}.html"
                logger.info(f"爬取携程 - {region} - {keyword}")
                
                response = self.session.get(search_url, timeout=15)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 查找景点列表 - 携程的景点通常在这些类中
                selectors = [
                    '.list_wide_mod2',
                    '.sight_item',
                    '.list_mod2',
                    '.ttd_sight',
                    '.sight_item_detail'
                ]
                
                items_found = []
                for selector in selectors:
                    items = soup.select(selector)
                    if items:
                        items_found.extend(items)
                        break
                
                if not items_found:
                    # 尝试通用选择器
                    items_found = soup.find_all(['div', 'li'], class_=re.compile(r'sight|attraction|scenic'))
                
                logger.info(f"找到 {len(items_found)} 个潜在景点项目")
                
                for item in items_found[:25]:  # 限制每个关键词25个
                    attraction = self.parse_ctrip_item(item, region, keyword)
                    if attraction:
                        attractions.append(attraction)
                
                # 随机延迟避免被封
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logger.error(f"爬取携程失败 - {keyword}: {e}")
                continue
        
        return attractions
    
    def parse_ctrip_item(self, item, region: str, keyword: str) -> Optional[Dict]:
        """解析携程景点项目"""
        try:
            # 提取景点名称
            name_selectors = ['h3', 'h4', '.sight_item_caption a', '.list_mod2_title a', 'a[title]']
            name = ""
            
            for selector in name_selectors:
                name_elem = item.select_one(selector)
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    if name and len(name) > 1:
                        break
            
            if not name or len(name) < 2:
                return None
            
            # 过滤无效名称
            invalid_keywords = ['更多', '查看', '点击', '详情', '攻略', '酒店', '机票']
            if any(invalid in name for invalid in invalid_keywords):
                return None
            
            # 提取描述
            desc_selectors = ['.sight_item_intro', '.list_mod2_intro', '.sight_desc', 'p']
            description = ""
            
            for selector in desc_selectors:
                desc_elem = item.select_one(selector)
                if desc_elem:
                    description = desc_elem.get_text(strip=True)
                    if description and len(description) > 10:
                        break
            
            # 提取评分
            rating_selectors = ['.score', '.rating', '.star', '[class*="score"]']
            rating = ""
            
            for selector in rating_selectors:
                rating_elem = item.select_one(selector)
                if rating_elem:
                    rating_text = rating_elem.get_text(strip=True)
                    rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                    if rating_match:
                        rating = rating_match.group(1)
                        break
            
            # 提取级别
            level_selectors = ['.level', '.grade', '[class*="level"]', '[class*="grade"]']
            level = ""
            
            for selector in level_selectors:
                level_elem = item.select_one(selector)
                if level_elem:
                    level_text = level_elem.get_text(strip=True)
                    if 'A' in level_text or '级' in level_text:
                        level = level_text
                        break
            
            return {
                "name": name,
                "location": f"{region}市" if region != "阿里" else "阿里地区",
                "address": f"{region}{name}",
                "description": description,
                "rating": rating,
                "level": level,
                "source": "ctrip",
                "region": region,
                "keyword": keyword,
                "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"解析携程项目失败: {e}")
            return None
    
    def crawl_mafengwo_region(self, region: str, keywords: List[str]) -> List[Dict]:
        """爬取马蜂窝指定地区的景点"""
        attractions = []
        
        for keyword in keywords:
            try:
                # 马蜂窝景点搜索
                search_url = f"https://www.mafengwo.cn/search/q.php"
                params = {
                    'q': keyword + ' 景点',
                    'type': 'sight'
                }
                
                logger.info(f"爬取马蜂窝 - {region} - {keyword}")
                
                response = self.session.get(search_url, params=params, timeout=15)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 马蜂窝景点项目选择器
                selectors = [
                    '.search-list .item',
                    '.poi-list .item',
                    '.sight-list .item',
                    '[class*="search"] [class*="item"]'
                ]
                
                items_found = []
                for selector in selectors:
                    items = soup.select(selector)
                    if items:
                        items_found.extend(items)
                        break
                
                if not items_found:
                    items_found = soup.find_all(['div', 'li'], class_=re.compile(r'item|sight|poi'))
                
                logger.info(f"马蜂窝找到 {len(items_found)} 个项目")
                
                for item in items_found[:20]:
                    attraction = self.parse_mafengwo_item(item, region, keyword)
                    if attraction:
                        attractions.append(attraction)
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logger.error(f"爬取马蜂窝失败 - {keyword}: {e}")
                continue
        
        return attractions
    
    def parse_mafengwo_item(self, item, region: str, keyword: str) -> Optional[Dict]:
        """解析马蜂窝景点项目"""
        try:
            # 提取名称
            name_selectors = ['h3', 'h4', '.title', '.name', 'a[title]']
            name = ""
            
            for selector in name_selectors:
                name_elem = item.select_one(selector)
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    if name and len(name) > 1:
                        break
            
            if not name or len(name) < 2:
                return None
            
            # 过滤无效名称
            if any(invalid in name for invalid in ['攻略', '酒店', '机票', '更多']):
                return None
            
            # 提取描述
            desc_elem = item.select_one('.desc, .intro, .summary, p')
            description = desc_elem.get_text(strip=True) if desc_elem else ""
            
            # 提取标签
            tag_elems = item.select('.tag, .label, [class*="tag"]')
            tags = [tag.get_text(strip=True) for tag in tag_elems if tag.get_text(strip=True)]
            
            return {
                "name": name,
                "location": f"{region}市" if region != "阿里" else "阿里地区",
                "address": f"{region}{name}",
                "description": description,
                "tags": tags,
                "source": "mafengwo",
                "region": region,
                "keyword": keyword,
                "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"解析马蜂窝项目失败: {e}")
            return None
    
    def crawl_qunar_region(self, region: str, keywords: List[str]) -> List[Dict]:
        """爬取去哪儿指定地区的景点"""
        attractions = []
        
        for keyword in keywords:
            try:
                # 去哪儿景点搜索
                search_url = f"https://travel.qunar.com/travelbook/list.htm"
                params = {
                    'keyword': keyword,
                    'region': region
                }
                
                logger.info(f"爬取去哪儿 - {region} - {keyword}")
                
                response = self.session.get(search_url, params=params, timeout=15)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 去哪儿景点选择器
                items = soup.select('.sight_item, .list_item, .result_item')
                
                if not items:
                    items = soup.find_all(['div', 'li'], class_=re.compile(r'sight|item|result'))
                
                logger.info(f"去哪儿找到 {len(items)} 个项目")
                
                for item in items[:15]:
                    attraction = self.parse_qunar_item(item, region, keyword)
                    if attraction:
                        attractions.append(attraction)
                
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                logger.error(f"爬取去哪儿失败 - {keyword}: {e}")
                continue
        
        return attractions
    
    def parse_qunar_item(self, item, region: str, keyword: str) -> Optional[Dict]:
        """解析去哪儿景点项目"""
        try:
            # 提取名称
            name_elem = item.select_one('h3, h4, .title, .name, a')
            if not name_elem:
                return None
            
            name = name_elem.get_text(strip=True)
            if not name or len(name) < 2:
                return None
            
            # 提取描述
            desc_elem = item.select_one('.desc, .intro, p')
            description = desc_elem.get_text(strip=True) if desc_elem else ""
            
            return {
                "name": name,
                "location": f"{region}市" if region != "阿里" else "阿里地区",
                "address": f"{region}{name}",
                "description": description,
                "source": "qunar",
                "region": region,
                "keyword": keyword,
                "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            logger.error(f"解析去哪儿项目失败: {e}")
            return None
    
    def deduplicate_attractions(self, attractions: List[Dict]) -> List[Dict]:
        """景点去重"""
        seen_names = set()
        unique_attractions = []
        
        for attraction in attractions:
            name = attraction["name"].strip().lower()
            # 更严格的去重条件
            name_key = f"{name}_{attraction['region']}"
            
            if name_key not in seen_names and len(name) > 1:
                seen_names.add(name_key)
                unique_attractions.append(attraction)
            else:
                logger.debug(f"重复景点: {attraction['name']} ({attraction['region']})")
        
        return unique_attractions
    
    def crawl_all_regions(self) -> List[Dict]:
        """爬取所有地区的真实景点数据"""
        logger.info("🚀 开始爬取真实西藏景点数据...")
        
        all_attractions = []
        
        for region, config in self.target_regions.items():
            logger.info(f"\n{'='*60}")
            logger.info(f"开始爬取 {region} 地区景点")
            logger.info(f"目标数量: {config['target']}")
            logger.info(f"关键词: {config['keywords']}")
            logger.info(f"{'='*60}")
            
            region_attractions = []
            
            # 1. 爬取携程
            logger.info(f"1. 爬取携程 - {region}")
            ctrip_attractions = self.crawl_ctrip_region(region, config['keywords'])
            region_attractions.extend(ctrip_attractions)
            logger.info(f"携程获得: {len(ctrip_attractions)} 个景点")
            
            # 2. 爬取马蜂窝
            logger.info(f"2. 爬取马蜂窝 - {region}")
            mafengwo_attractions = self.crawl_mafengwo_region(region, config['keywords'])
            region_attractions.extend(mafengwo_attractions)
            logger.info(f"马蜂窝获得: {len(mafengwo_attractions)} 个景点")
            
            # 3. 爬取去哪儿
            logger.info(f"3. 爬取去哪儿 - {region}")
            qunar_attractions = self.crawl_qunar_region(region, config['keywords'])
            region_attractions.extend(qunar_attractions)
            logger.info(f"去哪儿获得: {len(qunar_attractions)} 个景点")
            
            # 去重
            unique_attractions = self.deduplicate_attractions(region_attractions)
            logger.info(f"{region} 去重后: {len(unique_attractions)} 个景点")
            
            all_attractions.extend(unique_attractions)
            
            # 保存中间结果
            self.save_intermediate_results(region, unique_attractions)
            
            # 休息避免被封
            logger.info(f"{region} 完成，休息10秒...")
            time.sleep(10)
        
        return all_attractions
    
    def save_intermediate_results(self, region: str, attractions: List[Dict]):
        """保存中间结果"""
        try:
            filename = f"crawled_{region}_attractions.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(attractions, f, ensure_ascii=False, indent=2)
            logger.info(f"中间结果已保存: {filename}")
        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")
    
    def save_final_results(self, attractions: List[Dict]):
        """保存最终爬取结果"""
        try:
            # 统计信息
            stats = {}
            source_stats = {}
            
            for attraction in attractions:
                region = attraction["region"]
                source = attraction["source"]
                
                stats[region] = stats.get(region, 0) + 1
                source_stats[source] = source_stats.get(source, 0) + 1
            
            # 构建结果数据
            result = {
                "metadata": {
                    "total_attractions": len(attractions),
                    "regions": len(stats),
                    "sources": list(source_stats.keys()),
                    "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "description": "真实爬取的西藏景点数据"
                },
                "regional_distribution": stats,
                "source_distribution": source_stats,
                "attractions": attractions
            }
            
            # 保存文件
            filename = "real_tibet_attractions_crawled.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 真实爬取数据已保存: {filename}")
            logger.info(f"📊 总景点数: {len(attractions)}")
            logger.info(f"📊 地区分布: {stats}")
            logger.info(f"📊 数据源分布: {source_stats}")
            
            return result
            
        except Exception as e:
            logger.error(f"保存最终结果失败: {e}")
            return None

def main():
    """主函数"""
    crawler = RealTibetCrawler()
    
    # 爬取所有地区数据
    all_attractions = crawler.crawl_all_regions()
    
    # 保存最终结果
    result = crawler.save_final_results(all_attractions)
    
    if result:
        logger.info("🎉 真实西藏景点数据爬取完成！")
        logger.info(f"📁 文件: real_tibet_attractions_crawled.json")
        logger.info(f"📊 总计: {len(all_attractions)} 个真实景点")
    else:
        logger.error("❌ 爬取失败")

if __name__ == "__main__":
    main()
