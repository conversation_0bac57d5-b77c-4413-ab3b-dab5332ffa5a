# 同步批量处理实验完整数据报告（以秒为单位）

## 📊 实验基本信息
- 实验日期: 2025年7月23日
- 实验类型: 同步批量处理
- 测试批次: 6个
- 数据库: Neo4j
- 处理方式: 串行处理（包含节点创建和关系生成）

## 📈 完整实验数据表

| 批次序号 | 批次大小 | 开始时间      | 结束时间      | 处理时间(秒) | 处理速度(节点/秒) | 每节点时间(秒) | 成功数 | 失败数 | 成功率 |
|---------|---------|--------------|--------------|-------------|------------------|---------------|--------|--------|--------|
| 1       | 10      | 19:39:58     | 19:41:56     | **117.98**  | 0.085            | 11.80         | 10     | 0      | 100%   |
| 2       | 50      | 19:41:58     | 19:51:14     | **555.91**  | 0.090            | 11.12         | 50     | 0      | 100%   |
| 3       | 100     | 19:51:16     | 20:11:24     | **1207.37** | 0.083            | 12.07         | 100    | 0      | 100%   |
| 4       | 150     | 20:11:26     | 20:53:56     | **1568.88** | 0.096            | 10.46         | 150    | 0      | 100%   |
| 5       | 200     | 20:53:58     | 21:25:47     | **1909.31** | 0.105            | 9.55          | 200    | 0      | 100%   |
| 6       | 250     | 22:24:39     | 23:00:47     | **2167.16** | 0.115            | 8.67          | 250    | 0      | 100%   |

## 🔢 详细数值数据（秒）

### 处理时间数据
- 10个节点:  117.98秒
- 50个节点:  555.91秒
- 100个节点: 1207.37秒
- 150个节点: 1568.88秒
- 200个节点: 1909.31秒
- 250个节点: 2167.16秒

### 处理速度数据（节点/秒）
- 10个节点:  0.085
- 50个节点:  0.090
- 100个节点: 0.083
- 150个节点: 0.096
- 200个节点: 0.105
- 250个节点: 0.115

### 每节点平均处理时间（秒）
- 10个节点:  11.80秒/节点
- 50个节点:  11.12秒/节点
- 100个节点: 12.07秒/节点
- 150个节点: 10.46秒/节点
- 200个节点: 9.55秒/节点
- 250个节点: 8.67秒/节点

## 📊 统计汇总

### 总体统计
- **总处理节点数**: 760个
- **总处理时间**: 7,526.61秒 (125.44分钟 / 2.09小时)
- **平均处理速度**: 0.096节点/秒
- **平均每节点时间**: 10.61秒
- **整体成功率**: 100%

### 时间分布
- 最短处理时间: 117.98秒 (10个节点)
- 最长处理时间: 2167.16秒 (250个节点)
- 时间范围: 2049.18秒
- 时间增长倍数: 18.37倍 (250节点相比10节点)

### 速度分布
- 最高处理速度: 0.115节点/秒 (250个节点)
- 最低处理速度: 0.083节点/秒 (100个节点)
- 速度范围: 0.032节点/秒
- 速度变异系数: 12.9%

### 效率分析
- 最高效率批次: 250个节点 (8.67秒/节点)
- 最低效率批次: 100个节点 (12.07秒/节点)
- 效率改进: 28.2% (250节点相比100节点)

## 📈 增长趋势分析

### 时间增长率（相对前一批次）
- 50个节点: +371.2% (+437.93秒)
- 100个节点: +117.2% (+651.46秒)
- 150个节点: +29.9% (+361.51秒)
- 200个节点: +21.7% (+340.43秒)
- 250个节点: +13.5% (+257.85秒)

### 速度变化率（相对前一批次）
- 50个节点: +5.9%
- 100个节点: -7.8%
- 150个节点: +15.7%
- 200个节点: +9.4%
- 250个节点: +9.5%

## 🎯 性能特征

### 线性扩展性
- 时间与批次大小相关性: 0.986 (接近完美线性)
- 扩展性评级: 优秀

### 处理稳定性
- 成功率标准差: 0% (完全稳定)
- 速度标准差: 0.012节点/秒
- 稳定性评级: 优秀

### 批处理效率
- 大批次优势: 轻微 (250节点批次最快)
- 批处理优化: 有限
- 瓶颈: 串行处理限制

## 🔍 关键发现

1. **线性增长**: 处理时间与节点数量呈现良好的线性关系
2. **速度稳定**: 处理速度在0.083-0.115节点/秒范围内波动
3. **高成功率**: 所有批次均达到100%成功率
4. **效率提升**: 大批次处理略有效率优势
5. **主要瓶颈**: LLM API调用和JSON解析占用大部分时间

## 📋 原始数据（CSV格式）

批次大小,处理时间秒,处理速度,每节点时间秒,成功数,失败数,成功率
10,117.98,0.085,11.80,10,0,100%
50,555.91,0.090,11.12,50,0,100%
100,1207.37,0.083,12.07,100,0,100%
150,1568.88,0.096,10.46,150,0,100%
200,1909.31,0.105,9.55,200,0,100%
250,2167.16,0.115,8.67,250,0,100%

## 🚀 异步对比基准

以上数据作为异步处理的对比基准，预期异步版本改进：
- 处理时间减少: 50-80% (2-5倍提升)
- 处理速度提升: 100-400% (2-5倍提升)
- 并发处理能力: 支持多节点同时处理
- 资源利用率: 显著提升

---
数据生成时间: 2025-07-23 23:30
实验状态: 已完成
数据完整性: 100%
