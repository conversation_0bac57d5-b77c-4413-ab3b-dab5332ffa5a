#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展同步批量处理测试脚本 - 500个景点数据集
按照指定的分组大小（10, 50, 100, 150, 200, 250, 300, 400, 500）分批处理景点数据
记录每组的处理时间，用于与异步代码进行性能对比
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

# 导入项目模块
from config import Config
from neo4j_connection import Neo4jConnection
from text_processor import process_json_chunk, reset_database, close_resources

# 设置日志
PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE = os.path.join(PROJECT_DIR, 'sync_500_batch_test.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE, encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class Sync500BatchTester:
    """500个景点同步批量测试器"""
    
    def __init__(self):
        self.neo4j_conn = None
        self.results = []
        # 先测试较小的批次，确认系统稳定性
        self.batch_sizes = [300, 400, 500]  # 从300开始，扩展之前的测试
        
    def setup_neo4j_connection(self):
        """建立Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info(f"成功连接到 Neo4j: {neo4j_config['uri']}")
            return True
        except Exception as e:
            logger.error(f"Neo4j连接失败: {e}")
            return False
    
    def load_json_data(self, json_file_path: str) -> List[Dict]:
        """加载500个景点的JSON数据"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            nodes = data.get("nodes", [])
            if not nodes:
                logger.warning("JSON文件中无节点数据")
                return []
            
            # 去重处理
            seen_names = set()
            unique_nodes = []
            for node in nodes:
                if node["name"] not in seen_names:
                    seen_names.add(node["name"])
                    unique_nodes.append(node)
            
            logger.info(f"加载500景点数据完成，总节点数: {len(nodes)}, 去重后: {len(unique_nodes)}")
            return unique_nodes
            
        except Exception as e:
            logger.error(f"加载JSON数据失败: {e}")
            return []
    
    def process_batch_sync(self, nodes: List[Dict], batch_size: int, 
                          crawl_timestamp: str, source_type: str, metrics: Dict) -> Dict:
        """同步处理指定大小的批次"""
        logger.info(f"开始处理批次，大小: {batch_size}")
        
        # 清空数据库
        self.neo4j_conn.clear_database()
        reset_database(self.neo4j_conn)
        
        # 取指定数量的节点
        batch_nodes = nodes[:batch_size]
        
        start_time = time.perf_counter()
        start_datetime = datetime.now()
        
        try:
            # 分小批次处理（每次3个节点）
            small_batch_size = 3
            results = []
            processed_count = 0
            failed_count = 0
            
            for i in range(0, len(batch_nodes), small_batch_size):
                small_batch = batch_nodes[i:i + small_batch_size]
                
                batch_result = process_json_chunk(
                    neo4j_conn=self.neo4j_conn,
                    data=small_batch,
                    crawl_timestamp=crawl_timestamp,
                    source_type=source_type,
                    metrics=metrics
                )
                
                results.extend(batch_result)
                
                # 统计结果
                for result in batch_result:
                    if result["status"] == "success":
                        processed_count += 1
                    else:
                        failed_count += 1
                
                # 每10个节点记录一次进度
                if (i + small_batch_size) % 10 == 0 or (i + small_batch_size) >= len(batch_nodes):
                    current_processed = min(i + small_batch_size, len(batch_nodes))
                    elapsed = time.perf_counter() - start_time
                    speed = current_processed / elapsed if elapsed > 0 else 0
                    logger.info(f"已处理 {current_processed}/{len(batch_nodes)} 个节点, "
                               f"耗时: {elapsed:.1f}秒, 速度: {speed:.3f}节点/秒")
            
            end_time = time.perf_counter()
            end_datetime = datetime.now()
            duration = end_time - start_time
            
            result = {
                "batch_size": batch_size,
                "actual_processed": len(batch_nodes),
                "success_count": processed_count,
                "failed_count": failed_count,
                "start_time": start_datetime.isoformat(),
                "end_time": end_datetime.isoformat(),
                "duration_seconds": duration,
                "nodes_per_second": len(batch_nodes) / duration if duration > 0 else 0,
                "status": "success",
                "dataset": "merged_500_nodes"
            }
            
            logger.info(f"批次处理完成 - 大小: {batch_size}, 耗时: {duration:.2f}秒, "
                       f"成功: {processed_count}, 失败: {failed_count}, "
                       f"速度: {result['nodes_per_second']:.3f}节点/秒")
            
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            logger.error(f"批次处理失败: {e}")
            return {
                "batch_size": batch_size,
                "actual_processed": 0,
                "success_count": 0,
                "failed_count": batch_size,
                "start_time": start_datetime.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": duration,
                "nodes_per_second": 0,
                "status": "failed",
                "error": str(e),
                "dataset": "merged_500_nodes"
            }
    
    def run_all_batches(self, json_file_path: str):
        """运行所有批次的测试"""
        logger.info("开始500个景点的同步批量处理测试")
        
        # 加载数据
        nodes = self.load_json_data(json_file_path)
        if not nodes:
            logger.error("无法加载数据，测试终止")
            return
        
        # 检查数据量是否足够
        max_batch_size = max(self.batch_sizes)
        if len(nodes) < max_batch_size:
            logger.warning(f"数据量不足，最大批次大小: {max_batch_size}, 实际数据量: {len(nodes)}")
            # 调整批次大小
            self.batch_sizes = [size for size in self.batch_sizes if size <= len(nodes)]
            logger.info(f"调整后的批次大小: {self.batch_sizes}")
        
        # 设置处理参数
        crawl_timestamp = "2025-07-23T21:00:00.000000"
        source_type = "sync_500_test"
        metrics = {"ratings": 4.0}
        
        # 逐个处理批次
        for i, batch_size in enumerate(self.batch_sizes):
            logger.info(f"\n{'='*60}")
            logger.info(f"开始处理批次 {i+1}/{len(self.batch_sizes)}: {batch_size}个节点")
            logger.info(f"预计API调用次数: {batch_size * (batch_size - 1) // 2}")
            logger.info(f"{'='*60}")
            
            result = self.process_batch_sync(
                nodes=nodes,
                batch_size=batch_size,
                crawl_timestamp=crawl_timestamp,
                source_type=source_type,
                metrics=metrics
            )
            
            self.results.append(result)
            
            # 保存中间结果
            self.save_intermediate_results()
            
            # 短暂休息，避免系统负载过高
            if i < len(self.batch_sizes) - 1:  # 不是最后一个批次
                logger.info("休息2秒...")
                time.sleep(2)
        
        # 保存最终结果
        self.save_results()
        self.print_summary()
    
    def save_intermediate_results(self):
        """保存中间结果"""
        try:
            results_file = os.path.join(PROJECT_DIR, 'sync_500_batch_results_intermediate.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"中间结果已保存: {len(self.results)} 个批次")
        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")
    
    def save_results(self):
        """保存测试结果到文件"""
        try:
            # 保存为JSON格式
            results_file = os.path.join(PROJECT_DIR, 'sync_500_batch_results.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {results_file}")
            
            # 保存为CSV格式（便于分析）
            csv_file = os.path.join(PROJECT_DIR, 'sync_500_batch_results.csv')
            df = pd.DataFrame(self.results)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logger.info(f"结果已保存到: {csv_file}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        logger.info("\n" + "="*80)
        logger.info("500个景点同步批量处理测试结果摘要")
        logger.info("="*80)
        
        total_time = 0
        total_nodes = 0
        
        for result in self.results:
            if result["status"] == "success":
                logger.info(f"批次大小: {result['batch_size']:>3} | "
                           f"耗时: {result['duration_seconds']:>8.0f}秒 ({result['duration_seconds']/60:>5.1f}分钟) | "
                           f"速度: {result['nodes_per_second']:>6.3f} 节点/秒 | "
                           f"成功: {result['success_count']:>3} | "
                           f"失败: {result['failed_count']:>3}")
                total_time += result['duration_seconds']
                total_nodes += result['success_count']
            else:
                logger.info(f"批次大小: {result['batch_size']:>3} | 状态: 失败 | "
                           f"错误: {result.get('error', '未知错误')}")
        
        logger.info("="*80)
        logger.info(f"总处理节点数: {total_nodes}")
        logger.info(f"总处理时间: {total_time:.0f}秒 ({total_time/60:.1f}分钟 / {total_time/3600:.1f}小时)")
        if total_time > 0:
            logger.info(f"平均处理速度: {total_nodes/total_time:.3f}节点/秒")
        logger.info("="*80)
    
    def cleanup(self):
        """清理资源"""
        if self.neo4j_conn:
            self.neo4j_conn.close()
        close_resources()

def main():
    """主函数"""
    tester = Sync500BatchTester()
    
    try:
        # 建立连接
        if not tester.setup_neo4j_connection():
            logger.error("无法建立Neo4j连接，测试终止")
            return
        
        # 设置JSON文件路径
        json_file_path = os.path.join(PROJECT_DIR, "data", "merged_500_knowledge_graph.json")
        if not os.path.exists(json_file_path):
            logger.error(f"JSON文件不存在: {json_file_path}")
            return
        
        # 运行测试
        tester.run_all_batches(json_file_path)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
