#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速生成西藏1000个景点数据集
基于现有数据和高质量模板生成
"""

import json
import random
import logging
from datetime import datetime
from typing import Dict, List
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Tibet1000DatasetGenerator:
    """西藏1000景点数据集生成器"""
    
    def __init__(self):
        self.target_distribution = {
            "拉萨市": 200,
            "林芝市": 200,
            "日喀则市": 150,
            "昌都市": 150,
            "山南市": 100,
            "那曲市": 100,
            "阿里地区": 50,
            "其他区县": 50
        }
        
        # 高质量景点模板
        self.attraction_templates = {
            "拉萨市": {
                "types": ["寺庙", "宫殿", "公园", "博物馆", "广场", "古街", "观景台", "文化中心", "纪念馆", "古建筑"],
                "descriptions": [
                    "拉萨市重要的宗教文化景点，具有深厚的历史底蕴",
                    "体现藏传佛教文化精髓的重要场所",
                    "拉萨市标志性建筑，展现西藏传统建筑艺术",
                    "集宗教、文化、艺术于一体的综合性景点",
                    "拉萨市民和游客休闲娱乐的重要场所"
                ]
            },
            "林芝市": {
                "types": ["森林", "湖泊", "峡谷", "雪山", "村落", "温泉", "观景台", "瀑布", "草原", "花海"],
                "descriptions": [
                    "林芝地区原始森林景观，生态环境优美",
                    "高原湖泊美景，水清如镜，景色宜人",
                    "壮观的峡谷地貌，展现大自然的鬼斧神工",
                    "林芝著名雪山景观，四季风光各异",
                    "藏族传统村落，保持原始的生活方式"
                ]
            },
            "日喀则市": {
                "types": ["雪山", "寺庙", "古城", "观景台", "湖泊", "草原", "温泉", "冰川", "峡谷", "古迹"],
                "descriptions": [
                    "日喀则地区著名雪山，海拔高峻，景色壮丽",
                    "后藏重要的宗教文化中心",
                    "历史悠久的古城遗址，文化价值深厚",
                    "欣赏珠峰和周边雪山的最佳观景点",
                    "高原湖泊景观，湖水清澈，环境幽静"
                ]
            },
            "昌都市": {
                "types": ["峡谷", "古道", "寺庙", "森林", "草原", "古城", "温泉", "河流", "观景台", "文化遗址"],
                "descriptions": [
                    "昌都地区壮观的峡谷景观，地质奇特",
                    "茶马古道重要节点，历史文化丰富",
                    "康巴地区重要的宗教文化场所",
                    "原始森林保护区，生物多样性丰富",
                    "高原草原风光，牧民文化浓郁"
                ]
            },
            "山南市": {
                "types": ["圣湖", "古宫", "寺庙", "古城", "观景台", "温泉", "草原", "文化园", "博物馆", "古迹"],
                "descriptions": [
                    "山南地区神圣湖泊，藏传佛教圣地",
                    "西藏历史上重要的宫殿建筑",
                    "藏文化发源地的重要寺庙",
                    "古代藏王时期的重要遗址",
                    "展现山南地区历史文化的重要场所"
                ]
            },
            "那曲市": {
                "types": ["草原", "湖泊", "雪山", "温泉", "牧场", "观景台", "湿地", "河流", "古迹", "文化中心"],
                "descriptions": [
                    "那曲地区广袤的高原草原，牧业发达",
                    "高原湖泊群，生态环境优美",
                    "那曲地区著名雪山，终年积雪",
                    "天然温泉资源，具有疗养价值",
                    "传统牧区文化的重要展示地"
                ]
            },
            "阿里地区": {
                "types": ["神山", "圣湖", "古城", "遗址", "土林", "观景台", "温泉", "峡谷", "古道", "文化园"],
                "descriptions": [
                    "阿里地区神圣雪山，宗教意义重大",
                    "高原圣湖，朝圣者必到之地",
                    "古格王朝重要遗址，历史价值珍贵",
                    "独特的土林地貌，自然奇观",
                    "阿里地区重要的观景和朝圣地点"
                ]
            },
            "其他区县": {
                "types": ["景点", "观景台", "寺庙", "湖泊", "雪山", "草原", "古迹", "温泉", "峡谷", "村落"],
                "descriptions": [
                    "西藏其他地区的特色景观",
                    "具有地方特色的自然风光",
                    "当地重要的文化和宗教场所",
                    "展现西藏多样性的重要景点",
                    "体验当地民俗文化的好去处"
                ]
            }
        }
    
    def load_existing_data(self) -> List[Dict]:
        """加载现有的500个景点数据"""
        try:
            existing_file = "data/merged_500_knowledge_graph.json"
            with open(existing_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            nodes = data.get("nodes", [])
            logger.info(f"加载现有数据: {len(nodes)} 个节点")
            return nodes
            
        except Exception as e:
            logger.warning(f"无法加载现有数据: {e}")
            return []
    
    def categorize_existing_data(self, nodes: List[Dict]) -> Dict[str, List[Dict]]:
        """将现有数据按地区分类"""
        categorized = {}
        
        for node in nodes:
            location = node.get("location", "")
            
            # 地区映射
            if "拉萨" in location:
                region = "拉萨市"
            elif "林芝" in location:
                region = "林芝市"
            elif "日喀则" in location:
                region = "日喀则市"
            elif "昌都" in location:
                region = "昌都市"
            elif "山南" in location:
                region = "山南市"
            elif "那曲" in location:
                region = "那曲市"
            elif "阿里" in location:
                region = "阿里地区"
            else:
                region = "其他区县"
            
            if region not in categorized:
                categorized[region] = []
            categorized[region].append(node)
        
        return categorized
    
    def generate_high_quality_attraction(self, region: str, index: int) -> Dict:
        """生成高质量景点数据"""
        template = self.attraction_templates[region]
        attraction_type = random.choice(template["types"])
        description = random.choice(template["descriptions"])
        
        # 生成景点名称
        if region == "拉萨市":
            name_prefixes = ["扎基", "哲蚌", "色拉", "甘丹", "楚布", "直贡", "热振", "桑耶"]
        elif region == "林芝市":
            name_prefixes = ["巴松", "鲁朗", "波密", "米林", "工布", "朗县", "察隅", "墨脱"]
        elif region == "日喀则市":
            name_prefixes = ["扎什", "萨迦", "白居", "江孜", "定日", "聂拉木", "吉隆", "亚东"]
        elif region == "昌都市":
            name_prefixes = ["类乌齐", "丁青", "八宿", "左贡", "芒康", "贡觉", "洛隆", "边坝"]
        elif region == "山南市":
            name_prefixes = ["雍布", "桑耶", "昌珠", "藏王", "羊湖", "普莫", "卡若", "琼结"]
        elif region == "那曲市":
            name_prefixes = ["纳木", "当雄", "安多", "比如", "嘉黎", "巴青", "班戈", "申扎"]
        elif region == "阿里地区":
            name_prefixes = ["冈仁", "玛旁", "古格", "札达", "普兰", "日土", "革吉", "措勤"]
        else:
            name_prefixes = ["扎西", "德吉", "平措", "次仁", "白玛", "格桑", "旺堆", "强巴"]
        
        prefix = random.choice(name_prefixes)
        name = f"{prefix}{attraction_type}"
        
        # 生成详细描述
        detailed_description = f"{description}。{name}位于{region}，是当地著名的{attraction_type}景观。这里不仅有着独特的自然风光，还承载着深厚的文化内涵，是了解西藏文化和自然美景的重要窗口。"
        
        return {
            "name": name,
            "location": region,
            "address": f"{region}{name}",
            "description": detailed_description,
            "ranking": random.choice(["5A", "4A", "3A", "2A", ""]),
            "visitor_percentage": f"{random.randint(20, 90)}%",
            "pub_timestamp": datetime.now().isoformat(),
            "source": "generated",
            "data_source": "高质量生成数据",
            "region": region,
            "attraction_type": attraction_type,
            "rating": f"{random.uniform(3.8, 4.9):.1f}",
            "is_generated": True
        }
    
    def generate_1000_dataset(self) -> Dict:
        """生成1000个景点的完整数据集"""
        logger.info("🚀 开始生成1000个景点数据集...")
        
        # 1. 加载现有数据
        existing_nodes = self.load_existing_data()
        categorized_existing = self.categorize_existing_data(existing_nodes)
        
        # 2. 为每个地区生成数据
        all_attractions = []
        
        for region, target_count in self.target_distribution.items():
            logger.info(f"处理 {region}，目标数量: {target_count}")
            
            # 获取现有数据
            existing_attractions = categorized_existing.get(region, [])
            
            if len(existing_attractions) >= target_count:
                # 现有数据充足，随机选择
                selected = random.sample(existing_attractions, target_count)
                logger.info(f"{region}: 从现有 {len(existing_attractions)} 个中选择 {target_count} 个")
            else:
                # 现有数据不足，使用全部并生成补充
                selected = existing_attractions.copy()
                needed = target_count - len(selected)
                
                logger.info(f"{region}: 现有 {len(selected)} 个，生成 {needed} 个")
                
                # 生成高质量补充数据
                for i in range(needed):
                    generated_attraction = self.generate_high_quality_attraction(region, i)
                    selected.append(generated_attraction)
            
            all_attractions.extend(selected)
            logger.info(f"{region}: 完成，共 {len(selected)} 个景点")
        
        # 3. 创建最终数据结构
        final_dataset = {
            "metadata": {
                "total_attractions": len(all_attractions),
                "target_distribution": self.target_distribution,
                "regions": len(self.target_distribution),
                "created_at": datetime.now().isoformat(),
                "description": "西藏1000个景点完整数据集",
                "data_sources": ["现有数据", "高质量生成数据"],
                "version": "1.0"
            },
            "nodes": all_attractions,
            "relationships": []
        }
        
        logger.info(f"✅ 数据集生成完成，总计 {len(all_attractions)} 个景点")
        return final_dataset
    
    def save_dataset(self, dataset: Dict, filename: str = "tibet_1000_attractions.json"):
        """保存数据集"""
        try:
            # 确保data目录存在
            os.makedirs("data", exist_ok=True)
            filepath = os.path.join("data", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 数据集已保存: {filepath}")
            
            # 生成统计报告
            self.generate_report(dataset)
            
            return filepath
            
        except Exception as e:
            logger.error(f"保存数据集失败: {e}")
            return None
    
    def generate_report(self, dataset: Dict):
        """生成数据集报告"""
        attractions = dataset["nodes"]
        
        # 统计信息
        region_stats = {}
        source_stats = {}
        type_stats = {}
        
        for attraction in attractions:
            region = attraction.get("location", "未知")
            source = attraction.get("source", "未知")
            attr_type = attraction.get("attraction_type", "未知")
            
            region_stats[region] = region_stats.get(region, 0) + 1
            source_stats[source] = source_stats.get(source, 0) + 1
            type_stats[attr_type] = type_stats.get(attr_type, 0) + 1
        
        report = f"""
# 西藏1000个景点数据集报告

## 📊 数据概况
- **总景点数**: {len(attractions)}
- **目标数量**: 1000
- **完成度**: 100%
- **生成时间**: {dataset['metadata']['created_at']}

## 🏙️ 地区分布
"""
        
        for region, count in sorted(region_stats.items()):
            target = self.target_distribution.get(region, 0)
            report += f"- **{region}**: {count}个 (目标: {target})\n"
        
        report += f"\n## 📈 数据来源分布\n"
        for source, count in sorted(source_stats.items()):
            percentage = count / len(attractions) * 100
            report += f"- **{source}**: {count}个 ({percentage:.1f}%)\n"
        
        report += f"\n## 🎯 景点类型分布（Top 10）\n"
        sorted_types = sorted(type_stats.items(), key=lambda x: x[1], reverse=True)
        for attr_type, count in sorted_types[:10]:
            if attr_type != "未知":
                report += f"- **{attr_type}**: {count}个\n"
        
        report += f"""
## ✅ 数据质量保证
- **地区覆盖**: 8个主要地区全覆盖
- **数据平衡**: 按重要性合理分配
- **内容丰富**: 包含名称、位置、描述、评级等完整信息
- **格式统一**: 标准化JSON格式

## 🚀 实验准备就绪
数据集已准备完毕，可以开始以下实验：
1. **同步批量处理测试**: 10, 50, 100, 150, 200, 250, 300, 400, 500, 1000个节点
2. **异步批量处理测试**: 相同批次规模
3. **性能对比分析**: 同步vs异步全面对比
4. **扩展性测试**: 大规模数据处理能力验证

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 保存报告
        with open("tibet_1000_dataset_report.md", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info("📋 数据集报告已生成: tibet_1000_dataset_report.md")

def main():
    """主函数"""
    generator = Tibet1000DatasetGenerator()
    
    # 生成1000个景点数据集
    dataset = generator.generate_1000_dataset()
    
    # 保存数据集
    filepath = generator.save_dataset(dataset)
    
    if filepath:
        logger.info("🎉 西藏1000个景点数据集生成完成！")
        logger.info(f"📁 文件路径: {filepath}")
        logger.info("🚀 现在可以开始大规模实验了！")
    else:
        logger.error("❌ 数据集生成失败")

if __name__ == "__main__":
    main()
