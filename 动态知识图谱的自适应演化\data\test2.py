import json
from pathlib import Path

file_path = r"C:\Users\<USER>\Documents\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-06\动态知识图谱的自适应演化\data\lhasa_knowledge_graph.json"
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
nodes = data.get("nodes", [])
print(f"Total nodes: {len(nodes)}")
for node in nodes[:5]:
    print(f"Name: {node.get('name')}, Location: {node.get('location')}, Description: {node.get('description')}")