2025-08-01 21:41:33,375 - INFO - __main__ - 用户指定批次: [10, 50, 100]
2025-08-01 21:41:33,376 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 21:41:33,409 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 21:41:33,409 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 21:41:33,410 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 21:41:33,410 - INFO - __main__ - 测试批次: [10, 50, 100]
2025-08-01 21:41:33,419 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 21:41:33,420 - INFO - __main__ - 数据集整体组成:
2025-08-01 21:41:33,420 - INFO - __main__ -   总数: 1000
2025-08-01 21:41:33,420 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 21:41:33,420 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 21:41:33,420 - INFO - __main__ - 
====================================================================================================
2025-08-01 21:41:33,421 - INFO - __main__ - 开始处理真实数据批次 1/3: 10个节点
2025-08-01 21:41:33,421 - INFO - __main__ - 预估API调用: 45 次
2025-08-01 21:41:33,421 - INFO - __main__ - 预估时间: 0.00 小时
2025-08-01 21:41:33,422 - INFO - __main__ - ====================================================================================================
2025-08-01 21:41:33,422 - INFO - __main__ - 开始处理真实数据批次，大小: 10
2025-08-01 21:41:33,422 - INFO - __main__ - 预估API调用次数: 45
2025-08-01 21:41:33,423 - INFO - __main__ - 预估处理时间: 0.00小时
2025-08-01 21:41:33,720 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:41:33,725 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:41:33,725 - INFO - text_processor - 清空所有节点和关系
2025-08-01 21:41:33,824 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 21:41:33,838 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 21:41:33,839 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 21:41:33,839 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 21:41:33,839 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:41:33,839 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:41:33,840 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:41:33,840 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:41:41,940 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 21:41:42,113 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 21:41:42,113 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:41:51,393 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:41:51,454 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:05,387 - INFO - __main__ - 用户指定批次: [10]
2025-08-01 21:42:05,390 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 21:42:05,407 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 21:42:05,408 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 21:42:05,408 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 21:42:05,409 - INFO - __main__ - 测试批次: [10]
2025-08-01 21:42:05,417 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 21:42:05,417 - INFO - __main__ - 数据集整体组成:
2025-08-01 21:42:05,418 - INFO - __main__ -   总数: 1000
2025-08-01 21:42:05,418 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 21:42:05,418 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 21:42:05,418 - INFO - __main__ - 
====================================================================================================
2025-08-01 21:42:05,419 - INFO - __main__ - 开始处理真实数据批次 1/1: 10个节点
2025-08-01 21:42:05,419 - INFO - __main__ - 预估API调用: 45 次
2025-08-01 21:42:05,419 - INFO - __main__ - 预估时间: 0.00 小时
2025-08-01 21:42:05,419 - INFO - __main__ - ====================================================================================================
2025-08-01 21:42:05,420 - INFO - __main__ - 开始处理真实数据批次，大小: 10
2025-08-01 21:42:05,420 - INFO - __main__ - 预估API调用次数: 45
2025-08-01 21:42:05,420 - INFO - __main__ - 预估处理时间: 0.00小时
2025-08-01 21:42:05,432 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:42:05,438 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:42:05,438 - INFO - text_processor - 清空所有节点和关系
2025-08-01 21:42:05,444 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 21:42:05,453 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 21:42:05,453 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 21:42:05,454 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 21:42:05,454 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:05,454 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:05,455 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:05,455 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:11,572 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 21:42:11,658 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 21:42:11,659 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:20,064 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:20,132 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:27,494 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-08-01 21:42:27,531 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-08-01 21:42:27,531 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 21:42:27,531 - INFO - __main__ - 进度: 3/10 (30.0%) | 小批次: 1/4 | 已耗时: 0.4分钟 | 预计剩余: 0.9分钟 | 速度: 0.136节点/秒
2025-08-01 21:42:27,531 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:27,532 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:27,532 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:27,532 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:41,849 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-08-01 21:42:41,861 - INFO - text_processor - 成功处理实体: 圣地香都
2025-08-01 21:42:41,862 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:47,288 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-08-01 21:42:47,300 - INFO - text_processor - 成功处理实体: 曲苏
2025-08-01 21:42:47,301 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:53,470 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-08-01 21:42:53,484 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-08-01 21:42:53,484 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:42:53,484 - INFO - __main__ - 进度: 6/10 (60.0%) | 小批次: 2/4 | 已耗时: 0.8分钟 | 预计剩余: 0.5分钟 | 速度: 0.125节点/秒
2025-08-01 21:42:53,484 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:53,484 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:53,484 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:53,485 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:00,300 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-08-01 21:43:00,312 - INFO - text_processor - 成功处理实体: 喜德林
2025-08-01 21:43:00,312 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:07,478 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-08-01 21:43:07,489 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-08-01 21:43:07,490 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:12,022 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-08-01 21:43:12,036 - INFO - text_processor - 成功处理实体: 水上乐园
2025-08-01 21:43:12,036 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:43:12,036 - INFO - __main__ - 进度: 9/10 (90.0%) | 小批次: 3/4 | 已耗时: 1.1分钟 | 预计剩余: 0.1分钟 | 速度: 0.135节点/秒
2025-08-01 21:43:12,036 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:43:12,037 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:43:12,037 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:43:12,037 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:21,338 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-08-01 21:43:21,398 - INFO - text_processor - 成功处理实体: 阿热康参
2025-08-01 21:43:21,399 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:43:21,399 - INFO - __main__ - 进度: 10/10 (100.0%) | 小批次: 4/4 | 已耗时: 1.3分钟 | 预计剩余: 0.0分钟 | 速度: 0.132节点/秒
2025-08-01 21:43:21,400 - INFO - __main__ - 真实数据批次处理完成 - 大小: 10
2025-08-01 21:43:21,400 - INFO - __main__ - 耗时: 0.02小时 (1.3分钟)
2025-08-01 21:43:21,400 - INFO - __main__ - 成功: 10, 失败: 0
2025-08-01 21:43:21,400 - INFO - __main__ - 速度: 0.132节点/秒
2025-08-01 21:43:21,400 - INFO - __main__ - 真实数据比例: 100.0%
2025-08-01 21:43:21,403 - INFO - __main__ - 中间结果已保存: 1 个批次
2025-08-01 21:43:21,404 - INFO - __main__ - 最终结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.json
2025-08-01 21:43:21,421 - INFO - __main__ - CSV结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.csv
2025-08-01 21:43:21,421 - INFO - __main__ - 
========================================================================================================================
2025-08-01 21:43:21,422 - INFO - __main__ - 1000个真实西藏景点同步批量处理测试结果摘要
2025-08-01 21:43:21,422 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,422 - INFO - __main__ - 批次:   10 | 时间:   0.02小时 | 速度:  0.132节点/秒 | 真实数据: 100.0% | 成功:   10
2025-08-01 21:43:21,422 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,423 - INFO - __main__ - 总处理节点数: 10
2025-08-01 21:43:21,423 - INFO - __main__ - 总处理时间: 0.02小时
2025-08-01 21:43:21,423 - INFO - __main__ - 平均处理速度: 0.132节点/秒
2025-08-01 21:43:21,423 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,424 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-08-01 21:43:21,424 - INFO - text_processor - 关闭资源
2025-08-01 22:03:56,430 - INFO - __main__ - 用户指定批次: [10]
2025-08-01 22:03:56,431 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 22:03:59,077 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 22:03:59,077 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 22:03:59,078 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 22:03:59,078 - INFO - __main__ - 测试批次: [10]
2025-08-01 22:03:59,088 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 22:03:59,089 - INFO - __main__ - 数据集整体组成:
2025-08-01 22:03:59,089 - INFO - __main__ -   总数: 1000
2025-08-01 22:03:59,090 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 22:03:59,090 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 22:03:59,090 - INFO - __main__ - 
====================================================================================================
2025-08-01 22:03:59,090 - INFO - __main__ - 开始处理真实数据批次 1/1: 10个节点
2025-08-01 22:03:59,091 - INFO - __main__ - 预估API调用: 45 次
2025-08-01 22:03:59,092 - INFO - __main__ - 预估时间: 0.00 小时
2025-08-01 22:03:59,092 - INFO - __main__ - ====================================================================================================
2025-08-01 22:03:59,092 - INFO - __main__ - 开始处理真实数据批次，大小: 10
2025-08-01 22:03:59,093 - INFO - __main__ - 预估API调用次数: 45
2025-08-01 22:03:59,093 - INFO - __main__ - 预估处理时间: 0.00小时
2025-08-01 22:03:59,663 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:03:59,677 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:03:59,678 - INFO - text_processor - 清空所有节点和关系
2025-08-01 22:03:59,903 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 22:03:59,935 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 22:03:59,935 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 22:03:59,936 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 22:03:59,936 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:03:59,936 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:03:59,936 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:03:59,937 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:04:05,679 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 22:04:06,442 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 22:04:06,442 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:04:15,368 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:04:15,512 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:04:21,977 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-08-01 22:04:22,085 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-08-01 22:04:22,085 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:04:22,086 - INFO - __main__ - 进度: 3/10 (30.0%) | 小批次: 1/4 | 已耗时: 0.4分钟 | 预计剩余: 0.9分钟 | 速度: 0.135节点/秒
2025-08-01 22:04:22,086 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:04:22,086 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:04:22,086 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:04:22,086 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:04:36,168 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-08-01 22:04:36,185 - INFO - text_processor - 成功处理实体: 圣地香都
2025-08-01 22:04:36,186 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:04:49,315 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-08-01 22:04:49,397 - INFO - text_processor - 成功处理实体: 曲苏
2025-08-01 22:04:49,397 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:05:01,235 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-08-01 22:05:01,253 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-08-01 22:05:01,253 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:05:01,254 - INFO - __main__ - 进度: 6/10 (60.0%) | 小批次: 2/4 | 已耗时: 1.0分钟 | 预计剩余: 0.7分钟 | 速度: 0.098节点/秒
2025-08-01 22:05:01,254 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:05:01,254 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:05:01,254 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:05:01,254 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:05:13,639 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-08-01 22:05:13,656 - INFO - text_processor - 成功处理实体: 喜德林
2025-08-01 22:05:13,656 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:05:24,055 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-08-01 22:05:24,076 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-08-01 22:05:24,077 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:05:29,907 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-08-01 22:05:29,925 - INFO - text_processor - 成功处理实体: 水上乐园
2025-08-01 22:05:29,925 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:05:29,926 - INFO - __main__ - 进度: 9/10 (90.0%) | 小批次: 3/4 | 已耗时: 1.5分钟 | 预计剩余: 0.2分钟 | 速度: 0.100节点/秒
2025-08-01 22:05:29,926 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:05:29,926 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:05:29,926 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:05:29,927 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:05:39,705 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-08-01 22:05:39,721 - INFO - text_processor - 成功处理实体: 阿热康参
2025-08-01 22:05:39,721 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:05:39,722 - INFO - __main__ - 进度: 10/10 (100.0%) | 小批次: 4/4 | 已耗时: 1.7分钟 | 预计剩余: 0.0分钟 | 速度: 0.100节点/秒
2025-08-01 22:05:39,722 - INFO - __main__ - 真实数据批次处理完成 - 大小: 10
2025-08-01 22:05:39,722 - INFO - __main__ - 耗时: 0.03小时 (1.7分钟)
2025-08-01 22:05:39,722 - INFO - __main__ - 成功: 10, 失败: 0
2025-08-01 22:05:39,722 - INFO - __main__ - 速度: 0.100节点/秒
2025-08-01 22:05:39,722 - INFO - __main__ - 真实数据比例: 100.0%
2025-08-01 22:05:39,724 - INFO - __main__ - 中间结果已保存: 1 个批次
2025-08-01 22:05:39,725 - INFO - __main__ - 最终结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.json
2025-08-01 22:05:39,733 - INFO - __main__ - CSV结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.csv
2025-08-01 22:05:39,734 - INFO - __main__ - 
========================================================================================================================
2025-08-01 22:05:39,734 - INFO - __main__ - 1000个真实西藏景点同步批量处理测试结果摘要
2025-08-01 22:05:39,734 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:05:39,734 - INFO - __main__ - 批次:   10 | 时间:   0.03小时 | 速度:  0.100节点/秒 | 真实数据: 100.0% | 成功:   10
2025-08-01 22:05:39,734 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:05:39,734 - INFO - __main__ - 总处理节点数: 10
2025-08-01 22:05:39,735 - INFO - __main__ - 总处理时间: 0.03小时
2025-08-01 22:05:39,735 - INFO - __main__ - 平均处理速度: 0.100节点/秒
2025-08-01 22:05:39,735 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:05:39,735 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-08-01 22:05:39,736 - INFO - text_processor - 关闭资源
2025-08-01 22:06:05,699 - INFO - __main__ - 用户指定批次: [50]
2025-08-01 22:06:05,700 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 22:06:05,719 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 22:06:05,719 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 22:06:05,720 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 22:06:05,720 - INFO - __main__ - 测试批次: [50]
2025-08-01 22:06:05,728 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 22:06:05,728 - INFO - __main__ - 数据集整体组成:
2025-08-01 22:06:05,729 - INFO - __main__ -   总数: 1000
2025-08-01 22:06:05,729 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 22:06:05,729 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 22:06:05,729 - INFO - __main__ - 
====================================================================================================
2025-08-01 22:06:05,730 - INFO - __main__ - 开始处理真实数据批次 1/1: 50个节点
2025-08-01 22:06:05,730 - INFO - __main__ - 预估API调用: 1,225 次
2025-08-01 22:06:05,730 - INFO - __main__ - 预估时间: 0.05 小时
2025-08-01 22:06:05,731 - INFO - __main__ - ====================================================================================================
2025-08-01 22:06:05,731 - INFO - __main__ - 开始处理真实数据批次，大小: 50
2025-08-01 22:06:05,731 - INFO - __main__ - 预估API调用次数: 1,225
2025-08-01 22:06:05,732 - INFO - __main__ - 预估处理时间: 0.05小时
2025-08-01 22:06:05,748 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:06:05,754 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:06:05,754 - INFO - text_processor - 清空所有节点和关系
2025-08-01 22:06:05,760 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 22:06:05,765 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 22:06:05,766 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 22:06:05,766 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 22:06:05,766 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:06:05,767 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:06:05,767 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:06:05,767 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:06:25,041 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 22:06:25,241 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 22:06:25,242 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:06:34,137 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:06:34,209 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:06:37,529 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-08-01 22:06:37,646 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-08-01 22:06:37,646 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:06:37,647 - INFO - __main__ - 进度: 3/50 (6.0%) | 小批次: 1/17 | 已耗时: 0.5分钟 | 预计剩余: 8.3分钟 | 速度: 0.094节点/秒
2025-08-01 22:06:37,647 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:06:37,647 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:06:37,648 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:06:37,648 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:06:43,415 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-08-01 22:06:43,430 - INFO - text_processor - 成功处理实体: 圣地香都
2025-08-01 22:06:43,430 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:06:53,735 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-08-01 22:06:53,755 - INFO - text_processor - 成功处理实体: 曲苏
2025-08-01 22:06:53,755 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:03,131 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-08-01 22:07:03,150 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-08-01 22:07:03,151 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:07:03,151 - INFO - __main__ - 进度: 6/50 (12.0%) | 小批次: 2/17 | 已耗时: 1.0分钟 | 预计剩余: 7.0分钟 | 速度: 0.105节点/秒
2025-08-01 22:07:03,151 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:07:03,151 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:07:03,151 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:07:03,151 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:08,400 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-08-01 22:07:08,413 - INFO - text_processor - 成功处理实体: 喜德林
2025-08-01 22:07:08,413 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:16,550 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-08-01 22:07:16,565 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-08-01 22:07:16,566 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:23,267 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-08-01 22:07:23,287 - INFO - text_processor - 成功处理实体: 水上乐园
2025-08-01 22:07:23,288 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:07:23,288 - INFO - __main__ - 进度: 9/50 (18.0%) | 小批次: 3/17 | 已耗时: 1.3分钟 | 预计剩余: 5.9分钟 | 速度: 0.116节点/秒
2025-08-01 22:07:23,288 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:07:23,288 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:07:23,288 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:07:23,289 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:34,208 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-08-01 22:07:34,294 - INFO - text_processor - 成功处理实体: 阿热康参
2025-08-01 22:07:34,294 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:42,472 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达巧拉康
2025-08-01 22:07:42,485 - INFO - text_processor - 成功处理实体: 达巧拉康
2025-08-01 22:07:42,485 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:07:51,570 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-08-01 22:07:51,635 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-08-01 22:07:51,635 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:07:51,635 - INFO - __main__ - 进度: 12/50 (24.0%) | 小批次: 4/17 | 已耗时: 1.8分钟 | 预计剩余: 5.6分钟 | 速度: 0.113节点/秒
2025-08-01 22:07:51,635 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:07:51,635 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:07:51,636 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:07:51,636 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:03,035 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关净土
2025-08-01 22:08:03,052 - INFO - text_processor - 成功处理实体: 城关净土
2025-08-01 22:08:03,052 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:10,214 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-08-01 22:08:10,228 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-08-01 22:08:10,228 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:15,937 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏佛学院拉萨市哲蚌寺分院
2025-08-01 22:08:15,992 - INFO - text_processor - 成功处理实体: 西藏佛学院拉萨市哲蚌寺分院
2025-08-01 22:08:15,993 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:08:15,993 - INFO - __main__ - 进度: 15/50 (30.0%) | 小批次: 5/17 | 已耗时: 2.2分钟 | 预计剩余: 5.1分钟 | 速度: 0.115节点/秒
2025-08-01 22:08:15,993 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:08:15,993 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:08:15,993 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:08:15,993 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:20,799 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-08-01 22:08:20,811 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-08-01 22:08:20,812 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:27,283 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷囊
2025-08-01 22:08:27,304 - INFO - text_processor - 成功处理实体: 列卜廷囊
2025-08-01 22:08:27,305 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:34,932 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 霍钦夏玛
2025-08-01 22:08:34,946 - INFO - text_processor - 成功处理实体: 霍钦夏玛
2025-08-01 22:08:34,946 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:08:34,946 - INFO - __main__ - 进度: 18/50 (36.0%) | 小批次: 6/17 | 已耗时: 2.5分钟 | 预计剩余: 4.4分钟 | 速度: 0.121节点/秒
2025-08-01 22:08:34,946 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:08:34,946 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:08:34,947 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:08:39,768 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-08-01 22:08:39,782 - INFO - text_processor - 成功处理实体: 策门林寺
2025-08-01 22:08:39,783 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:49,028 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺-厨房
2025-08-01 22:08:49,041 - INFO - text_processor - 成功处理实体: 乃琼寺-厨房
2025-08-01 22:08:49,042 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:54,972 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-讲经场
2025-08-01 22:08:54,989 - INFO - text_processor - 成功处理实体: 大昭寺-讲经场
2025-08-01 22:08:54,989 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:08:54,990 - INFO - __main__ - 进度: 21/50 (42.0%) | 小批次: 7/17 | 已耗时: 2.8分钟 | 预计剩余: 3.9分钟 | 速度: 0.124节点/秒
2025-08-01 22:08:54,990 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:08:54,990 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:08:54,990 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:08:54,990 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:08:59,553 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园纪念碑
2025-08-01 22:08:59,567 - INFO - text_processor - 成功处理实体: 宗角禄康公园纪念碑
2025-08-01 22:08:59,568 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:03,204 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃康鼎
2025-08-01 22:09:03,231 - INFO - text_processor - 成功处理实体: 乃康鼎
2025-08-01 22:09:03,232 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:10,446 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-08-01 22:09:10,460 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-08-01 22:09:10,461 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:09:10,461 - INFO - __main__ - 进度: 24/50 (48.0%) | 小批次: 8/17 | 已耗时: 3.1分钟 | 预计剩余: 3.3分钟 | 速度: 0.130节点/秒
2025-08-01 22:09:10,461 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:09:10,461 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:09:10,462 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:09:18,176 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-08-01 22:09:18,189 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-08-01 22:09:18,189 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:26,752 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-08-01 22:09:26,766 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-08-01 22:09:26,766 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:33,494 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 莲花宝殿
2025-08-01 22:09:33,508 - INFO - text_processor - 成功处理实体: 莲花宝殿
2025-08-01 22:09:33,509 - INFO - text_processor - 过滤后景点数量: 2
2025-08-01 22:09:38,926 - ERROR - text_processor - JSON 解析失败 for 西藏博物馆 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-08-01 22:09:38,928 - INFO - __main__ - 进度: 27/50 (54.0%) | 小批次: 9/17 | 已耗时: 3.6分钟 | 预计剩余: 3.0分钟 | 速度: 0.127节点/秒
2025-08-01 22:09:38,929 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:09:38,929 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:09:38,929 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:09:38,930 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:51,753 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 堆龙德庆区旅游服务区
2025-08-01 22:09:51,766 - INFO - text_processor - 成功处理实体: 堆龙德庆区旅游服务区
2025-08-01 22:09:51,766 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:09:59,318 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 悠远的天空自驾车营地
2025-08-01 22:09:59,331 - INFO - text_processor - 成功处理实体: 悠远的天空自驾车营地
2025-08-01 22:09:59,331 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:10:10,655 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉热
2025-08-01 22:10:10,670 - INFO - text_processor - 成功处理实体: 吉热
2025-08-01 22:10:10,670 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:10:10,670 - INFO - __main__ - 进度: 30/50 (60.0%) | 小批次: 10/17 | 已耗时: 4.1分钟 | 预计剩余: 2.7分钟 | 速度: 0.122节点/秒
2025-08-01 22:10:10,670 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:10:10,670 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:10:10,671 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:10:10,671 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:10:20,120 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉大乘洲
2025-08-01 22:10:20,132 - INFO - text_processor - 成功处理实体: 色拉大乘洲
2025-08-01 22:10:20,133 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:10:31,814 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛拉郭
2025-08-01 22:10:31,827 - INFO - text_processor - 成功处理实体: 玛拉郭
2025-08-01 22:10:31,827 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:10:45,738 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡布岗
2025-08-01 22:10:45,754 - INFO - text_processor - 成功处理实体: 贡布岗
2025-08-01 22:10:45,754 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:10:45,754 - INFO - __main__ - 进度: 33/50 (66.0%) | 小批次: 11/17 | 已耗时: 4.7分钟 | 预计剩余: 2.4分钟 | 速度: 0.118节点/秒
2025-08-01 22:10:45,755 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:10:45,755 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:10:45,755 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:10:45,755 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:01,446 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 新旧西藏对比展
2025-08-01 22:11:01,489 - INFO - text_processor - 成功处理实体: 新旧西藏对比展
2025-08-01 22:11:01,489 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:12,175 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 弥勒法轮像
2025-08-01 22:11:12,187 - INFO - text_processor - 成功处理实体: 弥勒法轮像
2025-08-01 22:11:18,121 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-08-01 22:11:18,135 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-08-01 22:11:18,135 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:11:18,135 - INFO - __main__ - 进度: 36/50 (72.0%) | 小批次: 12/17 | 已耗时: 5.2分钟 | 预计剩余: 2.0分钟 | 速度: 0.115节点/秒
2025-08-01 22:11:18,135 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:11:18,135 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:11:18,135 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:11:18,136 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:23,925 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏巴吉仓
2025-08-01 22:11:23,939 - INFO - text_processor - 成功处理实体: 藏巴吉仓
2025-08-01 22:11:23,939 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:31,832 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-08-01 22:11:31,844 - INFO - text_processor - 成功处理实体: 5238
2025-08-01 22:11:31,844 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:39,646 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨市综合展馆
2025-08-01 22:11:39,702 - INFO - text_processor - 成功处理实体: 拉萨市综合展馆
2025-08-01 22:11:39,702 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:11:39,702 - INFO - __main__ - 进度: 39/50 (78.0%) | 小批次: 13/17 | 已耗时: 5.6分钟 | 预计剩余: 1.6分钟 | 速度: 0.117节点/秒
2025-08-01 22:11:39,702 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:11:39,702 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:11:39,702 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:11:39,703 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:11:51,207 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-08-01 22:11:51,219 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-08-01 22:11:55,253 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-08-01 22:11:55,268 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-08-01 22:11:55,268 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:00,623 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两路精神纪念馆
2025-08-01 22:12:00,636 - INFO - text_processor - 成功处理实体: 两路精神纪念馆
2025-08-01 22:12:00,637 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:12:00,637 - INFO - __main__ - 进度: 42/50 (84.0%) | 小批次: 14/17 | 已耗时: 5.9分钟 | 预计剩余: 1.1分钟 | 速度: 0.118节点/秒
2025-08-01 22:12:00,637 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:12:00,637 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:12:00,637 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:12:00,638 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:09,177 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶文化馆
2025-08-01 22:12:09,190 - INFO - text_processor - 成功处理实体: 茶文化馆
2025-08-01 22:12:09,190 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:21,052 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-08-01 22:12:21,067 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-08-01 22:12:21,068 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:26,924 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 擦多热康参
2025-08-01 22:12:26,936 - INFO - text_processor - 成功处理实体: 擦多热康参
2025-08-01 22:12:26,936 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:12:26,936 - INFO - __main__ - 进度: 45/50 (90.0%) | 小批次: 15/17 | 已耗时: 6.4分钟 | 预计剩余: 0.7分钟 | 速度: 0.118节点/秒
2025-08-01 22:12:26,937 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:12:26,937 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:12:26,937 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:12:31,872 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-08-01 22:12:31,884 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-08-01 22:12:31,884 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:40,307 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡如康参
2025-08-01 22:12:40,321 - INFO - text_processor - 成功处理实体: 贡如康参
2025-08-01 22:12:40,322 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:47,704 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 琼结崩唐
2025-08-01 22:12:47,722 - INFO - text_processor - 成功处理实体: 琼结崩唐
2025-08-01 22:12:47,722 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:12:47,722 - INFO - __main__ - 进度: 48/50 (96.0%) | 小批次: 16/17 | 已耗时: 6.7分钟 | 预计剩余: 0.3分钟 | 速度: 0.119节点/秒
2025-08-01 22:12:47,722 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:12:47,723 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:12:47,723 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:12:47,723 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:54,428 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰琼冬让
2025-08-01 22:12:54,440 - INFO - text_processor - 成功处理实体: 恰琼冬让
2025-08-01 22:12:54,440 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:12:59,741 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-08-01 22:12:59,814 - INFO - text_processor - 成功处理实体: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-08-01 22:12:59,814 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:12:59,814 - INFO - __main__ - 进度: 50/50 (100.0%) | 小批次: 17/17 | 已耗时: 6.9分钟 | 预计剩余: 0.0分钟 | 速度: 0.121节点/秒
2025-08-01 22:12:59,815 - INFO - __main__ - 真实数据批次处理完成 - 大小: 50
2025-08-01 22:12:59,815 - INFO - __main__ - 耗时: 0.12小时 (6.9分钟)
2025-08-01 22:12:59,815 - INFO - __main__ - 成功: 50, 失败: 0
2025-08-01 22:12:59,815 - INFO - __main__ - 速度: 0.121节点/秒
2025-08-01 22:12:59,815 - INFO - __main__ - 真实数据比例: 100.0%
2025-08-01 22:12:59,817 - INFO - __main__ - 中间结果已保存: 1 个批次
2025-08-01 22:12:59,818 - INFO - __main__ - 最终结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.json
2025-08-01 22:12:59,825 - INFO - __main__ - CSV结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.csv
2025-08-01 22:12:59,825 - INFO - __main__ - 
========================================================================================================================
2025-08-01 22:12:59,825 - INFO - __main__ - 1000个真实西藏景点同步批量处理测试结果摘要
2025-08-01 22:12:59,825 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:12:59,826 - INFO - __main__ - 批次:   50 | 时间:   0.12小时 | 速度:  0.121节点/秒 | 真实数据: 100.0% | 成功:   50
2025-08-01 22:12:59,826 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:12:59,826 - INFO - __main__ - 总处理节点数: 50
2025-08-01 22:12:59,826 - INFO - __main__ - 总处理时间: 0.12小时
2025-08-01 22:12:59,826 - INFO - __main__ - 平均处理速度: 0.121节点/秒
2025-08-01 22:12:59,826 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:12:59,827 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-08-01 22:12:59,827 - INFO - text_processor - 关闭资源
2025-08-01 22:13:25,187 - INFO - __main__ - 用户指定批次: [100]
2025-08-01 22:13:25,187 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 22:13:25,212 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 22:13:25,213 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 22:13:25,213 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 22:13:25,213 - INFO - __main__ - 测试批次: [100]
2025-08-01 22:13:25,227 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 22:13:25,227 - INFO - __main__ - 数据集整体组成:
2025-08-01 22:13:25,227 - INFO - __main__ -   总数: 1000
2025-08-01 22:13:25,227 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 22:13:25,228 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 22:13:25,228 - INFO - __main__ - 
====================================================================================================
2025-08-01 22:13:25,228 - INFO - __main__ - 开始处理真实数据批次 1/1: 100个节点
2025-08-01 22:13:25,228 - INFO - __main__ - 预估API调用: 4,950 次
2025-08-01 22:13:25,228 - INFO - __main__ - 预估时间: 0.21 小时
2025-08-01 22:13:25,228 - INFO - __main__ - ====================================================================================================
2025-08-01 22:13:25,228 - INFO - __main__ - 开始处理真实数据批次，大小: 100
2025-08-01 22:13:25,229 - INFO - __main__ - 预估API调用次数: 4,950
2025-08-01 22:13:25,229 - INFO - __main__ - 预估处理时间: 0.21小时
2025-08-01 22:13:25,263 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:13:25,268 - INFO - neo4j_connection - 数据库已清空
2025-08-01 22:13:25,270 - INFO - text_processor - 清空所有节点和关系
2025-08-01 22:13:25,274 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 22:13:25,277 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 22:13:25,278 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 22:13:25,278 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 22:13:25,278 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:13:25,278 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:13:25,278 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:13:25,279 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:13:31,652 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 22:13:31,734 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 22:13:31,734 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:13:38,574 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:13:38,627 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 22:13:43,257 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-08-01 22:13:43,328 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-08-01 22:13:43,329 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:13:43,329 - INFO - __main__ - 进度: 3/100 (3.0%) | 小批次: 1/34 | 已耗时: 0.3分钟 | 预计剩余: 9.7分钟 | 速度: 0.166节点/秒
2025-08-01 22:13:43,329 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:13:43,329 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:13:43,329 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:13:43,329 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:13:50,365 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-08-01 22:13:50,381 - INFO - text_processor - 成功处理实体: 圣地香都
2025-08-01 22:13:50,381 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:13:54,462 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-08-01 22:13:54,545 - INFO - text_processor - 成功处理实体: 曲苏
2025-08-01 22:13:54,545 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:04,358 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-08-01 22:14:04,370 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-08-01 22:14:04,370 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:14:04,371 - INFO - __main__ - 进度: 6/100 (6.0%) | 小批次: 2/34 | 已耗时: 0.7分钟 | 预计剩余: 10.2分钟 | 速度: 0.153节点/秒
2025-08-01 22:14:04,371 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:14:04,371 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:14:04,371 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:14:04,371 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:08,983 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-08-01 22:14:08,996 - INFO - text_processor - 成功处理实体: 喜德林
2025-08-01 22:14:08,996 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:16,260 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-08-01 22:14:16,271 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-08-01 22:14:16,271 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:21,466 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-08-01 22:14:21,479 - INFO - text_processor - 成功处理实体: 水上乐园
2025-08-01 22:14:21,479 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:14:21,479 - INFO - __main__ - 进度: 9/100 (9.0%) | 小批次: 3/34 | 已耗时: 0.9分钟 | 预计剩余: 9.5分钟 | 速度: 0.160节点/秒
2025-08-01 22:14:21,479 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:14:21,479 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:14:21,479 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:14:21,479 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:27,287 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-08-01 22:14:27,298 - INFO - text_processor - 成功处理实体: 阿热康参
2025-08-01 22:14:27,299 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:33,775 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达巧拉康
2025-08-01 22:14:33,838 - INFO - text_processor - 成功处理实体: 达巧拉康
2025-08-01 22:14:33,838 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:41,472 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-08-01 22:14:41,491 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-08-01 22:14:41,492 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:14:41,492 - INFO - __main__ - 进度: 12/100 (12.0%) | 小批次: 4/34 | 已耗时: 1.3分钟 | 预计剩余: 9.3分钟 | 速度: 0.157节点/秒
2025-08-01 22:14:41,492 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:14:41,493 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:14:41,493 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:14:41,493 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:48,964 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关净土
2025-08-01 22:14:48,980 - INFO - text_processor - 成功处理实体: 城关净土
2025-08-01 22:14:48,980 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:14:56,047 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-08-01 22:14:56,060 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-08-01 22:14:56,061 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:15:10,650 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏佛学院拉萨市哲蚌寺分院
2025-08-01 22:15:10,704 - INFO - text_processor - 成功处理实体: 西藏佛学院拉萨市哲蚌寺分院
2025-08-01 22:15:10,705 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:15:10,705 - INFO - __main__ - 进度: 15/100 (15.0%) | 小批次: 5/34 | 已耗时: 1.8分钟 | 预计剩余: 10.0分钟 | 速度: 0.142节点/秒
2025-08-01 22:15:10,705 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:15:10,705 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:15:10,705 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:15:10,706 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:15:18,065 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-08-01 22:15:18,078 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-08-01 22:15:18,079 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:15:27,738 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷囊
2025-08-01 22:15:27,749 - INFO - text_processor - 成功处理实体: 列卜廷囊
2025-08-01 22:15:27,749 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:15:39,201 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 霍钦夏玛
2025-08-01 22:15:39,214 - INFO - text_processor - 成功处理实体: 霍钦夏玛
2025-08-01 22:15:39,214 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:15:39,216 - INFO - __main__ - 进度: 18/100 (18.0%) | 小批次: 6/34 | 已耗时: 2.2分钟 | 预计剩余: 10.2分钟 | 速度: 0.134节点/秒
2025-08-01 22:15:39,216 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:15:39,216 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:15:39,216 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:15:46,280 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-08-01 22:15:46,296 - INFO - text_processor - 成功处理实体: 策门林寺
2025-08-01 22:15:46,296 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:15:54,138 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺-厨房
2025-08-01 22:15:54,153 - INFO - text_processor - 成功处理实体: 乃琼寺-厨房
2025-08-01 22:15:54,153 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:03,314 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-讲经场
2025-08-01 22:16:03,327 - INFO - text_processor - 成功处理实体: 大昭寺-讲经场
2025-08-01 22:16:03,327 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:16:03,327 - INFO - __main__ - 进度: 21/100 (21.0%) | 小批次: 7/34 | 已耗时: 2.6分钟 | 预计剩余: 9.9分钟 | 速度: 0.133节点/秒
2025-08-01 22:16:03,328 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:16:03,328 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:16:03,328 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:16:03,328 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:13,395 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园纪念碑
2025-08-01 22:16:13,409 - INFO - text_processor - 成功处理实体: 宗角禄康公园纪念碑
2025-08-01 22:16:13,409 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:18,769 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃康鼎
2025-08-01 22:16:18,782 - INFO - text_processor - 成功处理实体: 乃康鼎
2025-08-01 22:16:18,782 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:22,923 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-08-01 22:16:22,933 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-08-01 22:16:22,934 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:16:22,934 - INFO - __main__ - 进度: 24/100 (24.0%) | 小批次: 8/34 | 已耗时: 3.0分钟 | 预计剩余: 9.4分钟 | 速度: 0.135节点/秒
2025-08-01 22:16:22,934 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:16:22,934 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:16:22,935 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:16:28,481 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-08-01 22:16:28,497 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-08-01 22:16:28,497 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:35,655 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-08-01 22:16:35,666 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-08-01 22:16:35,666 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:42,108 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 莲花宝殿
2025-08-01 22:16:42,120 - INFO - text_processor - 成功处理实体: 莲花宝殿
2025-08-01 22:16:42,120 - INFO - text_processor - 过滤后景点数量: 2
2025-08-01 22:16:49,644 - ERROR - text_processor - JSON 解析失败 for 西藏博物馆 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是与西藏文化相关的景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-08-01 22:16:49,644 - INFO - __main__ - 进度: 27/100 (27.0%) | 小批次: 9/34 | 已耗时: 3.4分钟 | 预计剩余: 9.2分钟 | 速度: 0.132节点/秒
2025-08-01 22:16:49,645 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:16:49,645 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:16:49,645 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:16:49,646 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:16:55,323 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 堆龙德庆区旅游服务区
2025-08-01 22:16:55,335 - INFO - text_processor - 成功处理实体: 堆龙德庆区旅游服务区
2025-08-01 22:16:55,335 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:02,669 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 悠远的天空自驾车营地
2025-08-01 22:17:02,679 - INFO - text_processor - 成功处理实体: 悠远的天空自驾车营地
2025-08-01 22:17:02,679 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:13,883 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉热
2025-08-01 22:17:13,897 - INFO - text_processor - 成功处理实体: 吉热
2025-08-01 22:17:13,897 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:17:13,897 - INFO - __main__ - 进度: 30/100 (30.0%) | 小批次: 10/34 | 已耗时: 3.8分钟 | 预计剩余: 8.9分钟 | 速度: 0.131节点/秒
2025-08-01 22:17:13,898 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:17:13,898 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:17:13,898 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:17:13,898 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:22,483 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉大乘洲
2025-08-01 22:17:22,502 - INFO - text_processor - 成功处理实体: 色拉大乘洲
2025-08-01 22:17:22,503 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:33,133 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛拉郭
2025-08-01 22:17:33,145 - INFO - text_processor - 成功处理实体: 玛拉郭
2025-08-01 22:17:33,146 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:38,208 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡布岗
2025-08-01 22:17:38,220 - INFO - text_processor - 成功处理实体: 贡布岗
2025-08-01 22:17:38,221 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:17:38,221 - INFO - __main__ - 进度: 33/100 (33.0%) | 小批次: 11/34 | 已耗时: 4.2分钟 | 预计剩余: 8.6分钟 | 速度: 0.130节点/秒
2025-08-01 22:17:38,221 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:17:38,221 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:17:38,221 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:17:38,221 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:42,891 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 新旧西藏对比展
2025-08-01 22:17:42,902 - INFO - text_processor - 成功处理实体: 新旧西藏对比展
2025-08-01 22:17:42,903 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:17:48,779 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 弥勒法轮像
2025-08-01 22:17:48,853 - INFO - text_processor - 成功处理实体: 弥勒法轮像
2025-08-01 22:17:54,609 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-08-01 22:17:54,623 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-08-01 22:17:54,624 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:17:54,624 - INFO - __main__ - 进度: 36/100 (36.0%) | 小批次: 12/34 | 已耗时: 4.5分钟 | 预计剩余: 8.0分钟 | 速度: 0.134节点/秒
2025-08-01 22:17:54,624 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:17:54,624 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:17:54,624 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:17:54,624 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:01,982 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏巴吉仓
2025-08-01 22:18:01,997 - INFO - text_processor - 成功处理实体: 藏巴吉仓
2025-08-01 22:18:01,998 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:08,762 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-08-01 22:18:08,773 - INFO - text_processor - 成功处理实体: 5238
2025-08-01 22:18:08,773 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:15,009 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨市综合展馆
2025-08-01 22:18:15,020 - INFO - text_processor - 成功处理实体: 拉萨市综合展馆
2025-08-01 22:18:15,021 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:18:15,021 - INFO - __main__ - 进度: 39/100 (39.0%) | 小批次: 13/34 | 已耗时: 4.8分钟 | 预计剩余: 7.6分钟 | 速度: 0.135节点/秒
2025-08-01 22:18:15,021 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:18:15,021 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:18:15,021 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:18:15,022 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:20,812 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-08-01 22:18:20,822 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-08-01 22:18:30,890 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-08-01 22:18:30,903 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-08-01 22:18:30,904 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:38,470 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两路精神纪念馆
2025-08-01 22:18:38,481 - INFO - text_processor - 成功处理实体: 两路精神纪念馆
2025-08-01 22:18:38,481 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:18:38,481 - INFO - __main__ - 进度: 42/100 (42.0%) | 小批次: 14/34 | 已耗时: 5.2分钟 | 预计剩余: 7.2分钟 | 速度: 0.134节点/秒
2025-08-01 22:18:38,481 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:18:38,481 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:18:38,481 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:18:38,482 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:45,079 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶文化馆
2025-08-01 22:18:45,091 - INFO - text_processor - 成功处理实体: 茶文化馆
2025-08-01 22:18:45,091 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:51,394 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-08-01 22:18:51,404 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-08-01 22:18:51,405 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:18:58,944 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 擦多热康参
2025-08-01 22:18:58,956 - INFO - text_processor - 成功处理实体: 擦多热康参
2025-08-01 22:18:58,957 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:18:58,957 - INFO - __main__ - 进度: 45/100 (45.0%) | 小批次: 15/34 | 已耗时: 5.6分钟 | 预计剩余: 6.8分钟 | 速度: 0.135节点/秒
2025-08-01 22:18:58,957 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:18:58,957 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:18:58,957 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:19:06,565 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-08-01 22:19:06,576 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-08-01 22:19:06,577 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:19:13,151 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡如康参
2025-08-01 22:19:13,162 - INFO - text_processor - 成功处理实体: 贡如康参
2025-08-01 22:19:13,163 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:19:19,719 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 琼结崩唐
2025-08-01 22:19:19,728 - INFO - text_processor - 成功处理实体: 琼结崩唐
2025-08-01 22:19:19,729 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:19:19,729 - INFO - __main__ - 进度: 48/100 (48.0%) | 小批次: 16/34 | 已耗时: 5.9分钟 | 预计剩余: 6.4分钟 | 速度: 0.135节点/秒
2025-08-01 22:19:19,729 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:19:19,729 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:19:19,730 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:19:19,730 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:19:27,909 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰琼冬让
2025-08-01 22:19:27,919 - INFO - text_processor - 成功处理实体: 恰琼冬让
2025-08-01 22:19:27,919 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:19:38,978 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-08-01 22:19:39,028 - INFO - text_processor - 成功处理实体: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-08-01 22:19:39,028 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:19:47,139 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 舒适的露营地
2025-08-01 22:19:47,159 - INFO - text_processor - 成功处理实体: 舒适的露营地
2025-08-01 22:19:47,160 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:19:47,160 - INFO - __main__ - 进度: 51/100 (51.0%) | 小批次: 17/34 | 已耗时: 6.4分钟 | 预计剩余: 6.1分钟 | 速度: 0.134节点/秒
2025-08-01 22:19:47,160 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:19:47,161 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:19:47,161 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:19:47,161 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:04,447 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎尔琼拉康
2025-08-01 22:20:04,462 - INFO - text_processor - 成功处理实体: 嘎尔琼拉康
2025-08-01 22:20:04,462 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:14,453 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 群巴
2025-08-01 22:20:14,463 - INFO - text_processor - 成功处理实体: 群巴
2025-08-01 22:20:14,464 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:21,418 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 波康参
2025-08-01 22:20:21,428 - INFO - text_processor - 成功处理实体: 波康参
2025-08-01 22:20:21,428 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:20:21,428 - INFO - __main__ - 进度: 54/100 (54.0%) | 小批次: 18/34 | 已耗时: 6.9分钟 | 预计剩余: 5.9分钟 | 速度: 0.130节点/秒
2025-08-01 22:20:21,428 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:20:21,429 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:20:21,429 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:20:21,429 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:28,631 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 墨脱石锅
2025-08-01 22:20:28,645 - INFO - text_processor - 成功处理实体: 墨脱石锅
2025-08-01 22:20:28,645 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:38,047 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙皇精品紫铜佛像扎基寺分店
2025-08-01 22:20:38,060 - INFO - text_processor - 成功处理实体: 龙皇精品紫铜佛像扎基寺分店
2025-08-01 22:20:38,060 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:45,549 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德曲郭卓吉林卡
2025-08-01 22:20:45,560 - INFO - text_processor - 成功处理实体: 喜德曲郭卓吉林卡
2025-08-01 22:20:45,560 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:20:45,560 - INFO - __main__ - 进度: 57/100 (57.0%) | 小批次: 19/34 | 已耗时: 7.3分钟 | 预计剩余: 5.5分钟 | 速度: 0.129节点/秒
2025-08-01 22:20:45,560 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:20:45,561 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:20:45,561 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:20:49,332 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-08-01 22:20:49,344 - INFO - text_processor - 成功处理实体: 小昭寺
2025-08-01 22:20:49,344 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:20:55,663 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 擦瓦杰擦
2025-08-01 22:20:55,673 - INFO - text_processor - 成功处理实体: 擦瓦杰擦
2025-08-01 22:20:55,675 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:08,801 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 沃明心皈
2025-08-01 22:21:08,812 - INFO - text_processor - 成功处理实体: 沃明心皈
2025-08-01 22:21:08,812 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:21:08,812 - INFO - __main__ - 进度: 60/100 (60.0%) | 小批次: 20/34 | 已耗时: 7.7分钟 | 预计剩余: 5.2分钟 | 速度: 0.129节点/秒
2025-08-01 22:21:08,813 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:21:08,813 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:21:08,813 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:21:14,174 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-08-01 22:21:14,183 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-08-01 22:21:14,183 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:20,485 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达孜三合农庄餐饮棋牌垂钓露营地
2025-08-01 22:21:20,495 - INFO - text_processor - 成功处理实体: 达孜三合农庄餐饮棋牌垂钓露营地
2025-08-01 22:21:20,495 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:25,829 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 涉溪山谷露营地
2025-08-01 22:21:25,842 - INFO - text_processor - 成功处理实体: 涉溪山谷露营地
2025-08-01 22:21:25,842 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:21:25,843 - INFO - __main__ - 进度: 63/100 (63.0%) | 小批次: 21/34 | 已耗时: 8.0分钟 | 预计剩余: 4.7分钟 | 速度: 0.131节点/秒
2025-08-01 22:21:25,843 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:21:25,843 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:21:25,843 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:21:25,843 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:30,593 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 比都康参
2025-08-01 22:21:30,602 - INFO - text_processor - 成功处理实体: 比都康参
2025-08-01 22:21:30,603 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:36,414 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城北萨斯格桑林卡
2025-08-01 22:21:36,425 - INFO - text_processor - 成功处理实体: 城北萨斯格桑林卡
2025-08-01 22:21:36,425 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:42,691 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺扎迪(林卡)
2025-08-01 22:21:42,702 - INFO - text_processor - 成功处理实体: 色拉寺扎迪(林卡)
2025-08-01 22:21:42,703 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:21:42,703 - INFO - __main__ - 进度: 66/100 (66.0%) | 小批次: 22/34 | 已耗时: 8.3分钟 | 预计剩余: 4.3分钟 | 速度: 0.133节点/秒
2025-08-01 22:21:42,703 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:21:42,703 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:21:42,703 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:21:42,703 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:48,507 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 札隆纳交
2025-08-01 22:21:48,517 - INFO - text_processor - 成功处理实体: 札隆纳交
2025-08-01 22:21:48,518 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:21:55,043 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 杰荣康萨
2025-08-01 22:21:55,055 - INFO - text_processor - 成功处理实体: 杰荣康萨
2025-08-01 22:21:55,056 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:01,252 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 错尼拉
2025-08-01 22:22:01,263 - INFO - text_processor - 成功处理实体: 错尼拉
2025-08-01 22:22:01,263 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:22:01,264 - INFO - __main__ - 进度: 69/100 (69.0%) | 小批次: 23/34 | 已耗时: 8.6分钟 | 预计剩余: 3.9分钟 | 速度: 0.134节点/秒
2025-08-01 22:22:01,264 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:22:01,264 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:22:01,264 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:22:01,265 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:13,723 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宝藏局雪造币厂
2025-08-01 22:22:13,732 - INFO - text_processor - 成功处理实体: 宝藏局雪造币厂
2025-08-01 22:22:25,740 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-08-01 22:22:25,751 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-08-01 22:22:25,751 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:31,754 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫观景台
2025-08-01 22:22:31,763 - INFO - text_processor - 成功处理实体: 布达拉宫观景台
2025-08-01 22:22:31,764 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:22:31,764 - INFO - __main__ - 进度: 72/100 (72.0%) | 小批次: 24/34 | 已耗时: 9.1分钟 | 预计剩余: 3.5分钟 | 速度: 0.132节点/秒
2025-08-01 22:22:31,764 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:22:31,764 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:22:31,764 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:22:31,764 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:40,149 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布热吉康萨巴
2025-08-01 22:22:40,159 - INFO - text_processor - 成功处理实体: 崩布热吉康萨巴
2025-08-01 22:22:46,030 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-08-01 22:22:46,040 - INFO - text_processor - 成功处理实体: 楚布寺
2025-08-01 22:22:46,040 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:53,480 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 会客厅
2025-08-01 22:22:53,490 - INFO - text_processor - 成功处理实体: 会客厅
2025-08-01 22:22:53,490 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:22:53,490 - INFO - __main__ - 进度: 75/100 (75.0%) | 小批次: 25/34 | 已耗时: 9.5分钟 | 预计剩余: 3.2分钟 | 速度: 0.132节点/秒
2025-08-01 22:22:53,490 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:22:53,491 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:22:53,491 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:22:53,491 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:22:58,421 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西印经院
2025-08-01 22:22:58,431 - INFO - text_processor - 成功处理实体: 西印经院
2025-08-01 22:22:58,431 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:05,750 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏闽兴
2025-08-01 22:23:05,761 - INFO - text_processor - 成功处理实体: 西藏闽兴
2025-08-01 22:23:05,761 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:11,682 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 护法殿
2025-08-01 22:23:11,692 - INFO - text_processor - 成功处理实体: 护法殿
2025-08-01 22:23:11,693 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:23:11,693 - INFO - __main__ - 进度: 78/100 (78.0%) | 小批次: 26/34 | 已耗时: 9.8分钟 | 预计剩余: 2.8分钟 | 速度: 0.133节点/秒
2025-08-01 22:23:11,693 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:23:11,693 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:23:11,693 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:23:11,694 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:26,618 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两岛街道综治网格一体化中心
2025-08-01 22:23:26,629 - INFO - text_processor - 成功处理实体: 两岛街道综治网格一体化中心
2025-08-01 22:23:26,630 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:36,101 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格藏拉让
2025-08-01 22:23:36,113 - INFO - text_processor - 成功处理实体: 格藏拉让
2025-08-01 22:23:36,113 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:40,602 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 措钦大殿
2025-08-01 22:23:40,618 - INFO - text_processor - 成功处理实体: 措钦大殿
2025-08-01 22:23:40,618 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:23:40,618 - INFO - __main__ - 进度: 81/100 (81.0%) | 小批次: 27/34 | 已耗时: 10.3分钟 | 预计剩余: 2.4分钟 | 速度: 0.132节点/秒
2025-08-01 22:23:40,619 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:23:40,619 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:23:40,619 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:23:40,619 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:46,235 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松格果觉(白宫门厅)
2025-08-01 22:23:46,244 - INFO - text_processor - 成功处理实体: 松格果觉(白宫门厅)
2025-08-01 22:23:46,244 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:23:54,532 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 柳梧桑女林卡
2025-08-01 22:23:54,540 - INFO - text_processor - 成功处理实体: 柳梧桑女林卡
2025-08-01 22:23:54,540 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:01,910 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 坛城院
2025-08-01 22:24:01,921 - INFO - text_processor - 成功处理实体: 坛城院
2025-08-01 22:24:01,921 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:24:01,921 - INFO - __main__ - 进度: 84/100 (84.0%) | 小批次: 28/34 | 已耗时: 10.6分钟 | 预计剩余: 2.0分钟 | 速度: 0.132节点/秒
2025-08-01 22:24:01,922 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:24:01,922 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:24:01,922 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:24:01,922 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:06,414 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林卡
2025-08-01 22:24:06,424 - INFO - text_processor - 成功处理实体: 德吉林卡
2025-08-01 22:24:06,424 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:19,090 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 陕西民俗村
2025-08-01 22:24:19,100 - INFO - text_processor - 成功处理实体: 陕西民俗村
2025-08-01 22:24:19,100 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:27,681 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏擦擦文化展览馆
2025-08-01 22:24:27,697 - INFO - text_processor - 成功处理实体: 西藏擦擦文化展览馆
2025-08-01 22:24:27,697 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:24:27,697 - INFO - __main__ - 进度: 87/100 (87.0%) | 小批次: 29/34 | 已耗时: 11.0分钟 | 预计剩余: 1.6分钟 | 速度: 0.131节点/秒
2025-08-01 22:24:27,698 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:24:27,698 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:24:27,698 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:24:27,698 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:37,606 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷拉
2025-08-01 22:24:37,617 - INFO - text_processor - 成功处理实体: 列卜廷拉
2025-08-01 22:24:37,618 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:43,717 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 密宗院
2025-08-01 22:24:43,728 - INFO - text_processor - 成功处理实体: 密宗院
2025-08-01 22:24:50,886 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-08-01 22:24:50,898 - INFO - text_processor - 成功处理实体: 八廓街
2025-08-01 22:24:50,898 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 22:24:50,898 - INFO - __main__ - 进度: 90/100 (90.0%) | 小批次: 30/34 | 已耗时: 11.4分钟 | 预计剩余: 1.3分钟 | 速度: 0.131节点/秒
2025-08-01 22:24:50,900 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:24:50,900 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:24:50,900 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:24:50,900 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:24:57,801 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺杰扎仓
2025-08-01 22:24:57,811 - INFO - text_processor - 成功处理实体: 色拉寺杰扎仓
2025-08-01 22:24:57,811 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:09,944 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如吉康
2025-08-01 22:25:09,960 - INFO - text_processor - 成功处理实体: 木如吉康
2025-08-01 22:25:09,961 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:15,888 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两岛生态公园
2025-08-01 22:25:15,899 - INFO - text_processor - 成功处理实体: 两岛生态公园
2025-08-01 22:25:15,900 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:25:15,900 - INFO - __main__ - 进度: 93/100 (93.0%) | 小批次: 31/34 | 已耗时: 11.8分钟 | 预计剩余: 0.9分钟 | 速度: 0.131节点/秒
2025-08-01 22:25:15,900 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:25:15,900 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:25:15,900 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:25:15,900 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:22,220 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德勒林卡
2025-08-01 22:25:22,234 - INFO - text_processor - 成功处理实体: 德勒林卡
2025-08-01 22:25:22,235 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:26,007 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔布康参
2025-08-01 22:25:26,017 - INFO - text_processor - 成功处理实体: 塔布康参
2025-08-01 22:25:26,017 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:37,592 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思泽
2025-08-01 22:25:37,603 - INFO - text_processor - 成功处理实体: 思泽
2025-08-01 22:25:37,604 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:25:37,604 - INFO - __main__ - 进度: 96/100 (96.0%) | 小批次: 32/34 | 已耗时: 12.2分钟 | 预计剩余: 0.5分钟 | 速度: 0.131节点/秒
2025-08-01 22:25:37,604 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:25:37,604 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:25:37,604 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:25:37,604 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:48,445 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 国家网络安全宣传周西藏自治区活动展览馆
2025-08-01 22:25:48,457 - INFO - text_processor - 成功处理实体: 国家网络安全宣传周西藏自治区活动展览馆
2025-08-01 22:25:48,457 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:25:55,598 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 接巴果热
2025-08-01 22:25:55,611 - INFO - text_processor - 成功处理实体: 接巴果热
2025-08-01 22:25:55,612 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:26:05,438 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白宫门廊
2025-08-01 22:26:05,448 - INFO - text_processor - 成功处理实体: 白宫门廊
2025-08-01 22:26:05,448 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:26:05,448 - INFO - __main__ - 进度: 99/100 (99.0%) | 小批次: 33/34 | 已耗时: 12.7分钟 | 预计剩余: 0.1分钟 | 速度: 0.130节点/秒
2025-08-01 22:26:05,449 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 22:26:05,449 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 22:26:05,449 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 22:26:05,449 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 22:26:12,943 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶马古道马帮落脚点遗址
2025-08-01 22:26:12,954 - INFO - text_processor - 成功处理实体: 茶马古道马帮落脚点遗址
2025-08-01 22:26:12,954 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 22:26:12,954 - INFO - __main__ - 进度: 100/100 (100.0%) | 小批次: 34/34 | 已耗时: 12.8分钟 | 预计剩余: 0.0分钟 | 速度: 0.130节点/秒
2025-08-01 22:26:12,955 - INFO - __main__ - 进度检查点已保存: checkpoint_real_batch_100.json
2025-08-01 22:26:12,955 - INFO - __main__ - 真实数据批次处理完成 - 大小: 100
2025-08-01 22:26:12,956 - INFO - __main__ - 耗时: 0.21小时 (12.8分钟)
2025-08-01 22:26:12,956 - INFO - __main__ - 成功: 100, 失败: 0
2025-08-01 22:26:12,956 - INFO - __main__ - 速度: 0.130节点/秒
2025-08-01 22:26:12,956 - INFO - __main__ - 真实数据比例: 100.0%
2025-08-01 22:26:12,957 - INFO - __main__ - 中间结果已保存: 1 个批次
2025-08-01 22:26:12,959 - INFO - __main__ - 最终结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.json
2025-08-01 22:26:12,965 - INFO - __main__ - CSV结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.csv
2025-08-01 22:26:12,966 - INFO - __main__ - 
========================================================================================================================
2025-08-01 22:26:12,966 - INFO - __main__ - 1000个真实西藏景点同步批量处理测试结果摘要
2025-08-01 22:26:12,966 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:26:12,967 - INFO - __main__ - 批次:  100 | 时间:   0.21小时 | 速度:  0.130节点/秒 | 真实数据: 100.0% | 成功:  100
2025-08-01 22:26:12,967 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:26:12,967 - INFO - __main__ - 总处理节点数: 100
2025-08-01 22:26:12,967 - INFO - __main__ - 总处理时间: 0.21小时
2025-08-01 22:26:12,967 - INFO - __main__ - 平均处理速度: 0.130节点/秒
2025-08-01 22:26:12,968 - INFO - __main__ - ========================================================================================================================
2025-08-01 22:26:12,968 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-08-01 22:26:12,968 - INFO - text_processor - 关闭资源
