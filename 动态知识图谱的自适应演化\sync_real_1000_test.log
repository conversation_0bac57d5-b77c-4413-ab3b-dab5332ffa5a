2025-08-01 21:41:33,375 - INFO - __main__ - 用户指定批次: [10, 50, 100]
2025-08-01 21:41:33,376 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 21:41:33,409 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 21:41:33,409 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 21:41:33,410 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 21:41:33,410 - INFO - __main__ - 测试批次: [10, 50, 100]
2025-08-01 21:41:33,419 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 21:41:33,420 - INFO - __main__ - 数据集整体组成:
2025-08-01 21:41:33,420 - INFO - __main__ -   总数: 1000
2025-08-01 21:41:33,420 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 21:41:33,420 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 21:41:33,420 - INFO - __main__ - 
====================================================================================================
2025-08-01 21:41:33,421 - INFO - __main__ - 开始处理真实数据批次 1/3: 10个节点
2025-08-01 21:41:33,421 - INFO - __main__ - 预估API调用: 45 次
2025-08-01 21:41:33,421 - INFO - __main__ - 预估时间: 0.00 小时
2025-08-01 21:41:33,422 - INFO - __main__ - ====================================================================================================
2025-08-01 21:41:33,422 - INFO - __main__ - 开始处理真实数据批次，大小: 10
2025-08-01 21:41:33,422 - INFO - __main__ - 预估API调用次数: 45
2025-08-01 21:41:33,423 - INFO - __main__ - 预估处理时间: 0.00小时
2025-08-01 21:41:33,720 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:41:33,725 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:41:33,725 - INFO - text_processor - 清空所有节点和关系
2025-08-01 21:41:33,824 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 21:41:33,838 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 21:41:33,839 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 21:41:33,839 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 21:41:33,839 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:41:33,839 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:41:33,840 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:41:33,840 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:41:41,940 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 21:41:42,113 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 21:41:42,113 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:41:51,393 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:41:51,454 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:05,387 - INFO - __main__ - 用户指定批次: [10]
2025-08-01 21:42:05,390 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-08-01 21:42:05,407 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-08-01 21:42:05,408 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-08-01 21:42:05,408 - INFO - __main__ - 开始1000个真实西藏景点同步批量处理测试
2025-08-01 21:42:05,409 - INFO - __main__ - 测试批次: [10]
2025-08-01 21:42:05,417 - INFO - __main__ - 加载真实景点数据: 总数 1000, 真实数据 1000 (100.0%)
2025-08-01 21:42:05,417 - INFO - __main__ - 数据集整体组成:
2025-08-01 21:42:05,418 - INFO - __main__ -   总数: 1000
2025-08-01 21:42:05,418 - INFO - __main__ -   真实数据比例: 100.0%
2025-08-01 21:42:05,418 - INFO - __main__ -   数据源分布: {'现有拉萨林芝数据': 441, '基于真实数据扩展': 385, '新爬取真实数据': 174}
2025-08-01 21:42:05,418 - INFO - __main__ - 
====================================================================================================
2025-08-01 21:42:05,419 - INFO - __main__ - 开始处理真实数据批次 1/1: 10个节点
2025-08-01 21:42:05,419 - INFO - __main__ - 预估API调用: 45 次
2025-08-01 21:42:05,419 - INFO - __main__ - 预估时间: 0.00 小时
2025-08-01 21:42:05,419 - INFO - __main__ - ====================================================================================================
2025-08-01 21:42:05,420 - INFO - __main__ - 开始处理真实数据批次，大小: 10
2025-08-01 21:42:05,420 - INFO - __main__ - 预估API调用次数: 45
2025-08-01 21:42:05,420 - INFO - __main__ - 预估处理时间: 0.00小时
2025-08-01 21:42:05,432 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:42:05,438 - INFO - neo4j_connection - 数据库已清空
2025-08-01 21:42:05,438 - INFO - text_processor - 清空所有节点和关系
2025-08-01 21:42:05,444 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-08-01 21:42:05,453 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-08-01 21:42:05,453 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-08-01 21:42:05,454 - INFO - __main__ - 批次数据组成: 真实数据比例 100.0%
2025-08-01 21:42:05,454 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:05,454 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:05,455 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:05,455 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:11,572 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-08-01 21:42:11,658 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-08-01 21:42:11,659 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:20,064 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:20,132 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-08-01 21:42:27,494 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-08-01 21:42:27,531 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-08-01 21:42:27,531 - INFO - text_processor - 过滤后景点数量: 1
2025-08-01 21:42:27,531 - INFO - __main__ - 进度: 3/10 (30.0%) | 小批次: 1/4 | 已耗时: 0.4分钟 | 预计剩余: 0.9分钟 | 速度: 0.136节点/秒
2025-08-01 21:42:27,531 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:27,532 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:27,532 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:27,532 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:41,849 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-08-01 21:42:41,861 - INFO - text_processor - 成功处理实体: 圣地香都
2025-08-01 21:42:41,862 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:47,288 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-08-01 21:42:47,300 - INFO - text_processor - 成功处理实体: 曲苏
2025-08-01 21:42:47,301 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:42:53,470 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-08-01 21:42:53,484 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-08-01 21:42:53,484 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:42:53,484 - INFO - __main__ - 进度: 6/10 (60.0%) | 小批次: 2/4 | 已耗时: 0.8分钟 | 预计剩余: 0.5分钟 | 速度: 0.125节点/秒
2025-08-01 21:42:53,484 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:42:53,484 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:42:53,484 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:42:53,485 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:00,300 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-08-01 21:43:00,312 - INFO - text_processor - 成功处理实体: 喜德林
2025-08-01 21:43:00,312 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:07,478 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-08-01 21:43:07,489 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-08-01 21:43:07,490 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:12,022 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-08-01 21:43:12,036 - INFO - text_processor - 成功处理实体: 水上乐园
2025-08-01 21:43:12,036 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:43:12,036 - INFO - __main__ - 进度: 9/10 (90.0%) | 小批次: 3/4 | 已耗时: 1.1分钟 | 预计剩余: 0.1分钟 | 速度: 0.135节点/秒
2025-08-01 21:43:12,036 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-08-01 21:43:12,037 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-08-01 21:43:12,037 - INFO - conflict_resolution - ConflictResolver initialized
2025-08-01 21:43:12,037 - WARNING - text_processor - 描述为空，返回默认评论
2025-08-01 21:43:21,338 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-08-01 21:43:21,398 - INFO - text_processor - 成功处理实体: 阿热康参
2025-08-01 21:43:21,399 - INFO - text_processor - 过滤后景点数量: 0
2025-08-01 21:43:21,399 - INFO - __main__ - 进度: 10/10 (100.0%) | 小批次: 4/4 | 已耗时: 1.3分钟 | 预计剩余: 0.0分钟 | 速度: 0.132节点/秒
2025-08-01 21:43:21,400 - INFO - __main__ - 真实数据批次处理完成 - 大小: 10
2025-08-01 21:43:21,400 - INFO - __main__ - 耗时: 0.02小时 (1.3分钟)
2025-08-01 21:43:21,400 - INFO - __main__ - 成功: 10, 失败: 0
2025-08-01 21:43:21,400 - INFO - __main__ - 速度: 0.132节点/秒
2025-08-01 21:43:21,400 - INFO - __main__ - 真实数据比例: 100.0%
2025-08-01 21:43:21,403 - INFO - __main__ - 中间结果已保存: 1 个批次
2025-08-01 21:43:21,404 - INFO - __main__ - 最终结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.json
2025-08-01 21:43:21,421 - INFO - __main__ - CSV结果已保存: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_real_1000_results.csv
2025-08-01 21:43:21,421 - INFO - __main__ - 
========================================================================================================================
2025-08-01 21:43:21,422 - INFO - __main__ - 1000个真实西藏景点同步批量处理测试结果摘要
2025-08-01 21:43:21,422 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,422 - INFO - __main__ - 批次:   10 | 时间:   0.02小时 | 速度:  0.132节点/秒 | 真实数据: 100.0% | 成功:   10
2025-08-01 21:43:21,422 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,423 - INFO - __main__ - 总处理节点数: 10
2025-08-01 21:43:21,423 - INFO - __main__ - 总处理时间: 0.02小时
2025-08-01 21:43:21,423 - INFO - __main__ - 平均处理速度: 0.132节点/秒
2025-08-01 21:43:21,423 - INFO - __main__ - ========================================================================================================================
2025-08-01 21:43:21,424 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-08-01 21:43:21,424 - INFO - text_processor - 关闭资源
