2025-07-23 20:21:59,455 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-07-23 20:21:59,482 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-07-23 20:21:59,483 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-07-23 20:21:59,483 - INFO - __main__ - 开始简化版同步批量处理测试
2025-07-23 20:21:59,484 - INFO - __main__ - 加载数据完成，总节点数: 260, 去重后: 258
2025-07-23 20:21:59,485 - INFO - __main__ - 
==================================================
2025-07-23 20:21:59,485 - INFO - __main__ - 开始处理批次大小: 10
2025-07-23 20:21:59,486 - INFO - __main__ - ==================================================
2025-07-23 20:21:59,486 - INFO - __main__ - 开始简化处理批次，大小: 10
2025-07-23 20:21:59,497 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:21:59,500 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:21:59,504 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:21:59,937 - INFO - __main__ - 已处理 10/10 个节点
2025-07-23 20:21:59,937 - INFO - __main__ - 简化批次处理完成 - 大小: 10, 耗时: 0.43秒, 成功: 10, 失败: 0, 速度: 23.12 节点/秒
2025-07-23 20:22:00,946 - INFO - __main__ - 
==================================================
2025-07-23 20:22:00,946 - INFO - __main__ - 开始处理批次大小: 50
2025-07-23 20:22:00,947 - INFO - __main__ - ==================================================
2025-07-23 20:22:00,947 - INFO - __main__ - 开始简化处理批次，大小: 50
2025-07-23 20:22:00,955 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:22:00,959 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:22:00,962 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:22:01,047 - INFO - __main__ - 已处理 10/50 个节点
2025-07-23 20:22:01,289 - INFO - __main__ - 已处理 20/50 个节点
2025-07-23 20:22:01,454 - INFO - __main__ - 已处理 30/50 个节点
2025-07-23 20:22:01,528 - INFO - __main__ - 已处理 40/50 个节点
2025-07-23 20:22:01,716 - INFO - __main__ - 已处理 50/50 个节点
2025-07-23 20:22:01,716 - INFO - __main__ - 简化批次处理完成 - 大小: 50, 耗时: 0.75秒, 成功: 50, 失败: 0, 速度: 66.38 节点/秒
2025-07-23 20:22:02,725 - INFO - __main__ - 
==================================================
2025-07-23 20:22:02,725 - INFO - __main__ - 开始处理批次大小: 100
2025-07-23 20:22:02,726 - INFO - __main__ - ==================================================
2025-07-23 20:22:02,727 - INFO - __main__ - 开始简化处理批次，大小: 100
2025-07-23 20:22:02,746 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:22:02,749 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:22:02,754 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:22:02,834 - INFO - __main__ - 已处理 10/100 个节点
2025-07-23 20:22:02,905 - INFO - __main__ - 已处理 20/100 个节点
2025-07-23 20:22:02,976 - INFO - __main__ - 已处理 30/100 个节点
2025-07-23 20:22:03,044 - INFO - __main__ - 已处理 40/100 个节点
2025-07-23 20:22:03,108 - INFO - __main__ - 已处理 50/100 个节点
2025-07-23 20:22:03,320 - INFO - __main__ - 已处理 60/100 个节点
2025-07-23 20:22:03,395 - INFO - __main__ - 已处理 70/100 个节点
2025-07-23 20:22:03,464 - INFO - __main__ - 已处理 80/100 个节点
2025-07-23 20:22:03,534 - INFO - __main__ - 已处理 90/100 个节点
2025-07-23 20:22:03,603 - INFO - __main__ - 已处理 100/100 个节点
2025-07-23 20:22:03,604 - INFO - __main__ - 简化批次处理完成 - 大小: 100, 耗时: 0.85秒, 成功: 100, 失败: 0, 速度: 117.67 节点/秒
2025-07-23 20:22:04,611 - INFO - __main__ - 
==================================================
2025-07-23 20:22:04,611 - INFO - __main__ - 开始处理批次大小: 150
2025-07-23 20:22:04,611 - INFO - __main__ - ==================================================
2025-07-23 20:22:04,612 - INFO - __main__ - 开始简化处理批次，大小: 150
2025-07-23 20:22:04,626 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:22:04,630 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:22:04,633 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:22:04,698 - INFO - __main__ - 已处理 10/150 个节点
2025-07-23 20:22:04,772 - INFO - __main__ - 已处理 20/150 个节点
2025-07-23 20:22:04,841 - INFO - __main__ - 已处理 30/150 个节点
2025-07-23 20:22:04,909 - INFO - __main__ - 已处理 40/150 个节点
2025-07-23 20:22:04,978 - INFO - __main__ - 已处理 50/150 个节点
2025-07-23 20:22:05,043 - INFO - __main__ - 已处理 60/150 个节点
2025-07-23 20:22:05,105 - INFO - __main__ - 已处理 70/150 个节点
2025-07-23 20:22:05,172 - INFO - __main__ - 已处理 80/150 个节点
2025-07-23 20:22:05,239 - INFO - __main__ - 已处理 90/150 个节点
2025-07-23 20:22:05,306 - INFO - __main__ - 已处理 100/150 个节点
2025-07-23 20:22:05,376 - INFO - __main__ - 已处理 110/150 个节点
2025-07-23 20:22:05,433 - INFO - __main__ - 已处理 120/150 个节点
2025-07-23 20:22:05,491 - INFO - __main__ - 已处理 130/150 个节点
2025-07-23 20:22:05,550 - INFO - __main__ - 已处理 140/150 个节点
2025-07-23 20:22:05,609 - INFO - __main__ - 已处理 150/150 个节点
2025-07-23 20:22:05,609 - INFO - __main__ - 简化批次处理完成 - 大小: 150, 耗时: 0.98秒, 成功: 150, 失败: 0, 速度: 153.67 节点/秒
2025-07-23 20:22:06,610 - INFO - __main__ - 
==================================================
2025-07-23 20:22:06,610 - INFO - __main__ - 开始处理批次大小: 200
2025-07-23 20:22:06,610 - INFO - __main__ - ==================================================
2025-07-23 20:22:06,611 - INFO - __main__ - 开始简化处理批次，大小: 200
2025-07-23 20:22:06,630 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:22:06,634 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:22:06,637 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:22:06,698 - INFO - __main__ - 已处理 10/200 个节点
2025-07-23 20:22:06,763 - INFO - __main__ - 已处理 20/200 个节点
2025-07-23 20:22:06,826 - INFO - __main__ - 已处理 30/200 个节点
2025-07-23 20:22:06,889 - INFO - __main__ - 已处理 40/200 个节点
2025-07-23 20:22:06,947 - INFO - __main__ - 已处理 50/200 个节点
2025-07-23 20:22:07,008 - INFO - __main__ - 已处理 60/200 个节点
2025-07-23 20:22:07,067 - INFO - __main__ - 已处理 70/200 个节点
2025-07-23 20:22:07,127 - INFO - __main__ - 已处理 80/200 个节点
2025-07-23 20:22:07,193 - INFO - __main__ - 已处理 90/200 个节点
2025-07-23 20:22:07,260 - INFO - __main__ - 已处理 100/200 个节点
2025-07-23 20:22:07,321 - INFO - __main__ - 已处理 110/200 个节点
2025-07-23 20:22:07,377 - INFO - __main__ - 已处理 120/200 个节点
2025-07-23 20:22:07,434 - INFO - __main__ - 已处理 130/200 个节点
2025-07-23 20:22:07,492 - INFO - __main__ - 已处理 140/200 个节点
2025-07-23 20:22:07,559 - INFO - __main__ - 已处理 150/200 个节点
2025-07-23 20:22:07,624 - INFO - __main__ - 已处理 160/200 个节点
2025-07-23 20:22:07,685 - INFO - __main__ - 已处理 170/200 个节点
2025-07-23 20:22:07,748 - INFO - __main__ - 已处理 180/200 个节点
2025-07-23 20:22:07,806 - INFO - __main__ - 已处理 190/200 个节点
2025-07-23 20:22:07,868 - INFO - __main__ - 已处理 200/200 个节点
2025-07-23 20:22:07,869 - INFO - __main__ - 简化批次处理完成 - 大小: 200, 耗时: 1.23秒, 成功: 200, 失败: 0, 速度: 162.50 节点/秒
2025-07-23 20:22:08,880 - INFO - __main__ - 
==================================================
2025-07-23 20:22:08,880 - INFO - __main__ - 开始处理批次大小: 250
2025-07-23 20:22:08,880 - INFO - __main__ - ==================================================
2025-07-23 20:22:08,881 - INFO - __main__ - 开始简化处理批次，大小: 250
2025-07-23 20:22:08,899 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:22:08,901 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:22:08,905 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:22:08,970 - INFO - __main__ - 已处理 10/250 个节点
2025-07-23 20:22:09,034 - INFO - __main__ - 已处理 20/250 个节点
2025-07-23 20:22:09,093 - INFO - __main__ - 已处理 30/250 个节点
2025-07-23 20:22:09,153 - INFO - __main__ - 已处理 40/250 个节点
2025-07-23 20:22:09,212 - INFO - __main__ - 已处理 50/250 个节点
2025-07-23 20:22:09,272 - INFO - __main__ - 已处理 60/250 个节点
2025-07-23 20:22:09,326 - INFO - __main__ - 已处理 70/250 个节点
2025-07-23 20:22:09,388 - INFO - __main__ - 已处理 80/250 个节点
2025-07-23 20:22:09,444 - INFO - __main__ - 已处理 90/250 个节点
2025-07-23 20:22:09,499 - INFO - __main__ - 已处理 100/250 个节点
2025-07-23 20:22:09,555 - INFO - __main__ - 已处理 110/250 个节点
2025-07-23 20:22:09,608 - INFO - __main__ - 已处理 120/250 个节点
2025-07-23 20:22:09,670 - INFO - __main__ - 已处理 130/250 个节点
2025-07-23 20:22:09,727 - INFO - __main__ - 已处理 140/250 个节点
2025-07-23 20:22:09,785 - INFO - __main__ - 已处理 150/250 个节点
2025-07-23 20:22:09,844 - INFO - __main__ - 已处理 160/250 个节点
2025-07-23 20:22:09,907 - INFO - __main__ - 已处理 170/250 个节点
2025-07-23 20:22:09,965 - INFO - __main__ - 已处理 180/250 个节点
2025-07-23 20:22:10,021 - INFO - __main__ - 已处理 190/250 个节点
2025-07-23 20:22:10,075 - INFO - __main__ - 已处理 200/250 个节点
2025-07-23 20:22:10,131 - INFO - __main__ - 已处理 210/250 个节点
2025-07-23 20:22:10,186 - INFO - __main__ - 已处理 220/250 个节点
2025-07-23 20:22:10,245 - INFO - __main__ - 已处理 230/250 个节点
2025-07-23 20:22:10,305 - INFO - __main__ - 已处理 240/250 个节点
2025-07-23 20:22:10,360 - INFO - __main__ - 已处理 250/250 个节点
2025-07-23 20:22:10,360 - INFO - __main__ - 简化批次处理完成 - 大小: 250, 耗时: 1.45秒, 成功: 250, 失败: 0, 速度: 171.91 节点/秒
2025-07-23 20:22:11,373 - INFO - __main__ - 结果已保存到: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_simple_results.json
2025-07-23 20:22:11,386 - INFO - __main__ - 结果已保存到: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_simple_results.csv
2025-07-23 20:22:11,386 - INFO - __main__ - 
======================================================================
2025-07-23 20:22:11,386 - INFO - __main__ - 简化版同步批量处理测试结果摘要
2025-07-23 20:22:11,386 - INFO - __main__ - ======================================================================
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小:  10 | 耗时:   0.43秒 | 速度:  23.12 节点/秒 | 成功:  10 | 失败:   0
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小:  50 | 耗时:   0.75秒 | 速度:  66.38 节点/秒 | 成功:  50 | 失败:   0
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小: 100 | 耗时:   0.85秒 | 速度: 117.67 节点/秒 | 成功: 100 | 失败:   0
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小: 150 | 耗时:   0.98秒 | 速度: 153.67 节点/秒 | 成功: 150 | 失败:   0
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小: 200 | 耗时:   1.23秒 | 速度: 162.50 节点/秒 | 成功: 200 | 失败:   0
2025-07-23 20:22:11,387 - INFO - __main__ - 批次大小: 250 | 耗时:   1.45秒 | 速度: 171.91 节点/秒 | 成功: 250 | 失败:   0
2025-07-23 20:22:11,388 - INFO - __main__ - ======================================================================
2025-07-23 20:22:11,388 - INFO - neo4j_connection - Neo4j 连接已关闭
