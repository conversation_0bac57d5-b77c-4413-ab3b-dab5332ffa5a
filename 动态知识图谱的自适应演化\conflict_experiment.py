# 冲突解决策略的实验评估
import logging
import time
from typing import Dict, List
from neo4j_connection import Neo4jConnection
from config import Config
from knowledge_graph_updater import KnowledgeGraphUpdater
from conflict_resolution import ConflictResolver

logger = logging.getLogger(__name__)

def run_conflict_resolution_experiment():
    """运行冲突解决策略实验评估"""
    neo4j_conn = None
    try:
        neo4j_config = Config.get_neo4j_config()
        neo4j_conn = Neo4jConnection(
            uri=neo4j_config["uri"],
            user=neo4j_config["user"],
            password=neo4j_config["password"]
        )
        neo4j_conn.verify_connectivity()
        logger.info("成功连接到Neo4j数据库")

        updater = KnowledgeGraphUpdater(neo4j_conn)
        conflict_resolver = ConflictResolver(updater.crud)

        # 模拟冲突数据
        conflict_data = [
            {
                "entity_name": "布达拉宫_relationship",
                "conflict_data": {
                    "source_name": "布达拉宫",
                    "target_name": "大昭寺",
                    "type": "NEARBY",
                    "properties": {"reason": "模拟冲突: 地理位置相近", "confidence": 0.8}
                },
                "existing_data": [{
                    "source_name": "布达拉宫",
                    "target_name": "大昭寺",
                    "type": "NEARBY",
                    "properties": {"reason": "现有关系: 同一区域", "confidence": 0.9}
                }],
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S+08:00")
            },
            {
                "entity_name": "大昭寺_relationship",
                "conflict_data": {
                    "source_name": "大昭寺",
                    "target_name": "纳木措",
                    "type": "COMPLEMENTARY_VISIT",
                    "properties": {"reason": "模拟冲突: 互补游览", "confidence": 0.7}
                },
                "existing_data": [{
                    "source_name": "大昭寺",
                    "target_name": "纳木措",
                    "type": "COMPLEMENTARY_VISIT",
                    "properties": {"reason": "现有关系: 适合一起游览", "confidence": 0.85}
                }],
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S+08:00")
            }
        ]

        # 记录冲突到队列
        for conflict in conflict_data:
            conflict_resolver.log_conflict(conflict)

        # 运行冲突解决
        logger.info("开始冲突解决实验...")
        resolved_count = 0
        for conflict in conflict_data:
            try:
                resolved = conflict_resolver.resolve_conflict(conflict)
                logger.info(f"解决冲突: {resolved['entity_name']}")
                resolved_count += 1
            except Exception as e:
                logger.error(f"解决冲突失败: {conflict['entity_name']}, 错误: {e}")
                continue

        logger.info(f"冲突解决实验完成，共处理 {resolved_count} 个冲突")
    except Exception as e:
        logger.error(f"冲突解决实验失败: {e}")
        raise
    finally:
        if neo4j_conn:
            neo4j_conn.close()