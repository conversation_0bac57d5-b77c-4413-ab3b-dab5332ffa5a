#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JSON数据文件中的节点数量
"""

import json
import os

def check_data_count():
    """检查数据数量"""
    project_dir = os.path.dirname(os.path.abspath(__file__))
    json_file_path = os.path.join(project_dir, "data", "lhasa_knowledge_graph.json")
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        nodes = data.get("nodes", [])
        relationships = data.get("relationships", [])
        
        print(f"JSON文件路径: {json_file_path}")
        print(f"总节点数: {len(nodes)}")
        print(f"总关系数: {len(relationships)}")
        
        # 去重检查
        seen_names = set()
        duplicates = []
        unique_nodes = []
        
        for node in nodes:
            name = node.get("name", "")
            if name in seen_names:
                duplicates.append(name)
            else:
                seen_names.add(name)
                unique_nodes.append(node)
        
        print(f"去重后节点数: {len(unique_nodes)}")
        print(f"重复节点数: {len(duplicates)}")
        
        if duplicates:
            print(f"重复节点示例: {duplicates[:5]}")
        
        # 显示前几个节点的信息
        print("\n前5个节点信息:")
        for i, node in enumerate(nodes[:5]):
            print(f"{i+1}. {node.get('name', 'N/A')} - {node.get('location', 'N/A')}")
        
        # 检查是否有足够的数据进行分组测试
        batch_sizes = [10, 50, 100, 150, 200, 250]
        max_batch = max(batch_sizes)
        
        print(f"\n分组测试需求:")
        print(f"最大批次大小: {max_batch}")
        print(f"当前数据量: {len(unique_nodes)}")
        
        if len(unique_nodes) >= max_batch:
            print("✓ 数据量充足，可以进行所有分组测试")
        else:
            print("⚠ 数据量不足，需要调整测试批次大小")
            available_batches = [size for size in batch_sizes if size <= len(unique_nodes)]
            print(f"可用的批次大小: {available_batches}")
        
    except FileNotFoundError:
        print(f"文件不存在: {json_file_path}")
    except json.JSONDecodeError as e:
        print(f"JSON格式错误: {e}")
    except Exception as e:
        print(f"检查数据时发生错误: {e}")

if __name__ == "__main__":
    check_data_count()
