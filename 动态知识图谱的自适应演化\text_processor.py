# 负责文本处理和 LLM 管道逻辑
from datetime import datetime
import json
import logging
import os
from pathlib import Path
import time
import requests
from typing import Dict, List
from time_converter import convert_to_beijing_time
from neo4j_connection import Neo4jConnection
from config import Config
from utils import normalize_location
from conflict_resolution import ConflictResolver
from knowledge_graph_updater import KnowledgeGraphUpdater

logger = logging.getLogger(__name__)

PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))

llm_log_file = os.path.join(PROJECT_DIR, "llm_response_log.json")
with open(llm_log_file, "a", encoding="utf-8") as f:
    f.write("")

# Cache for LLM descriptions
description_cache = {}


def call_deepseek_with_retry(prompt: str, model_name: str = "default") -> str:
    """同步调用 DeepSeek API"""
    config = Config.get_llm_config(model_name)
    input_tokens = estimate_tokens(prompt)
    max_model_tokens = 16384
    max_output_tokens = 1024
    if input_tokens >= max_model_tokens - max_output_tokens:
        logger.warning(f"Prompt 过长 ({input_tokens} tokens)，截断到安全长度")
        prompt = prompt[:int(len(prompt) * (max_model_tokens - max_output_tokens - 100) / input_tokens)]
        input_tokens = estimate_tokens(prompt)

    logger.debug(f"输入 token 数: {input_tokens}, max_tokens: {max_output_tokens}")

    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": config["model"],
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": max_output_tokens
    }
    for attempt in range(config["max_retries"]):
        try:
            response = requests.post(config["api_base"], headers=headers, json=payload, timeout=config["timeout"])
            response.raise_for_status()
            response_data = response.json()
            with open(llm_log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(
                    {
                        "prompt": prompt,
                        "response": response_data,
                        "input_tokens": input_tokens,
                        "timestamp": convert_to_beijing_time()
                    },
                    ensure_ascii=False
                ) + "\n")
            return json.dumps(response_data)
        except requests.exceptions.RequestException as e:
            if attempt == config["max_retries"] - 1:
                logger.error(f"LLM 调用失败 after {config['max_retries']} retries: {e}")
                return "{}"  # 返回空响应以继续流程
            time.sleep(2 ** attempt)
    return "{}"


def estimate_tokens(text: str) -> int:
    """估算文本的 token 数（简化为 1 token ≈ 0.75 个中文字符或 0.5 个英文字符）"""
    chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
    other_chars = len(text) - chinese_chars
    return int(chinese_chars * 0.75 + other_chars * 0.5)


def infer_description(name: str, location: str) -> str:
    """使用 LLM 生成描述"""
    if not name or not location:
        logger.warning(f"无效输入: name={name}, location={location}, 返回默认描述")
        return f"Description for {name or 'Unknown'}"
    prompt = f"Generate a brief description (50-100 words) for a tourist attraction named '{name}' located in '{location}'. The description should highlight its cultural, historical, or natural significance."
    try:
        response = call_deepseek_with_retry(prompt, model_name="default")
        description = json.loads(response).get("choices", [{}])[0].get("message", {}).get("content", "")
        return description.strip()
    except Exception as e:
        logger.error(f"生成描述失败 for {name}: {e}")
        return f"Description for {name}"


def reset_database(neo4j_conn):
    """
    Reset the Neo4j database by clearing all nodes and relationships
    and creating indexes for Attraction and City nodes.
    """
    try:
        neo4j_conn.clear_database()
        logger.info("清空所有节点和关系")

        with neo4j_conn.driver.session() as session:
            session.run("CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)")
            logger.debug("执行查询: CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)")
            session.run("CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)")
            logger.debug("执行查询: CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)")

        logger.info("创建 Attraction 和 City 名称索引")
    except Exception as e:
        logger.error(f"重置数据库失败: {e}", exc_info=True)
        raise


def extract_best_comment(description: str, debug_mode: bool = False) -> str:
    """使用 LLM 提取最佳评论"""
    if not description:
        logger.warning("描述为空，返回默认评论")
        return "Sample comment..."
    if debug_mode:
        return f"Sample comment for {description[:20]}..."
    prompt = f"Generate a concise, positive visitor comment (20-50 words) for a tourist attraction based on this description: {description}"
    try:
        response = call_deepseek_with_retry(prompt, model_name="default")
        comment = json.loads(response).get("choices", [{}])[0].get("message", {}).get("content", "")
        return comment.strip()
    except Exception as e:
        logger.error(f"生成评论失败: {e}")
        return ""


def batch_call_deepseek(prompts: List[str], model_name: str = "default") -> List[str]:
    """同步批量调用 DeepSeek API，按 token 限制分块处理"""
    max_model_tokens = 16384
    max_output_tokens = 1024
    max_input_tokens = max_model_tokens - max_output_tokens - 100

    chunks = []
    current_chunk = []
    current_token_count = 0

    for prompt in prompts:
        prompt_tokens = estimate_tokens(prompt)
        if current_token_count + prompt_tokens > max_input_tokens:
            chunks.append(current_chunk)
            current_chunk = [prompt]
            current_token_count = prompt_tokens
        else:
            current_chunk.append(prompt)
            current_token_count += prompt_tokens
    if current_chunk:
        chunks.append(current_chunk)

    logger.debug(f"分块数量: {len(chunks)}, 总 prompts: {len(prompts)}")

    results = []
    for i, chunk in enumerate(chunks):
        logger.debug(f"处理第 {i + 1} 块，包含 {len(chunk)} 个 prompts")
        for prompt in chunk:
            try:
                result = call_deepseek_with_retry(prompt, model_name)
                results.append(result)
            except Exception as e:
                logger.error(f"批量调用失败 for prompt: {prompt[:50]}...: {e}")
                results.append(str(e))
                continue
        time.sleep(1)  # 模拟异步的速率限制

    for i, result in enumerate(results):
        if isinstance(result, str) and result.startswith("Client error"):
            logger.warning(f"批量调用结果 {i} 失败: {result}")

    return results


def extract_relationships(data: Dict) -> Dict:
    """增强数据，生成描述和评论"""
    if not data or not isinstance(data, dict):
        logger.error("无效数据输入，跳过处理")
        return {}
    data = data.copy()
    if not data.get("best_comment"):
        data["best_comment"] = extract_best_comment(data.get("description", ""), debug_mode=False)
    if not data.get("location"):
        data["location"] = "拉萨市"
    if not data.get("description"):
        data["description"] = infer_description(data.get("name", "Unknown"), data.get("location", "拉萨市"))

    cultural_keywords = ["寺", "庙", "宫", "博物馆", "文化", "宗教", "历史", "古街", "遗址", "纪念碑"]
    is_cultural = any(
        keyword in data.get("name", "") or keyword in data.get("description", "") for keyword in cultural_keywords)
    data["is_cultural"] = is_cultural
    logger.debug(
        f"LLM 处理数据: {data['name']}, is_cultural: {is_cultural}, location: {data['location']}, description: {data['description']}")
    return data


def process_json_chunk(neo4j_conn: Neo4jConnection, data: List[Dict], crawl_timestamp: str, source_type: str,
                       metrics: Dict):
    if not data or not isinstance(data, list):
        logger.error("数据为空或格式无效，跳过处理")
        return []

    # 确保传入的是 Neo4jConnection，然后创建 Updater
    updater = KnowledgeGraphUpdater(neo4j_conn)
    conflict_resolver = ConflictResolver(updater.crud)
    results = []

    # 处理节点
    for item in data:
        try:
            if not item.get("name"):
                logger.warning(f"跳过无效数据，缺少name: {item}")
                continue
            processed_item = extract_relationships(item)
            log_id = f"{processed_item['name']}_{crawl_timestamp}"
            weights = {"rules_valid": 1.0, "llm_valid": 0.8, "weight_valid": 0.9}
            reason = "Initial import from JSON with LLM enhancement"
            updater.update_knowledge_graph(processed_item, log_id, reason, weights)
            logger.info(f"成功处理实体: {processed_item['name']}")
            results.append({"name": processed_item["name"], "status": "success"})
        except Exception as e:
            logger.error(f"处理实体 {item.get('name', 'Unknown')} 失败: {e}", exc_info=True)
            results.append({"name": item.get("name", "Unknown"), "status": "failed", "error": str(e)})
            continue

    # 过滤景点
    filtered_data = [item for item in data if float(item.get("visitor_percentage", "0%").strip("%")) > 0]
    logger.info(f"过滤后景点数量: {len(filtered_data)}")

    # 批量推断关系
    relationship_prompts = []
    node_pairs = []
    for i, node1 in enumerate(filtered_data):
        for node2 in filtered_data[i + 1:]:
            if node1.get("name") != node2.get("name"):
                prompt = f"""
                你是一个专业的旅游景点关系分析专家。请分析以下两个西藏景点之间可能存在的关系，并返回JSON格式的关系列表。

                景点1:
                名称: {node1.get('name', '')}
                位置: {node1.get('location', '')}
                描述: {node1.get('description', '')}
                地址: {node1.get('address', '')}

                景点2:
                名称: {node2.get('name', '')}
                位置: {node2.get('location', '')}
                描述: {node2.get('description', '')}
                地址: {node2.get('address', '')}

                请根据以下关系类型分析：
                1. NEARBY - 地理位置相近（同一区域或相邻区域）
                2. SIMILAR_TYPE - 相似类型（都是寺庙、都是自然景观等）
                3. COMPLEMENTARY_VISIT - 互补游览（适合一起游览的景点）
                4. HISTORICAL_LINK - 历史关联（有共同的历史背景或文化联系）
                5. CULTURAL_RELATED - 文化相关（属于同一文化体系或宗教体系）

                返回JSON格式，每个关系包含：
                - type: 关系类型
                - reason: 关系原因（中文）
                - confidence: 置信度（0.0-1.0）
                - direction: 关系方向（"bidirectional"表示双向，"forward"表示单向）

                示例格式：
                [
                    {{"type": "NEARBY", "reason": "两个景点都位于拉萨市中心区域", "confidence": 0.8, "direction": "bidirectional"}},
                    {{"type": "CULTURAL_RELATED", "reason": "都是藏传佛教寺庙", "confidence": 0.9, "direction": "bidirectional"}}
                ]

                如果没有明显关系，返回空数组 []
                """
                relationship_prompts.append(prompt)
                node_pairs.append((node1, node2))

    responses = batch_call_deepseek(relationship_prompts)

    # 创建关系并检测冲突
    for (node1, node2), response in zip(node_pairs, responses):
        try:
            if isinstance(response, Exception):
                logger.error(f"LLM 调用失败 for {node1['name']} -> {node2['name']}: {response}")
                continue
            # 清理响应，移除可能的代码块标记
            content = json.loads(response).get("choices", [{}])[0].get("message", {}).get("content", "[]")
            if content.startswith("```json"):
                content = content.strip("```json\n").strip("\n```")
            elif content.startswith("```"):
                content = content.strip("```\n").strip("\n```")

            # 尝试解析 JSON
            try:
                relationships = json.loads(content) if isinstance(content, str) else []
            except json.JSONDecodeError as e:
                logger.error(f"JSON 解析失败 for {node1['name']} -> {node2['name']}: {e}, content: {content}")
                continue

            existing_rels = updater.crud.get_relationships("Attraction", node1["name"])

            for rel in relationships:
                if isinstance(rel, dict) and all(k in rel for k in ["type", "reason", "confidence"]):
                    rel_data = {
                        "source_name": node1["name"],
                        "target_name": node2["name"],
                        "type": rel["type"],
                        "properties": {"reason": rel["reason"], "confidence": rel["confidence"]}
                    }
                    if conflict_resolver.check_relationship_conflict(rel_data, existing_rels):
                        continue

                    updater.crud.create_relationship(
                        source_label="Attraction",
                        source_name=node1["name"],
                        target_label="Attraction",
                        target_name=node2["name"],
                        rel_type=rel["type"],
                        properties={"reason": rel["reason"], "confidence": rel["confidence"]}
                    )
                    logger.debug(f"创建 {rel['type']} 关系: {node1['name']} -> {node2['name']}")
        except Exception as e:
            logger.error(f"创建关系失败: {node1['name']} -> {node2['name']}: {str(e)}")

    return results


def process_json_files(neo4j_conn, json_file_path: str, crawl_timestamp: str, source_type: str, metrics: Dict):
    logger.info(f"读取 JSON 文件: {json_file_path}")
    try:
        file_path = Path(json_file_path)
        if file_path.is_dir():
            raise ValueError(f"路径是目录而非文件: {json_file_path}")
        if not file_path.is_file():
            raise FileNotFoundError(f"文件不存在: {json_file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        nodes = data.get("nodes", [])
        if not nodes:
            logger.warning("JSON 文件中无节点数据")
            return {"status": "skipped", "message": "无节点数据"}
        logger.info(f"JSON 文件包含 {len(nodes)} 条记录")

        seen_names = set()
        duplicates = []
        unique_nodes = []
        for node in nodes:
            if node["name"] in seen_names:
                duplicates.append(node["name"])
            else:
                seen_names.add(node["name"])
                unique_nodes.append(node)
        logger.info(f"去重后节点数: {len(unique_nodes)}, 重复节点: {duplicates if duplicates else '无'}")

        # 可以通过参数控制处理的节点数量，默认处理所有节点
        # unique_nodes = unique_nodes[:50]  # 注释掉硬编码限制
        logger.info(f"准备处理节点数: {len(unique_nodes)}")

        batch_size = 3
        results = []
        for i in range(0, len(unique_nodes), batch_size):
            batch = unique_nodes[i:i + batch_size]
            batch_result = process_json_chunk(
                neo4j_conn=neo4j_conn,
                data=batch,
                crawl_timestamp=crawl_timestamp,
                source_type=source_type,
                metrics=metrics
            )
            results.extend(batch_result)

        return {
            "status": "success",
            "processed": len([r for r in results if r["status"] == "success"]),
            "failed": len([r for r in results if r["status"] == "failed"]),
            "details": results
        }
    except Exception as e:
        logger.error(f"处理 JSON 文件失败: {e}", exc_info=True)
        return {"status": "failed", "error": str(e)}


def close_resources():
    """
    Close any resources if needed.
    """
    logger.info("关闭资源")