#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步批量处理性能可视化脚本
生成多种图表展示不同批次大小的性能数据，用于与异步版本对比
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SyncPerformanceVisualizer:
    """同步性能可视化器"""
    
    def __init__(self):
        # 完整的实验数据
        self.data = {
            'batch_size': [10, 50, 100, 150, 200, 250],
            'duration_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
            'duration_minutes': [1.97, 9.27, 20.12, 26.15, 31.82, 36.12],
            'nodes_per_second': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
            'success_count': [10, 50, 100, 150, 200, 250],
            'failed_count': [0, 0, 0, 0, 0, 0]
        }
        
        self.df = pd.DataFrame(self.data)
        
        # 创建输出目录
        self.output_dir = "sync_charts"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_processing_time_chart(self):
        """创建处理时间对比图（以秒为主要单位）"""
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))

        # 处理时间(秒) - 主图
        bars = ax.bar(self.df['batch_size'], self.df['duration_seconds'],
                     color='steelblue', alpha=0.8, edgecolor='navy', linewidth=2)
        ax.set_xlabel('批次大小 (节点数)', fontsize=14, fontweight='bold')
        ax.set_ylabel('处理时间 (秒)', fontsize=14, fontweight='bold')
        ax.set_title('同步处理 - 各批次处理时间对比', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=0.3)

        # 添加数值标签（秒和分钟双标注）
        for i, bar in enumerate(bars):
            height = bar.get_height()
            seconds = height
            minutes = height / 60
            ax.text(bar.get_x() + bar.get_width()/2., height + 30,
                    f'{seconds:.0f}s\n({minutes:.1f}min)',
                    ha='center', va='bottom', fontweight='bold', fontsize=11)

        # 设置Y轴刻度，显示对应的分钟数
        ax2 = ax.twinx()
        ax2.set_ylabel('处理时间 (分钟)', fontsize=14, fontweight='bold')
        ax2.set_ylim(ax.get_ylim()[0]/60, ax.get_ylim()[1]/60)

        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/processing_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 处理时间对比图已生成（以秒为主单位）")
    
    def create_processing_speed_chart(self):
        """创建处理速度图"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 创建折线图
        line = ax.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                      marker='o', linewidth=3, markersize=10, 
                      color='red', markerfacecolor='darkred', markeredgecolor='white', markeredgewidth=2)
        
        # 添加数据点标签
        for i, (x, y) in enumerate(zip(self.df['batch_size'], self.df['nodes_per_second'])):
            ax.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                       xytext=(0,15), ha='center', fontweight='bold', fontsize=11)
        
        ax.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax.set_ylabel('处理速度 (节点/秒)', fontsize=12, fontweight='bold')
        ax.set_title('同步处理 - 各批次处理速度对比', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 添加平均线
        avg_speed = self.df['nodes_per_second'].mean()
        ax.axhline(y=avg_speed, color='orange', linestyle='--', linewidth=2, 
                  label=f'平均速度: {avg_speed:.3f} 节点/秒')
        ax.legend(fontsize=11)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/processing_speed_trend.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 处理速度趋势图已生成")
    
    def create_comprehensive_dashboard(self):
        """创建综合仪表板（以秒为主要单位）"""
        fig = plt.figure(figsize=(20, 12))

        # 创建网格布局
        gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3)

        # 1. 处理时间柱状图（秒）
        ax1 = fig.add_subplot(gs[0, 0])
        bars1 = ax1.bar(self.df['batch_size'], self.df['duration_seconds'],
                       color='steelblue', alpha=0.8)
        ax1.set_title('处理时间 (秒)', fontweight='bold')
        ax1.set_xlabel('批次大小')
        ax1.set_ylabel('时间 (秒)')
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 30,
                    f'{height:.0f}s', ha='center', va='bottom', fontsize=9)
        
        # 2. 处理速度折线图
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                marker='o', linewidth=2, markersize=6, color='red')
        ax2.set_title('处理速度 (节点/秒)', fontweight='bold')
        ax2.set_xlabel('批次大小')
        ax2.set_ylabel('速度 (节点/秒)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 成功率饼图
        ax3 = fig.add_subplot(gs[0, 2])
        total_success = sum(self.df['success_count'])
        total_failed = sum(self.df['failed_count'])
        ax3.pie([total_success], labels=['成功'], 
               colors=['lightgreen'], autopct='%1.1f%%', startangle=90)
        ax3.set_title('总体成功率: 100%', fontweight='bold')
        
        # 4. 时间增长趋势
        ax4 = fig.add_subplot(gs[1, :2])
        ax4.plot(self.df['batch_size'], self.df['duration_seconds'], 
                marker='s', linewidth=3, markersize=8, color='purple')
        ax4.set_title('处理时间增长趋势', fontweight='bold')
        ax4.set_xlabel('批次大小 (节点数)')
        ax4.set_ylabel('处理时间 (秒)')
        ax4.grid(True, alpha=0.3)
        
        # 5. 性能指标表格
        ax5 = fig.add_subplot(gs[1, 2])
        ax5.axis('tight')
        ax5.axis('off')
        
        table_data = []
        for i, row in self.df.iterrows():
            table_data.append([
                f"{row['batch_size']}",
                f"{row['duration_seconds']:.0f}s",
                f"{row['nodes_per_second']:.3f}",
                f"100%"
            ])

        table = ax5.table(cellText=table_data,
                         colLabels=['批次', '时间(秒)', '速度', '成功率'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.2, 1.5)
        ax5.set_title('性能指标汇总', fontweight='bold')
        
        # 添加总标题
        fig.suptitle('同步批量处理性能综合仪表板', fontsize=16, fontweight='bold', y=0.98)
        
        plt.savefig(f'{self.output_dir}/comprehensive_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 综合仪表板已生成")
    
    def create_comparison_template(self):
        """创建对比模板图表（以秒为主要单位）"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 处理时间对比 (为异步数据预留空间) - 使用秒
        x = np.arange(len(self.df['batch_size']))
        width = 0.35

        bars1 = ax1.bar(x - width/2, self.df['duration_seconds'], width,
                       label='同步处理', color='steelblue', alpha=0.8)
        # 预留异步数据位置
        bars2 = ax1.bar(x + width/2, [0]*len(x), width,
                       label='异步处理 (待测)', color='lightcoral', alpha=0.8)

        # 添加数值标签
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 20,
                    f'{height:.0f}s', ha='center', va='bottom', fontsize=9)

        ax1.set_xlabel('批次大小 (节点数)')
        ax1.set_ylabel('处理时间 (秒)')
        ax1.set_title('处理时间对比 (秒)')
        ax1.set_xticks(x)
        ax1.set_xticklabels(self.df['batch_size'])
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 处理速度对比
        ax2.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                marker='o', linewidth=2, label='同步处理', color='steelblue')
        # 预留异步数据位置
        ax2.plot(self.df['batch_size'], [0]*len(self.df['batch_size']), 
                marker='s', linewidth=2, label='异步处理 (待测)', color='lightcoral')
        
        ax2.set_xlabel('批次大小 (节点数)')
        ax2.set_ylabel('处理速度 (节点/秒)')
        ax2.set_title('处理速度对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 扩展性对比
        ax3.plot(self.df['batch_size'], self.df['duration_seconds'], 
                marker='o', linewidth=2, label='同步处理', color='steelblue')
        ax3.plot(self.df['batch_size'], [0]*len(self.df['batch_size']), 
                marker='s', linewidth=2, label='异步处理 (待测)', color='lightcoral')
        
        ax3.set_xlabel('批次大小 (节点数)')
        ax3.set_ylabel('处理时间 (秒)')
        ax3.set_title('扩展性对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 效率提升预期
        ax4.bar(self.df['batch_size'], [1]*len(self.df['batch_size']), 
               label='同步处理 (基准)', color='steelblue', alpha=0.8)
        ax4.bar(self.df['batch_size'], [0]*len(self.df['batch_size']), 
               label='异步处理 (待测)', color='lightcoral', alpha=0.8)
        
        ax4.set_xlabel('批次大小 (节点数)')
        ax4.set_ylabel('相对性能倍数')
        ax4.set_title('性能提升倍数对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('同步 vs 异步处理性能对比模板', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/comparison_template.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 对比模板图已生成")
    
    def generate_all_charts(self):
        """生成所有图表"""
        print("🎨 开始生成同步性能可视化图表...")
        
        self.create_processing_time_chart()
        self.create_processing_speed_chart()
        self.create_comprehensive_dashboard()
        self.create_comparison_template()
        
        print(f"\n✅ 所有图表已生成完成！保存在 '{self.output_dir}' 目录中")
        print("\n📁 生成的文件:")
        for file in os.listdir(self.output_dir):
            if file.endswith('.png'):
                print(f"   - {file}")

def main():
    """主函数"""
    visualizer = SyncPerformanceVisualizer()
    visualizer.generate_all_charts()

if __name__ == "__main__":
    main()
