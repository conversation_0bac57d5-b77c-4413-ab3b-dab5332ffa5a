# Neo4j 数据库连接模块
import logging
from neo4j import GraphDatabase

logger = logging.getLogger(__name__)

class Neo4jConnection:
    def __init__(self, uri: str, user: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        logger.info(f"初始化 Neo4j 连接: {uri}")

    def verify_connectivity(self):
        try:
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info("Neo4j 连接验证成功")
        except Exception as e:
            logger.error(f"Neo4j 连接失败: {e}")
            raise

    def clear_database(self):
        try:
            with self.driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
            raise

    def close(self):
        if self.driver:
            self.driver.close()
            logger.info("Neo4j 连接已关闭")