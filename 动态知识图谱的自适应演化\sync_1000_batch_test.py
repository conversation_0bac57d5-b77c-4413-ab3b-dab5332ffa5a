#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1000个景点同步批量处理测试脚本
按照指定的分组大小进行测试，记录详细的性能数据
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

# 导入项目模块
from config import Config
from neo4j_connection import Neo4jConnection
from text_processor import process_json_chunk, reset_database, close_resources

# 设置日志
PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE = os.path.join(PROJECT_DIR, 'sync_1000_batch_test.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE, encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class Sync1000BatchTester:
    """1000个景点同步批量测试器"""
    
    def __init__(self):
        self.neo4j_conn = None
        self.results = []
        # 扩展的批次大小，包含1000个节点的完整测试
        self.batch_sizes = [10, 50, 100, 150, 200, 250, 300, 400, 500, 1000]
        
    def setup_neo4j_connection(self):
        """建立Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info(f"成功连接到 Neo4j: {neo4j_config['uri']}")
            return True
        except Exception as e:
            logger.error(f"Neo4j连接失败: {e}")
            return False
    
    def load_json_data(self, json_file_path: str) -> List[Dict]:
        """加载1000个景点的JSON数据"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            nodes = data.get("nodes", [])
            if not nodes:
                logger.warning("JSON文件中无节点数据")
                return []
            
            # 去重处理
            seen_names = set()
            unique_nodes = []
            for node in nodes:
                if node["name"] not in seen_names:
                    seen_names.add(node["name"])
                    unique_nodes.append(node)
            
            logger.info(f"加载1000景点数据完成，总节点数: {len(nodes)}, 去重后: {len(unique_nodes)}")
            return unique_nodes
            
        except Exception as e:
            logger.error(f"加载JSON数据失败: {e}")
            return []
    
    def estimate_processing_time(self, batch_size: int) -> Dict:
        """预估处理时间"""
        api_calls = batch_size * (batch_size - 1) // 2
        avg_api_time = 0.12  # 基于之前测试的平均时间
        
        estimated_seconds = api_calls * avg_api_time
        estimated_minutes = estimated_seconds / 60
        estimated_hours = estimated_minutes / 60
        
        return {
            "api_calls": api_calls,
            "estimated_seconds": estimated_seconds,
            "estimated_minutes": estimated_minutes,
            "estimated_hours": estimated_hours
        }
    
    def process_batch_sync(self, nodes: List[Dict], batch_size: int, 
                          crawl_timestamp: str, source_type: str, metrics: Dict) -> Dict:
        """同步处理指定大小的批次"""
        logger.info(f"开始处理批次，大小: {batch_size}")
        
        # 预估时间
        estimation = self.estimate_processing_time(batch_size)
        logger.info(f"预估API调用次数: {estimation['api_calls']:,}")
        logger.info(f"预估处理时间: {estimation['estimated_hours']:.1f}小时 ({estimation['estimated_minutes']:.1f}分钟)")
        
        # 清空数据库
        self.neo4j_conn.clear_database()
        reset_database(self.neo4j_conn)
        
        # 取指定数量的节点
        batch_nodes = nodes[:batch_size]
        
        start_time = time.perf_counter()
        start_datetime = datetime.now()
        
        try:
            # 分小批次处理（每次3个节点）
            small_batch_size = 3
            results = []
            processed_count = 0
            failed_count = 0
            
            total_small_batches = (len(batch_nodes) + small_batch_size - 1) // small_batch_size
            
            for i in range(0, len(batch_nodes), small_batch_size):
                small_batch = batch_nodes[i:i + small_batch_size]
                current_batch_num = i // small_batch_size + 1
                
                batch_result = process_json_chunk(
                    neo4j_conn=self.neo4j_conn,
                    data=small_batch,
                    crawl_timestamp=crawl_timestamp,
                    source_type=source_type,
                    metrics=metrics
                )
                
                results.extend(batch_result)
                
                # 统计结果
                for result in batch_result:
                    if result["status"] == "success":
                        processed_count += 1
                    else:
                        failed_count += 1
                
                # 计算进度和预估剩余时间
                current_processed = min(i + small_batch_size, len(batch_nodes))
                elapsed = time.perf_counter() - start_time
                progress = current_processed / len(batch_nodes)
                
                if progress > 0:
                    estimated_total_time = elapsed / progress
                    remaining_time = estimated_total_time - elapsed
                    speed = current_processed / elapsed if elapsed > 0 else 0
                    
                    logger.info(f"进度: {current_processed}/{len(batch_nodes)} ({progress*100:.1f}%) | "
                               f"小批次: {current_batch_num}/{total_small_batches} | "
                               f"已耗时: {elapsed:.0f}秒 | "
                               f"预计剩余: {remaining_time:.0f}秒 | "
                               f"速度: {speed:.3f}节点/秒")
                
                # 每50个节点保存一次中间结果
                if current_processed % 50 == 0:
                    self.save_intermediate_progress(batch_size, current_processed, elapsed, speed)
            
            end_time = time.perf_counter()
            end_datetime = datetime.now()
            duration = end_time - start_time
            
            result = {
                "batch_size": batch_size,
                "actual_processed": len(batch_nodes),
                "success_count": processed_count,
                "failed_count": failed_count,
                "start_time": start_datetime.isoformat(),
                "end_time": end_datetime.isoformat(),
                "duration_seconds": duration,
                "duration_minutes": duration / 60,
                "duration_hours": duration / 3600,
                "nodes_per_second": len(batch_nodes) / duration if duration > 0 else 0,
                "api_calls_estimated": estimation['api_calls'],
                "avg_time_per_api_call": duration / estimation['api_calls'] if estimation['api_calls'] > 0 else 0,
                "status": "success",
                "dataset": "tibet_1000_nodes"
            }
            
            logger.info(f"批次处理完成 - 大小: {batch_size}")
            logger.info(f"耗时: {duration:.0f}秒 ({duration/60:.1f}分钟 / {duration/3600:.1f}小时)")
            logger.info(f"成功: {processed_count}, 失败: {failed_count}")
            logger.info(f"速度: {result['nodes_per_second']:.3f}节点/秒")
            logger.info(f"平均API调用时间: {result['avg_time_per_api_call']:.3f}秒")
            
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            logger.error(f"批次处理失败: {e}")
            return {
                "batch_size": batch_size,
                "actual_processed": 0,
                "success_count": 0,
                "failed_count": batch_size,
                "start_time": start_datetime.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": duration,
                "duration_minutes": duration / 60,
                "duration_hours": duration / 3600,
                "nodes_per_second": 0,
                "status": "failed",
                "error": str(e),
                "dataset": "tibet_1000_nodes"
            }
    
    def save_intermediate_progress(self, batch_size: int, processed: int, elapsed: float, speed: float):
        """保存中间进度"""
        try:
            progress_data = {
                "batch_size": batch_size,
                "processed": processed,
                "elapsed_seconds": elapsed,
                "speed_nodes_per_sec": speed,
                "timestamp": datetime.now().isoformat(),
                "progress_percentage": (processed / batch_size) * 100
            }
            
            progress_file = f"progress_batch_{batch_size}.json"
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存中间进度失败: {e}")
    
    def run_selected_batches(self, json_file_path: str, selected_batches: List[int] = None):
        """运行选定的批次测试"""
        if selected_batches:
            self.batch_sizes = selected_batches
        
        logger.info(f"开始1000个景点的同步批量处理测试")
        logger.info(f"测试批次: {self.batch_sizes}")
        
        # 加载数据
        nodes = self.load_json_data(json_file_path)
        if not nodes:
            logger.error("无法加载数据，测试终止")
            return
        
        # 检查数据量
        max_batch_size = max(self.batch_sizes)
        if len(nodes) < max_batch_size:
            logger.warning(f"数据量不足，最大批次大小: {max_batch_size}, 实际数据量: {len(nodes)}")
            self.batch_sizes = [size for size in self.batch_sizes if size <= len(nodes)]
            logger.info(f"调整后的批次大小: {self.batch_sizes}")
        
        # 设置处理参数
        crawl_timestamp = "2025-08-01T21:00:00.000000"
        source_type = "sync_1000_test"
        metrics = {"ratings": 4.0}
        
        # 逐个处理批次
        for i, batch_size in enumerate(self.batch_sizes):
            logger.info(f"\n{'='*80}")
            logger.info(f"开始处理批次 {i+1}/{len(self.batch_sizes)}: {batch_size}个节点")
            
            # 显示预估信息
            estimation = self.estimate_processing_time(batch_size)
            logger.info(f"预估API调用: {estimation['api_calls']:,} 次")
            logger.info(f"预估时间: {estimation['estimated_hours']:.1f} 小时")
            logger.info(f"{'='*80}")
            
            result = self.process_batch_sync(
                nodes=nodes,
                batch_size=batch_size,
                crawl_timestamp=crawl_timestamp,
                source_type=source_type,
                metrics=metrics
            )
            
            self.results.append(result)
            
            # 保存中间结果
            self.save_intermediate_results()
            
            # 短暂休息
            if i < len(self.batch_sizes) - 1:
                logger.info("休息5秒...")
                time.sleep(5)
        
        # 保存最终结果
        self.save_results()
        self.print_summary()
    
    def save_intermediate_results(self):
        """保存中间结果"""
        try:
            results_file = os.path.join(PROJECT_DIR, 'sync_1000_batch_results_intermediate.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"中间结果已保存: {len(self.results)} 个批次")
        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")
    
    def save_results(self):
        """保存最终测试结果"""
        try:
            # JSON格式
            results_file = os.path.join(PROJECT_DIR, 'sync_1000_batch_results.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {results_file}")
            
            # CSV格式
            csv_file = os.path.join(PROJECT_DIR, 'sync_1000_batch_results.csv')
            df = pd.DataFrame(self.results)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logger.info(f"结果已保存到: {csv_file}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        logger.info("\n" + "="*100)
        logger.info("1000个景点同步批量处理测试结果摘要")
        logger.info("="*100)
        
        total_time = 0
        total_nodes = 0
        
        for result in self.results:
            if result["status"] == "success":
                logger.info(f"批次: {result['batch_size']:>4} | "
                           f"时间: {result['duration_hours']:>6.2f}小时 ({result['duration_minutes']:>6.1f}分钟) | "
                           f"速度: {result['nodes_per_second']:>6.3f}节点/秒 | "
                           f"API调用: {result['api_calls_estimated']:>8,} | "
                           f"成功: {result['success_count']:>4}")
                total_time += result['duration_seconds']
                total_nodes += result['success_count']
            else:
                logger.info(f"批次: {result['batch_size']:>4} | 状态: 失败")
        
        logger.info("="*100)
        logger.info(f"总处理节点数: {total_nodes:,}")
        logger.info(f"总处理时间: {total_time/3600:.2f}小时 ({total_time/60:.1f}分钟)")
        if total_time > 0:
            logger.info(f"平均处理速度: {total_nodes/total_time:.3f}节点/秒")
        logger.info("="*100)
    
    def cleanup(self):
        """清理资源"""
        if self.neo4j_conn:
            self.neo4j_conn.close()
        close_resources()

def main():
    """主函数"""
    # 可以通过命令行参数选择要测试的批次
    if len(sys.argv) > 1:
        try:
            selected_batches = [int(x) for x in sys.argv[1].split(',')]
            logger.info(f"用户指定批次: {selected_batches}")
        except:
            selected_batches = None
            logger.info("使用默认批次")
    else:
        selected_batches = None
    
    tester = Sync1000BatchTester()
    
    try:
        # 建立连接
        if not tester.setup_neo4j_connection():
            logger.error("无法建立Neo4j连接，测试终止")
            return
        
        # 设置JSON文件路径
        json_file_path = os.path.join(PROJECT_DIR, "data", "tibet_1000_attractions.json")
        if not os.path.exists(json_file_path):
            logger.error(f"JSON文件不存在: {json_file_path}")
            return
        
        # 运行测试
        tester.run_selected_batches(json_file_path, selected_batches)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
