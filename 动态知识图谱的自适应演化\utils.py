import json
import logging
from datetime import datetime, timezone
from time_converter import convert_to_beijing_time
import requests
import numpy as np
from config import Config

logger = logging.getLogger(__name__)


def normalize_location(location: str) -> str:
    """标准化地点名称"""
    CITY_MAP = {
        "西藏拉萨": "拉萨市",
        "拉萨": "拉萨市",
        "林芝": "林芝市",
        "日喀则": "日喀则市",
        "昌都": "昌都市",
        "那曲": "那曲市",
        "阿里": "阿里地区",
        "山南": "山南市",
        "八廓街": "拉萨市",
        "西藏市": "拉萨市",
        "当雄县": "拉萨市",
        "墨竹工卡县": "拉萨市",
        "林周县": "拉萨市",
        "尼木县": "拉萨市",
        "曲水县": "拉萨市"
    }
    return CITY_MAP.get(location, location)


def compute_dynamic_weight(data: dict, weights: dict) -> float:
    """计算动态权重"""
    try:
        # 获取 pub_timestamp，假设可能是无时区的 ISO 格式
        pub_timestamp_str = data.get("pub_timestamp")
        if not pub_timestamp_str:
            logger.warning("pub_timestamp 为空，使用默认权重 0.5")
            return 0.5

        # 尝试解析 pub_timestamp，确保其为 offset-aware
        try:
            pub_time = datetime.fromisoformat(pub_timestamp_str.replace('Z', '+00:00'))
            if pub_time.tzinfo is None:
                # 如果没有时区，假设为 UTC
                pub_time = pub_time.replace(tzinfo=timezone.utc)
        except ValueError as e:
            logger.error(f"解析 pub_timestamp 失败: {pub_timestamp_str}, 错误: {e}")
            return 0.5

        # 获取当前北京时间（offset-aware）
        current_time = datetime.fromisoformat(convert_to_beijing_time())

        # 计算时间差
        time_diff = (current_time - pub_time).total_seconds() / (3600 * 24)  # 转换为天

        # 示例权重计算逻辑
        base_weight = weights.get("rules_valid", 0.5) * 0.4 + \
                      weights.get("llm_valid", 0.5) * 0.4 + \
                      weights.get("weight_valid", 0.5) * 0.2

        # 根据时间差调整权重（例如，较新的数据权重更高）
        if time_diff < 30:  # 30 天内
            time_factor = 1.0
        elif time_diff < 90:  # 90 天内
            time_factor = 0.8
        else:
            time_factor = 0.5

        return base_weight * time_factor

    except Exception as e:
        logger.error(f"计算动态权重失败: {e}")
        return 0.0


def compute_semantic_similarity(text1: str, text2: str, model_name: str = "default") -> float:
    """计算两个文本的语义相似性（余弦相似性）"""
    try:
        # 获取 LLM 配置
        config = Config.get_llm_config(model_name)
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        # 使用 DeepSeek 或其他嵌入模型生成嵌入
        payload = {
            "model": config["model"],
            "messages": [
                {"role": "user", "content": f"Generate embeddings for: {text1}"},
                {"role": "user", "content": f"Generate embeddings for: {text2}"}
            ],
            "max_tokens": 1024
        }

        response = requests.post(config["api_base"], headers=headers, json=payload, timeout=config["timeout"])
        response.raise_for_status()
        response_data = response.json()

        # 假设 API 返回嵌入向量（这里需要根据实际 API 调整）
        embeddings = response_data.get("choices", [{}])[0].get("message", {}).get("content", "")
        if not embeddings:
            logger.error("LLM 未返回有效嵌入")
            return 0.0

        # 解析嵌入（假设返回两个向量的列表）
        try:
            emb1, emb2 = json.loads(embeddings)
            emb1 = np.array(emb1, dtype=np.float32)
            emb2 = np.array(emb2, dtype=np.float32)

            # 计算余弦相似性
            cosine_sim = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            return float(cosine_sim)
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"解析嵌入失败: {e}")
            return 0.0

    except Exception as e:
        logger.error(f"计算语义相似性失败: {e}")
        return 0.0