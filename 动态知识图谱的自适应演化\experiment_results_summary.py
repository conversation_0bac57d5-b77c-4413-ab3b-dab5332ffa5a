#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验结果汇总脚本
汇总所有同步批量处理实验的时间数据
"""

import json
import pandas as pd
from datetime import datetime

def create_experiment_summary():
    """创建实验结果汇总"""
    
    print("📊 西藏1000个真实景点同步批量处理实验结果汇总")
    print("=" * 80)
    
    # 手动记录的实验数据（基于日志分析）
    experiment_data = [
        {
            "batch_size": 10,
            "start_time": "2025-08-01 22:03:59",
            "end_time": "2025-08-01 22:05:39",
            "duration_seconds": 100,
            "duration_minutes": 1.67,
            "duration_hours": 0.028,
            "nodes_per_second": 0.100,
            "avg_time_per_node": 10.0,
            "api_calls_estimated": 45,
            "avg_time_per_api_call": 2.22,
            "success_count": 10,
            "failed_count": 0,
            "success_rate": 100.0,
            "real_data_ratio": 100.0,
            "status": "completed"
        },
        {
            "batch_size": 50,
            "start_time": "2025-08-01 22:06:05",
            "end_time": "2025-08-01 22:12:59",
            "duration_seconds": 414,
            "duration_minutes": 6.90,
            "duration_hours": 0.115,
            "nodes_per_second": 0.121,
            "avg_time_per_node": 8.28,
            "api_calls_estimated": 1225,
            "avg_time_per_api_call": 0.338,
            "success_count": 50,
            "failed_count": 0,
            "success_rate": 100.0,
            "real_data_ratio": 100.0,
            "status": "completed"
        },
        {
            "batch_size": 100,
            "start_time": "2025-08-01 22:13:25",
            "end_time": "进行中",
            "duration_seconds": "进行中",
            "duration_minutes": "进行中",
            "duration_hours": "进行中",
            "nodes_per_second": "进行中",
            "avg_time_per_node": "进行中",
            "api_calls_estimated": 4950,
            "avg_time_per_api_call": "进行中",
            "success_count": "进行中",
            "failed_count": "进行中",
            "success_rate": "进行中",
            "real_data_ratio": 100.0,
            "status": "running"
        }
    ]
    
    # 显示已完成的实验结果
    print("\n✅ 已完成的实验:")
    print("-" * 80)
    
    completed_experiments = [exp for exp in experiment_data if exp["status"] == "completed"]
    
    for exp in completed_experiments:
        print(f"🔸 {exp['batch_size']}个节点:")
        print(f"   开始时间: {exp['start_time']}")
        print(f"   结束时间: {exp['end_time']}")
        print(f"   总耗时: {exp['duration_minutes']:.1f}分钟 ({exp['duration_hours']:.3f}小时)")
        print(f"   处理速度: {exp['nodes_per_second']:.3f}节点/秒")
        print(f"   平均每节点: {exp['avg_time_per_node']:.1f}秒")
        print(f"   API调用数: {exp['api_calls_estimated']:,}次")
        print(f"   平均每次API: {exp['avg_time_per_api_call']:.3f}秒")
        print(f"   成功率: {exp['success_rate']:.1f}%")
        print()
    
    # 显示进行中的实验
    running_experiments = [exp for exp in experiment_data if exp["status"] == "running"]
    
    if running_experiments:
        print("🔄 进行中的实验:")
        print("-" * 80)
        
        for exp in running_experiments:
            print(f"🔸 {exp['batch_size']}个节点:")
            print(f"   开始时间: {exp['start_time']}")
            print(f"   预估API调用: {exp['api_calls_estimated']:,}次")
            print(f"   预估时间: 25-35分钟")
            print(f"   状态: 进行中...")
            print()
    
    # 性能分析
    print("📈 性能分析:")
    print("-" * 80)
    
    if len(completed_experiments) >= 2:
        # 计算性能趋势
        batch_10 = completed_experiments[0]
        batch_50 = completed_experiments[1]
        
        speed_improvement = batch_50["nodes_per_second"] / batch_10["nodes_per_second"]
        api_time_improvement = batch_10["avg_time_per_api_call"] / batch_50["avg_time_per_api_call"]
        
        print(f"🔸 处理速度变化:")
        print(f"   10→50节点: {speed_improvement:.2f}x 提升")
        print(f"   ({batch_10['nodes_per_second']:.3f} → {batch_50['nodes_per_second']:.3f} 节点/秒)")
        
        print(f"🔸 API调用效率变化:")
        print(f"   10→50节点: {api_time_improvement:.2f}x 提升")
        print(f"   ({batch_10['avg_time_per_api_call']:.3f} → {batch_50['avg_time_per_api_call']:.3f} 秒/次)")
        
        print(f"🔸 扩展性分析:")
        print(f"   节点数增加: 5倍 (10→50)")
        print(f"   时间增加: {batch_50['duration_minutes']/batch_10['duration_minutes']:.1f}倍")
        print(f"   API调用增加: {batch_50['api_calls_estimated']/batch_10['api_calls_estimated']:.1f}倍")
    
    # 预测分析
    print(f"\n🔮 基于当前数据的预测:")
    print("-" * 80)
    
    if len(completed_experiments) >= 2:
        # 基于50个节点的性能预测更大批次
        base_speed = batch_50["nodes_per_second"]
        base_api_time = batch_50["avg_time_per_api_call"]
        
        predictions = [
            {"size": 100, "api_calls": 4950},
            {"size": 200, "api_calls": 19900},
            {"size": 300, "api_calls": 44850},
            {"size": 500, "api_calls": 124750},
            {"size": 1000, "api_calls": 499500}
        ]
        
        for pred in predictions:
            estimated_time_min = pred["api_calls"] * base_api_time / 60
            estimated_time_hours = estimated_time_min / 60
            
            print(f"🔸 {pred['size']}个节点:")
            print(f"   API调用: {pred['api_calls']:,}次")
            print(f"   预估时间: {estimated_time_min:.1f}分钟 ({estimated_time_hours:.1f}小时)")
            print()
    
    # 保存数据到CSV
    df = pd.DataFrame(completed_experiments)
    df.to_csv("experiment_results_summary.csv", index=False, encoding='utf-8-sig')
    print("💾 结果已保存到: experiment_results_summary.csv")
    
    return experiment_data

def monitor_current_experiment():
    """监控当前实验状态"""
    try:
        with open("sync_real_1000_results_intermediate.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        if results:
            latest = results[-1]
            print(f"\n📊 最新实验状态:")
            print(f"   批次大小: {latest.get('batch_size', 'N/A')}")
            print(f"   状态: {latest.get('status', 'N/A')}")
            if latest.get('status') == 'success':
                print(f"   耗时: {latest.get('duration_minutes', 0):.1f}分钟")
                print(f"   速度: {latest.get('nodes_per_second', 0):.3f}节点/秒")
    
    except FileNotFoundError:
        print("\n📊 暂无中间结果文件")
    except Exception as e:
        print(f"\n❌ 读取中间结果失败: {e}")

if __name__ == "__main__":
    # 创建汇总
    experiment_data = create_experiment_summary()
    
    # 监控当前状态
    monitor_current_experiment()
    
    print(f"\n⏰ 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
