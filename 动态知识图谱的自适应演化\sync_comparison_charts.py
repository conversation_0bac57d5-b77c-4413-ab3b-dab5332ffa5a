#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步vs异步对比专用可视化脚本
专门用于对比分析，以秒为主要时间单位
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComparisonVisualizer:
    """对比可视化器"""
    
    def __init__(self):
        # 同步处理实验数据
        self.sync_data = {
            'batch_size': [10, 50, 100, 150, 200, 250],
            'duration_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
            'nodes_per_second': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
            'success_count': [10, 50, 100, 150, 200, 250],
            'processing_type': 'Synchronous'
        }
        
        # 异步处理数据模板（待填入）
        self.async_data = {
            'batch_size': [10, 50, 100, 150, 200, 250],
            'duration_seconds': [0, 0, 0, 0, 0, 0],  # 待填入
            'nodes_per_second': [0, 0, 0, 0, 0, 0],  # 待填入
            'success_count': [0, 0, 0, 0, 0, 0],     # 待填入
            'processing_type': 'Asynchronous'
        }
        
        self.sync_df = pd.DataFrame(self.sync_data)
        self.async_df = pd.DataFrame(self.async_data)
        
        # 创建输出目录
        self.output_dir = "comparison_charts"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_time_comparison_chart(self):
        """创建处理时间对比图（秒为单位）"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        x = np.arange(len(self.sync_df['batch_size']))
        width = 0.35
        
        # 同步数据
        bars1 = ax.bar(x - width/2, self.sync_df['duration_seconds'], width, 
                      label='同步处理', color='steelblue', alpha=0.8, edgecolor='navy')
        
        # 异步数据（预留位置）
        bars2 = ax.bar(x + width/2, self.async_df['duration_seconds'], width, 
                      label='异步处理 (待测)', color='lightcoral', alpha=0.8, edgecolor='darkred')
        
        # 添加数值标签
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            if height > 0:
                ax.text(bar.get_x() + bar.get_width()/2., height + 30,
                       f'{height:.0f}s', ha='center', va='bottom', fontweight='bold', fontsize=10)
        
        ax.set_xlabel('批次大小 (节点数)', fontsize=14, fontweight='bold')
        ax.set_ylabel('处理时间 (秒)', fontsize=14, fontweight='bold')
        ax.set_title('同步 vs 异步处理时间对比', fontsize=16, fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(self.sync_df['batch_size'])
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加性能提升预期文本
        ax.text(0.02, 0.98, '异步处理预期提升：2-5倍', transform=ax.transAxes, 
               fontsize=12, fontweight='bold', va='top', 
               bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/time_comparison_seconds.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 处理时间对比图已生成（秒为单位）")
    
    def create_speed_comparison_chart(self):
        """创建处理速度对比图"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 同步数据
        line1 = ax.plot(self.sync_df['batch_size'], self.sync_df['nodes_per_second'], 
                       marker='o', linewidth=3, markersize=10, 
                       label='同步处理', color='steelblue', markerfacecolor='darkblue', 
                       markeredgecolor='white', markeredgewidth=2)
        
        # 异步数据（预留位置）
        line2 = ax.plot(self.async_df['batch_size'], self.async_df['nodes_per_second'], 
                       marker='s', linewidth=3, markersize=10, 
                       label='异步处理 (待测)', color='lightcoral', markerfacecolor='darkred',
                       markeredgecolor='white', markeredgewidth=2)
        
        # 添加数据点标签
        for i, (x, y) in enumerate(zip(self.sync_df['batch_size'], self.sync_df['nodes_per_second'])):
            ax.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                       xytext=(0,15), ha='center', fontweight='bold', fontsize=10)
        
        ax.set_xlabel('批次大小 (节点数)', fontsize=14, fontweight='bold')
        ax.set_ylabel('处理速度 (节点/秒)', fontsize=14, fontweight='bold')
        ax.set_title('同步 vs 异步处理速度对比', fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加平均线
        avg_speed = self.sync_df['nodes_per_second'].mean()
        ax.axhline(y=avg_speed, color='orange', linestyle='--', linewidth=2, 
                  label=f'同步平均速度: {avg_speed:.3f} 节点/秒')
        ax.legend(fontsize=12)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/speed_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 处理速度对比图已生成")
    
    def create_performance_metrics_table(self):
        """创建性能指标对比表"""
        fig, ax = plt.subplots(figsize=(16, 10))
        ax.axis('tight')
        ax.axis('off')
        
        # 准备表格数据
        table_data = []
        headers = ['批次大小', '同步时间(秒)', '异步时间(秒)', '同步速度', '异步速度', '时间提升倍数', '速度提升倍数']
        
        for i in range(len(self.sync_df)):
            sync_time = self.sync_df.iloc[i]['duration_seconds']
            sync_speed = self.sync_df.iloc[i]['nodes_per_second']
            batch_size = self.sync_df.iloc[i]['batch_size']
            
            table_data.append([
                f"{batch_size}",
                f"{sync_time:.0f}",
                "待测",
                f"{sync_speed:.3f}",
                "待测",
                "待计算",
                "待计算"
            ])
        
        # 创建表格
        table = ax.table(cellText=table_data,
                        colLabels=headers,
                        cellLoc='center',
                        loc='center')
        
        table.auto_set_font_size(False)
        table.set_fontsize(11)
        table.scale(1.2, 2)
        
        # 设置表格样式
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        for i in range(1, len(table_data) + 1):
            for j in range(len(headers)):
                if j < 2 or j == 3:  # 同步数据列
                    table[(i, j)].set_facecolor('#E3F2FD')
                else:  # 异步数据列
                    table[(i, j)].set_facecolor('#FFEBEE')
        
        ax.set_title('同步 vs 异步性能指标对比表', fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/performance_metrics_table.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 性能指标对比表已生成")
    
    def create_efficiency_analysis_chart(self):
        """创建效率分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))
        
        # 1. 每节点处理时间对比
        sync_time_per_node = self.sync_df['duration_seconds'] / self.sync_df['batch_size']
        
        ax1.bar(self.sync_df['batch_size'], sync_time_per_node, 
               color='steelblue', alpha=0.8, label='同步处理')
        ax1.set_xlabel('批次大小')
        ax1.set_ylabel('每节点处理时间 (秒)')
        ax1.set_title('每节点平均处理时间对比')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 添加数值标签
        for i, (x, y) in enumerate(zip(self.sync_df['batch_size'], sync_time_per_node)):
            ax1.text(x, y + 0.2, f'{y:.1f}s', ha='center', va='bottom', fontweight='bold')
        
        # 2. 处理时间增长趋势
        ax2.plot(self.sync_df['batch_size'], self.sync_df['duration_seconds'], 
                marker='o', linewidth=3, markersize=8, color='steelblue', label='同步处理')
        ax2.plot(self.async_df['batch_size'], self.async_df['duration_seconds'], 
                marker='s', linewidth=3, markersize=8, color='lightcoral', label='异步处理 (待测)')
        ax2.set_xlabel('批次大小')
        ax2.set_ylabel('处理时间 (秒)')
        ax2.set_title('处理时间增长趋势')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 3. 扩展性分析
        # 理想线性增长
        ideal_growth = [(size / 10) * self.sync_df.iloc[0]['duration_seconds'] 
                       for size in self.sync_df['batch_size']]
        
        ax3.plot(self.sync_df['batch_size'], self.sync_df['duration_seconds'], 
                marker='o', linewidth=3, label='实际同步处理', color='steelblue')
        ax3.plot(self.sync_df['batch_size'], ideal_growth, 
                '--', linewidth=2, label='理想线性增长', color='gray', alpha=0.7)
        ax3.set_xlabel('批次大小')
        ax3.set_ylabel('处理时间 (秒)')
        ax3.set_title('扩展性分析')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 4. 性能提升预期
        expected_improvement = [2, 2.5, 3, 3.5, 4, 4.5]  # 预期提升倍数
        
        ax4.bar(self.sync_df['batch_size'], [1]*len(self.sync_df), 
               label='同步处理 (基准)', color='steelblue', alpha=0.8)
        ax4.bar(self.sync_df['batch_size'], expected_improvement, 
               label='异步处理 (预期)', color='lightcoral', alpha=0.8)
        ax4.set_xlabel('批次大小')
        ax4.set_ylabel('相对性能倍数')
        ax4.set_title('预期性能提升倍数')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 添加预期提升数值
        for i, (x, y) in enumerate(zip(self.sync_df['batch_size'], expected_improvement)):
            ax4.text(x, y + 0.1, f'{y}x', ha='center', va='bottom', fontweight='bold')
        
        plt.suptitle('同步 vs 异步处理效率分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/efficiency_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 效率分析图已生成")
    
    def create_summary_dashboard(self):
        """创建汇总仪表板"""
        fig = plt.figure(figsize=(20, 14))
        gs = fig.add_gridspec(3, 3, hspace=0.4, wspace=0.3)
        
        # 主要对比图 - 处理时间（秒）
        ax_main = fig.add_subplot(gs[0, :2])
        x = np.arange(len(self.sync_df['batch_size']))
        width = 0.35
        
        bars1 = ax_main.bar(x - width/2, self.sync_df['duration_seconds'], width, 
                           label='同步处理', color='steelblue', alpha=0.8)
        bars2 = ax_main.bar(x + width/2, self.async_df['duration_seconds'], width, 
                           label='异步处理 (待测)', color='lightcoral', alpha=0.8)
        
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax_main.text(bar.get_x() + bar.get_width()/2., height + 30,
                        f'{height:.0f}s', ha='center', va='bottom', fontweight='bold')
        
        ax_main.set_xlabel('批次大小 (节点数)', fontweight='bold')
        ax_main.set_ylabel('处理时间 (秒)', fontweight='bold')
        ax_main.set_title('处理时间对比 (秒)', fontsize=14, fontweight='bold')
        ax_main.set_xticks(x)
        ax_main.set_xticklabels(self.sync_df['batch_size'])
        ax_main.legend()
        ax_main.grid(True, alpha=0.3)
        
        # 其他子图...
        # 速度对比
        ax1 = fig.add_subplot(gs[0, 2])
        ax1.plot(self.sync_df['batch_size'], self.sync_df['nodes_per_second'], 
                marker='o', linewidth=2, color='steelblue', label='同步')
        ax1.set_title('处理速度', fontweight='bold')
        ax1.set_ylabel('节点/秒')
        ax1.grid(True, alpha=0.3)
        
        # 统计信息
        ax2 = fig.add_subplot(gs[1, :])
        ax2.axis('off')
        
        stats_text = f"""
同步处理统计信息：
• 总处理节点数: {self.sync_df['batch_size'].sum()} 个
• 总处理时间: {self.sync_df['duration_seconds'].sum():.0f} 秒 ({self.sync_df['duration_seconds'].sum()/60:.1f} 分钟)
• 平均处理速度: {self.sync_df['nodes_per_second'].mean():.3f} 节点/秒
• 最快速度: {self.sync_df['nodes_per_second'].max():.3f} 节点/秒 ({self.sync_df.loc[self.sync_df['nodes_per_second'].idxmax(), 'batch_size']} 个节点批次)
• 最慢速度: {self.sync_df['nodes_per_second'].min():.3f} 节点/秒 ({self.sync_df.loc[self.sync_df['nodes_per_second'].idxmin(), 'batch_size']} 个节点批次)
• 速度稳定性: 变异系数 {(self.sync_df['nodes_per_second'].std()/self.sync_df['nodes_per_second'].mean()*100):.1f}%
        """
        
        ax2.text(0.05, 0.8, stats_text, transform=ax2.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # 预期改进信息
        improvement_text = """
异步处理预期改进：
• 处理速度提升: 2-5倍
• 并发处理能力: 支持多节点同时处理
• 资源利用率: 更好的CPU和网络利用
• 错误恢复: 更强的容错机制
• 扩展性: 可能实现超线性增长
        """
        
        ax2.text(0.55, 0.8, improvement_text, transform=ax2.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        
        plt.suptitle('同步 vs 异步处理性能对比仪表板', fontsize=18, fontweight='bold')
        plt.savefig(f'{self.output_dir}/summary_dashboard.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 汇总仪表板已生成")
    
    def generate_all_comparison_charts(self):
        """生成所有对比图表"""
        print("🎨 开始生成同步vs异步对比图表（以秒为主要单位）...")
        
        self.create_time_comparison_chart()
        self.create_speed_comparison_chart()
        self.create_performance_metrics_table()
        self.create_efficiency_analysis_chart()
        self.create_summary_dashboard()
        
        print(f"\n✅ 所有对比图表已生成完成！保存在 '{self.output_dir}' 目录中")
        print("\n📁 生成的文件:")
        for file in os.listdir(self.output_dir):
            if file.endswith('.png'):
                print(f"   - {file}")

def main():
    """主函数"""
    visualizer = ComparisonVisualizer()
    visualizer.generate_all_comparison_charts()

if __name__ == "__main__":
    main()
