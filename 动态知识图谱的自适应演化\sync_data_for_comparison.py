#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成用于对比的同步处理数据表格（以秒为主要单位）
"""

import pandas as pd
import json

def create_comparison_data():
    """创建对比数据"""
    
    # 同步处理的完整数据（以秒为主要单位）
    sync_data = {
        'batch_size': [10, 50, 100, 150, 200, 250],
        'processing_time_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
        'processing_speed_nodes_per_sec': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
        'time_per_node_seconds': [11.80, 11.12, 12.07, 10.46, 9.55, 8.67],
        'success_count': [10, 50, 100, 150, 200, 250],
        'success_rate_percent': [100.0, 100.0, 100.0, 100.0, 100.0, 100.0],
        'processing_type': 'Synchronous'
    }
    
    # 创建DataFrame
    df = pd.DataFrame(sync_data)
    
    # 保存为CSV（用于对比）
    df.to_csv('sync_data_for_comparison.csv', index=False, encoding='utf-8-sig')
    
    # 创建对比模板
    comparison_template = {
        'batch_size': [10, 50, 100, 150, 200, 250],
        'sync_time_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
        'async_time_seconds': ['TBD', 'TBD', 'TBD', 'TBD', 'TBD', 'TBD'],
        'sync_speed_nodes_per_sec': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
        'async_speed_nodes_per_sec': ['TBD', 'TBD', 'TBD', 'TBD', 'TBD', 'TBD'],
        'time_improvement_ratio': ['TBD', 'TBD', 'TBD', 'TBD', 'TBD', 'TBD'],
        'speed_improvement_ratio': ['TBD', 'TBD', 'TBD', 'TBD', 'TBD', 'TBD']
    }
    
    comparison_df = pd.DataFrame(comparison_template)
    comparison_df.to_csv('sync_vs_async_comparison_template.csv', index=False, encoding='utf-8-sig')
    
    # 生成JSON格式
    with open('sync_performance_seconds.json', 'w', encoding='utf-8') as f:
        json.dump(sync_data, f, ensure_ascii=False, indent=2)
    
    # 生成简洁的对比表格
    print("📊 同步处理性能数据（以秒为单位）")
    print("=" * 80)
    print(f"{'批次大小':<8} {'处理时间(秒)':<12} {'处理速度':<12} {'每节点时间(秒)':<14} {'成功率':<8}")
    print("-" * 80)
    
    for i, row in df.iterrows():
        print(f"{row['batch_size']:<8} {row['processing_time_seconds']:<12.0f} "
              f"{row['processing_speed_nodes_per_sec']:<12.3f} "
              f"{row['time_per_node_seconds']:<14.1f} {row['success_rate_percent']:<8.0f}%")
    
    print("-" * 80)
    print(f"总计: {df['batch_size'].sum()} 个节点, "
          f"{df['processing_time_seconds'].sum():.0f} 秒 "
          f"({df['processing_time_seconds'].sum()/60:.1f} 分钟)")
    print(f"平均速度: {df['processing_speed_nodes_per_sec'].mean():.3f} 节点/秒")
    print(f"平均每节点时间: {df['time_per_node_seconds'].mean():.1f} 秒")
    
    print("\n📁 生成的文件:")
    print("   - sync_data_for_comparison.csv")
    print("   - sync_vs_async_comparison_template.csv") 
    print("   - sync_performance_seconds.json")
    
    return df

def create_async_template():
    """创建异步数据填入模板"""
    
    template = """
# 异步处理数据填入模板

## 请将异步处理的实验结果填入下表：

| 批次大小 | 异步时间(秒) | 异步速度(节点/秒) | 时间提升倍数 | 速度提升倍数 |
|---------|-------------|------------------|-------------|-------------|
| 10      | ___         | ___              | ___         | ___         |
| 50      | ___         | ___              | ___         | ___         |
| 100     | ___         | ___              | ___         | ___         |
| 150     | ___         | ___              | ___         | ___         |
| 200     | ___         | ___              | ___         | ___         |
| 250     | ___         | ___              | ___         | ___         |

## 同步处理基准数据（参考）：

| 批次大小 | 同步时间(秒) | 同步速度(节点/秒) |
|---------|-------------|------------------|
| 10      | 118         | 0.085            |
| 50      | 556         | 0.090            |
| 100     | 1207        | 0.083            |
| 150     | 1569        | 0.096            |
| 200     | 1909        | 0.105            |
| 250     | 2167        | 0.115            |

## 计算公式：
- 时间提升倍数 = 同步时间(秒) ÷ 异步时间(秒)
- 速度提升倍数 = 异步速度 ÷ 同步速度

## 预期范围：
- 时间提升倍数: 2-5倍
- 速度提升倍数: 2-5倍
"""
    
    with open('async_data_template.md', 'w', encoding='utf-8') as f:
        f.write(template)
    
    print("   - async_data_template.md (异步数据填入模板)")

def main():
    """主函数"""
    print("🎯 生成用于对比的同步处理数据...")
    df = create_comparison_data()
    create_async_template()
    print("\n✅ 对比数据生成完成！")

if __name__ == "__main__":
    main()
