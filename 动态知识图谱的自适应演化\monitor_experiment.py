#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验监控脚本
实时监控同步批量处理实验的进度
"""

import json
import os
import time
from datetime import datetime

def monitor_experiment_progress():
    """监控实验进度"""
    print("🔍 实验进度监控器启动...")
    print("=" * 80)
    
    # 监控文件
    files_to_monitor = [
        "sync_real_1000_results_intermediate.json",
        "checkpoint_real_batch_50.json",
        "checkpoint_real_batch_100.json",
        "sync_real_1000_test.log"
    ]
    
    last_update_times = {}
    
    while True:
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"\n[{current_time}] 检查实验状态...")
        
        # 检查中间结果文件
        intermediate_file = "sync_real_1000_results_intermediate.json"
        if os.path.exists(intermediate_file):
            try:
                with open(intermediate_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                if results:
                    latest_result = results[-1]
                    batch_size = latest_result.get('batch_size', 0)
                    status = latest_result.get('status', 'unknown')
                    
                    if status == 'success':
                        duration_min = latest_result.get('duration_minutes', 0)
                        speed = latest_result.get('nodes_per_second', 0)
                        success_count = latest_result.get('success_count', 0)
                        
                        print(f"✅ 最新完成批次: {batch_size}个节点")
                        print(f"   耗时: {duration_min:.1f}分钟")
                        print(f"   速度: {speed:.3f}节点/秒")
                        print(f"   成功: {success_count}个")
                    else:
                        print(f"⚠️ 最新批次状态: {status}")
                        
            except Exception as e:
                print(f"❌ 读取中间结果失败: {e}")
        
        # 检查检查点文件
        for batch_size in [50, 100, 200, 300, 500, 1000]:
            checkpoint_file = f"checkpoint_real_batch_{batch_size}.json"
            if os.path.exists(checkpoint_file):
                try:
                    file_time = os.path.getmtime(checkpoint_file)
                    if checkpoint_file not in last_update_times or file_time > last_update_times[checkpoint_file]:
                        last_update_times[checkpoint_file] = file_time
                        
                        with open(checkpoint_file, 'r', encoding='utf-8') as f:
                            checkpoint = json.load(f)
                        
                        processed = checkpoint.get('processed', 0)
                        total = checkpoint.get('batch_size', 0)
                        elapsed_min = checkpoint.get('elapsed_minutes', 0)
                        progress = checkpoint.get('progress_percentage', 0)
                        speed = checkpoint.get('speed_nodes_per_sec', 0)
                        
                        print(f"🔄 {batch_size}个节点批次进行中:")
                        print(f"   进度: {processed}/{total} ({progress:.1f}%)")
                        print(f"   已耗时: {elapsed_min:.1f}分钟")
                        print(f"   当前速度: {speed:.3f}节点/秒")
                        
                except Exception as e:
                    print(f"❌ 读取检查点失败: {e}")
        
        # 检查日志文件
        log_file = "sync_real_1000_test.log"
        if os.path.exists(log_file):
            try:
                file_time = os.path.getmtime(log_file)
                if log_file not in last_update_times or file_time > last_update_times[log_file]:
                    last_update_times[log_file] = file_time
                    
                    # 读取最后几行日志
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    if lines:
                        last_line = lines[-1].strip()
                        if "进度:" in last_line:
                            print(f"📊 最新日志: {last_line}")
                        elif "处理完成" in last_line:
                            print(f"✅ 最新日志: {last_line}")
                        elif "ERROR" in last_line:
                            print(f"❌ 错误日志: {last_line}")
                            
            except Exception as e:
                print(f"❌ 读取日志失败: {e}")
        
        print("-" * 80)
        time.sleep(30)  # 每30秒检查一次

if __name__ == "__main__":
    try:
        monitor_experiment_progress()
    except KeyboardInterrupt:
        print("\n监控器已停止")
