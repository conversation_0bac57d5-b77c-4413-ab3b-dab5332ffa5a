#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步批量处理性能可视化脚本
生成多种图表展示不同批次大小的性能数据，用于与异步版本对比
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')

class SyncPerformanceVisualizer:
    """同步性能可视化器"""
    
    def __init__(self):
        # 完整的实验数据
        self.data = {
            'batch_size': [10, 50, 100, 150, 200, 250],
            'duration_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
            'duration_minutes': [1.97, 9.27, 20.12, 26.15, 31.82, 36.12],
            'nodes_per_second': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
            'success_count': [10, 50, 100, 150, 200, 250],
            'failed_count': [0, 0, 0, 0, 0, 0]
        }
        
        self.df = pd.DataFrame(self.data)
        
        # 创建输出目录
        self.output_dir = "sync_performance_charts"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_processing_time_chart(self):
        """创建处理时间对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 左图：处理时间(秒)
        bars1 = ax1.bar(self.df['batch_size'], self.df['duration_seconds'], 
                       color='steelblue', alpha=0.8, edgecolor='navy', linewidth=1)
        ax1.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('处理时间 (秒)', fontsize=12, fontweight='bold')
        ax1.set_title('同步处理 - 各批次处理时间 (秒)', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 20,
                    f'{height:.0f}s', ha='center', va='bottom', fontweight='bold')
        
        # 右图：处理时间(分钟)
        bars2 = ax2.bar(self.df['batch_size'], self.df['duration_minutes'], 
                       color='darkgreen', alpha=0.8, edgecolor='darkgreen', linewidth=1)
        ax2.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('处理时间 (分钟)', fontsize=12, fontweight='bold')
        ax2.set_title('同步处理 - 各批次处理时间 (分钟)', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}min', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/processing_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_processing_speed_chart(self):
        """创建处理速度图"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 创建折线图
        line = ax.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                      marker='o', linewidth=3, markersize=10, 
                      color='red', markerfacecolor='darkred', markeredgecolor='white', markeredgewidth=2)
        
        # 添加数据点标签
        for i, (x, y) in enumerate(zip(self.df['batch_size'], self.df['nodes_per_second'])):
            ax.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                       xytext=(0,15), ha='center', fontweight='bold', fontsize=11)
        
        ax.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax.set_ylabel('处理速度 (节点/秒)', fontsize=12, fontweight='bold')
        ax.set_title('同步处理 - 各批次处理速度对比', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 添加平均线
        avg_speed = self.df['nodes_per_second'].mean()
        ax.axhline(y=avg_speed, color='orange', linestyle='--', linewidth=2, 
                  label=f'平均速度: {avg_speed:.3f} 节点/秒')
        ax.legend(fontsize=11)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/processing_speed_trend.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_scalability_analysis(self):
        """创建扩展性分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 左图：时间增长趋势
        ax1.plot(self.df['batch_size'], self.df['duration_minutes'], 
                marker='s', linewidth=3, markersize=8, color='purple', 
                markerfacecolor='purple', markeredgecolor='white', markeredgewidth=2)
        
        # 添加理想线性增长线
        ideal_slope = self.df['duration_minutes'].iloc[-1] / self.df['batch_size'].iloc[-1]
        ideal_line = [size * ideal_slope for size in self.df['batch_size']]
        ax1.plot(self.df['batch_size'], ideal_line, '--', color='gray', 
                linewidth=2, alpha=0.7, label='理想线性增长')
        
        ax1.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('处理时间 (分钟)', fontsize=12, fontweight='bold')
        ax1.set_title('扩展性分析 - 时间增长趋势', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 右图：每个节点平均处理时间
        avg_time_per_node = [duration/size for duration, size in 
                           zip(self.df['duration_seconds'], self.df['batch_size'])]
        
        bars = ax2.bar(self.df['batch_size'], avg_time_per_node, 
                      color='orange', alpha=0.8, edgecolor='darkorange', linewidth=1)
        ax2.set_xlabel('批次大小 (节点数)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('每节点平均时间 (秒)', fontsize=12, fontweight='bold')
        ax2.set_title('扩展性分析 - 每节点平均处理时间', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, time in zip(bars, avg_time_per_node):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.2,
                    f'{time:.1f}s', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/scalability_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_comprehensive_dashboard(self):
        """创建综合仪表板"""
        fig = plt.figure(figsize=(20, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 处理时间柱状图
        ax1 = fig.add_subplot(gs[0, 0])
        bars1 = ax1.bar(self.df['batch_size'], self.df['duration_minutes'], 
                       color='steelblue', alpha=0.8)
        ax1.set_title('处理时间 (分钟)', fontweight='bold')
        ax1.set_xlabel('批次大小')
        ax1.set_ylabel('时间 (分钟)')
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}', ha='center', va='bottom', fontsize=9)
        
        # 2. 处理速度折线图
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                marker='o', linewidth=2, markersize=6, color='red')
        ax2.set_title('处理速度 (节点/秒)', fontweight='bold')
        ax2.set_xlabel('批次大小')
        ax2.set_ylabel('速度 (节点/秒)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 成功率饼图
        ax3 = fig.add_subplot(gs[0, 2])
        total_success = sum(self.df['success_count'])
        total_failed = sum(self.df['failed_count'])
        ax3.pie([total_success, total_failed], labels=['成功', '失败'], 
               colors=['lightgreen', 'lightcoral'], autopct='%1.1f%%', startangle=90)
        ax3.set_title('总体成功率', fontweight='bold')
        
        # 4. 时间增长趋势
        ax4 = fig.add_subplot(gs[1, :2])
        ax4.plot(self.df['batch_size'], self.df['duration_seconds'], 
                marker='s', linewidth=3, markersize=8, color='purple')
        ax4.set_title('处理时间增长趋势', fontweight='bold')
        ax4.set_xlabel('批次大小 (节点数)')
        ax4.set_ylabel('处理时间 (秒)')
        ax4.grid(True, alpha=0.3)
        
        # 5. 性能指标表格
        ax5 = fig.add_subplot(gs[1, 2])
        ax5.axis('tight')
        ax5.axis('off')
        
        table_data = []
        for i, row in self.df.iterrows():
            table_data.append([
                f"{row['batch_size']}",
                f"{row['duration_minutes']:.1f}min",
                f"{row['nodes_per_second']:.3f}",
                f"{row['success_count']}/{row['batch_size']}"
            ])
        
        table = ax5.table(cellText=table_data,
                         colLabels=['批次', '时间', '速度', '成功率'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.2, 1.5)
        ax5.set_title('性能指标汇总', fontweight='bold')
        
        # 6. 效率分析
        ax6 = fig.add_subplot(gs[2, :])
        
        # 计算效率指标
        efficiency = [speed / max(self.df['nodes_per_second']) * 100 
                     for speed in self.df['nodes_per_second']]
        
        bars = ax6.bar(self.df['batch_size'], efficiency, 
                      color='lightblue', alpha=0.8, edgecolor='blue')
        ax6.set_title('相对效率分析 (以最高速度为100%)', fontweight='bold')
        ax6.set_xlabel('批次大小 (节点数)')
        ax6.set_ylabel('相对效率 (%)')
        ax6.set_ylim(0, 110)
        
        for bar, eff in zip(bars, efficiency):
            ax6.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{eff:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 添加总标题
        fig.suptitle('同步批量处理性能综合仪表板', fontsize=16, fontweight='bold', y=0.98)
        
        plt.savefig(f'{self.output_dir}/comprehensive_dashboard.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_comparison_template(self):
        """创建对比模板图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 处理时间对比 (为异步数据预留空间)
        x = np.arange(len(self.df['batch_size']))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, self.df['duration_minutes'], width, 
                       label='同步处理', color='steelblue', alpha=0.8)
        # 预留异步数据位置
        bars2 = ax1.bar(x + width/2, [0]*len(x), width, 
                       label='异步处理 (待测)', color='lightcoral', alpha=0.8)
        
        ax1.set_xlabel('批次大小 (节点数)')
        ax1.set_ylabel('处理时间 (分钟)')
        ax1.set_title('处理时间对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(self.df['batch_size'])
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 处理速度对比
        ax2.plot(self.df['batch_size'], self.df['nodes_per_second'], 
                marker='o', linewidth=2, label='同步处理', color='steelblue')
        # 预留异步数据位置
        ax2.plot(self.df['batch_size'], [0]*len(self.df['batch_size']), 
                marker='s', linewidth=2, label='异步处理 (待测)', color='lightcoral')
        
        ax2.set_xlabel('批次大小 (节点数)')
        ax2.set_ylabel('处理速度 (节点/秒)')
        ax2.set_title('处理速度对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 扩展性对比
        ax3.plot(self.df['batch_size'], self.df['duration_seconds'], 
                marker='o', linewidth=2, label='同步处理', color='steelblue')
        ax3.plot(self.df['batch_size'], [0]*len(self.df['batch_size']), 
                marker='s', linewidth=2, label='异步处理 (待测)', color='lightcoral')
        
        ax3.set_xlabel('批次大小 (节点数)')
        ax3.set_ylabel('处理时间 (秒)')
        ax3.set_title('扩展性对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 效率提升预期
        ax4.bar(self.df['batch_size'], [1]*len(self.df['batch_size']), 
               label='同步处理 (基准)', color='steelblue', alpha=0.8)
        ax4.bar(self.df['batch_size'], [0]*len(self.df['batch_size']), 
               label='异步处理 (待测)', color='lightcoral', alpha=0.8)
        
        ax4.set_xlabel('批次大小 (节点数)')
        ax4.set_ylabel('相对性能倍数')
        ax4.set_title('性能提升倍数对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('同步 vs 异步处理性能对比模板', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/comparison_template.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_all_charts(self):
        """生成所有图表"""
        print("🎨 开始生成同步性能可视化图表...")
        
        print("📊 1. 生成处理时间对比图...")
        self.create_processing_time_chart()
        
        print("📈 2. 生成处理速度趋势图...")
        self.create_processing_speed_chart()
        
        print("📉 3. 生成扩展性分析图...")
        self.create_scalability_analysis()
        
        print("🎯 4. 生成综合仪表板...")
        self.create_comprehensive_dashboard()
        
        print("🔄 5. 生成对比模板图...")
        self.create_comparison_template()
        
        print(f"✅ 所有图表已生成完成！保存在 '{self.output_dir}' 目录中")
        print("\n📁 生成的文件:")
        for file in os.listdir(self.output_dir):
            if file.endswith('.png'):
                print(f"   - {file}")

def main():
    """主函数"""
    visualizer = SyncPerformanceVisualizer()
    visualizer.generate_all_charts()

if __name__ == "__main__":
    main()
