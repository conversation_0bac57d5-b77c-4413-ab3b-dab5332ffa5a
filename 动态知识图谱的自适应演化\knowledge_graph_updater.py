import logging
from typing import Dict
from neo4j_crud import Neo4jCRUD
from neo4j_connection import Neo4jConnection
from utils import normalize_location, compute_dynamic_weight
from time_converter import convert_to_beijing_time

logger = logging.getLogger(__name__)


class KnowledgeGraphUpdater:
    def __init__(self, connection: Neo4jConnection):
        """
        初始化函数，接收一个 Neo4jConnection 对象，并用它创建唯一的 Neo4jCRUD 实例。
        """
        # 正确的做法：用 Neo4jConnection 实例来创建 Neo4jCRUD
        self.crud = Neo4jCRUD(connection)
        logger.info("KnowledgeGraphUpdater 初始化完成")

    def close(self):
        """
        关闭连接。通过 CRUD 对象中持有的 connection 来关闭。
        """
        if self.crud and self.crud.connection:
            self.crud.connection.close()
            logger.info("KnowledgeGraphUpdater 连接已关闭")

    def update_knowledge_graph(self, data: Dict, log_id: str, reason: str, weights: Dict):
        """
        更新知识图谱。现在直接调用 self.crud 的方法，无需管理 session 或 transaction。
        """
        if not data or not isinstance(data, dict) or not data.get("name"):
            logger.error(f"无效数据输入，跳过处理: {data}")
            return

        try:
            normalized_location = normalize_location(data.get("location", ""))

            # 1. 更新或创建 Attraction 节点
            attraction_props = {
                "name": data.get("name"),
                "description": data.get("description", ""),
                "address": data.get("address", ""),
                "source_type": data.get("source_type", "crawler"),
                "pub_timestamp": data.get("pub_timestamp", convert_to_beijing_time()),
                "log_id": log_id,
                "reason": reason,
                "dynamic_weight": compute_dynamic_weight(data, weights)
            }
            if data.get("best_comment"):
                attraction_props["best_comment"] = data["best_comment"]
            if data.get("is_cultural") is not None:
                attraction_props["is_cultural"] = data["is_cultural"]

            # 直接调用 self.crud 的方法，不再需要传入 session
            attraction_node = self.crud.create_node("Attraction", attraction_props)
            if attraction_node:
                logger.info(f"成功创建/更新 Attraction 节点: {data.get('name')}")
            else:
                logger.warning(f"创建 Attraction 节点失败: {data.get('name')}")
                return  # 如果节点创建失败，后续操作也无法进行

            # 2. 更新或创建 City 节点
            city_props = {
                "name": normalized_location,
                "region": "西藏自治区"
            }
            city_node = self.crud.create_node("City", city_props)
            if city_node:
                logger.debug(f"成功创建/更新 City 节点: {normalized_location}")
            else:
                logger.warning(f"创建 City 节点失败: {normalized_location}")
                return

            # 3. 创建 Attraction -> City 关系
            # 确保两个节点都已成功创建
            if attraction_node and city_node:
                success = self.crud.create_relationship(
                    source_label="Attraction",
                    source_name=data.get("name"),
                    target_label="City",
                    target_name=normalized_location,
                    rel_type="LOCATED_IN",
                    properties={"reason": reason, "log_id": log_id}
                )
                if success:
                    logger.debug(f"成功创建关系: {data.get('name')} -> {normalized_location}")
                else:
                    logger.warning(f"创建关系失败: {data.get('name')} -> {normalized_location}")

        except Exception as e:
            logger.error(f"更新知识图谱失败: {data.get('name')}, 错误: {e}", exc_info=True)
            raise