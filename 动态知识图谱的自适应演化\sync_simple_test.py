#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版同步批量处理测试脚本
只测试节点创建性能，跳过关系生成，专注于数据插入速度
"""

import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

# 导入项目模块
from config import Config
from neo4j_connection import Neo4jConnection

# 设置日志
PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE = os.path.join(PROJECT_DIR, 'sync_simple_test.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(LOG_FILE, encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimpleSyncTester:
    """简化版同步测试器"""
    
    def __init__(self):
        self.neo4j_conn = None
        self.results = []
        self.batch_sizes = [10, 50, 100, 150, 200, 250]
        
    def setup_neo4j_connection(self):
        """建立Neo4j连接"""
        try:
            neo4j_config = Config.get_neo4j_config()
            self.neo4j_conn = Neo4jConnection(
                uri=neo4j_config["uri"],
                user=neo4j_config["user"],
                password=neo4j_config["password"]
            )
            self.neo4j_conn.verify_connectivity()
            logger.info(f"成功连接到 Neo4j: {neo4j_config['uri']}")
            return True
        except Exception as e:
            logger.error(f"Neo4j连接失败: {e}")
            return False
    
    def load_json_data(self, json_file_path: str) -> List[Dict]:
        """加载JSON数据"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            nodes = data.get("nodes", [])
            if not nodes:
                logger.warning("JSON文件中无节点数据")
                return []
            
            # 去重处理
            seen_names = set()
            unique_nodes = []
            for node in nodes:
                if node["name"] not in seen_names:
                    seen_names.add(node["name"])
                    unique_nodes.append(node)
            
            logger.info(f"加载数据完成，总节点数: {len(nodes)}, 去重后: {len(unique_nodes)}")
            return unique_nodes
            
        except Exception as e:
            logger.error(f"加载JSON数据失败: {e}")
            return []
    
    def create_attraction_node(self, node_data: Dict, source_type: str, metrics: Dict) -> bool:
        """创建单个景点节点（简化版，不生成关系）"""
        try:
            with self.neo4j_conn.driver.session() as session:
                # 创建或更新Attraction节点
                query = """
                MERGE (a:Attraction {name: $name})
                SET a.location = $location,
                    a.address = $address,
                    a.description = $description,
                    a.ranking = $ranking,
                    a.visitor_percentage = $visitor_percentage,
                    a.pub_timestamp = $pub_timestamp,
                    a.source_type = $source_type,
                    a.ratings = $ratings,
                    a.updated_at = datetime()
                
                // 创建或连接到City节点
                WITH a
                WHERE $location IS NOT NULL AND $location <> ''
                MERGE (c:City {name: $location})
                SET c.updated_at = datetime()
                MERGE (a)-[:LOCATED_IN]->(c)
                
                RETURN a.name as name
                """
                
                result = session.run(query, {
                    'name': node_data.get('name', ''),
                    'location': node_data.get('location', ''),
                    'address': node_data.get('address', ''),
                    'description': node_data.get('description', ''),
                    'ranking': node_data.get('ranking', ''),
                    'visitor_percentage': node_data.get('visitor_percentage', ''),
                    'pub_timestamp': node_data.get('pub_timestamp', ''),
                    'source_type': source_type,
                    'ratings': metrics.get('ratings', 0)
                })
                
                record = result.single()
                if record:
                    logger.debug(f"成功创建节点: {record['name']}")
                    return True
                else:
                    logger.warning(f"节点创建可能失败: {node_data.get('name', 'Unknown')}")
                    return False
                    
        except Exception as e:
            logger.error(f"创建节点失败 {node_data.get('name', 'Unknown')}: {e}")
            return False
    
    def process_batch_simple(self, nodes: List[Dict], batch_size: int, 
                           source_type: str, metrics: Dict) -> Dict:
        """简化版批次处理"""
        logger.info(f"开始简化处理批次，大小: {batch_size}")
        
        # 清空数据库
        self.neo4j_conn.clear_database()
        
        # 创建索引
        try:
            with self.neo4j_conn.driver.session() as session:
                session.run("CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)")
                session.run("CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)")
        except Exception as e:
            logger.warning(f"创建索引失败: {e}")
        
        # 取指定数量的节点
        batch_nodes = nodes[:batch_size]
        
        start_time = time.perf_counter()
        start_datetime = datetime.now()
        
        try:
            success_count = 0
            failed_count = 0
            
            for i, node in enumerate(batch_nodes):
                if self.create_attraction_node(node, source_type, metrics):
                    success_count += 1
                else:
                    failed_count += 1
                
                # 每10个节点记录一次进度
                if (i + 1) % 10 == 0:
                    logger.info(f"已处理 {i + 1}/{len(batch_nodes)} 个节点")
            
            end_time = time.perf_counter()
            end_datetime = datetime.now()
            duration = end_time - start_time
            
            result = {
                "batch_size": batch_size,
                "actual_processed": len(batch_nodes),
                "success_count": success_count,
                "failed_count": failed_count,
                "start_time": start_datetime.isoformat(),
                "end_time": end_datetime.isoformat(),
                "duration_seconds": duration,
                "nodes_per_second": len(batch_nodes) / duration if duration > 0 else 0,
                "status": "success"
            }
            
            logger.info(f"简化批次处理完成 - 大小: {batch_size}, 耗时: {duration:.2f}秒, "
                       f"成功: {success_count}, 失败: {failed_count}, "
                       f"速度: {result['nodes_per_second']:.2f} 节点/秒")
            
            return result
            
        except Exception as e:
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            logger.error(f"批次处理失败: {e}")
            return {
                "batch_size": batch_size,
                "actual_processed": 0,
                "success_count": 0,
                "failed_count": batch_size,
                "start_time": start_datetime.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": duration,
                "nodes_per_second": 0,
                "status": "failed",
                "error": str(e)
            }
    
    def run_all_batches(self, json_file_path: str):
        """运行所有批次的简化测试"""
        logger.info("开始简化版同步批量处理测试")
        
        # 加载数据
        nodes = self.load_json_data(json_file_path)
        if not nodes:
            logger.error("无法加载数据，测试终止")
            return
        
        # 检查数据量
        max_batch_size = max(self.batch_sizes)
        if len(nodes) < max_batch_size:
            self.batch_sizes = [size for size in self.batch_sizes if size <= len(nodes)]
            logger.info(f"调整后的批次大小: {self.batch_sizes}")
        
        # 设置处理参数
        source_type = "simple_sync_test"
        metrics = {"ratings": 4.0}
        
        # 逐个处理批次
        for batch_size in self.batch_sizes:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始处理批次大小: {batch_size}")
            logger.info(f"{'='*50}")
            
            result = self.process_batch_simple(
                nodes=nodes,
                batch_size=batch_size,
                source_type=source_type,
                metrics=metrics
            )
            
            self.results.append(result)
            
            # 短暂休息
            time.sleep(1)
        
        # 保存结果
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """保存测试结果"""
        try:
            # JSON格式
            results_file = os.path.join(PROJECT_DIR, 'sync_simple_results.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {results_file}")
            
            # CSV格式
            csv_file = os.path.join(PROJECT_DIR, 'sync_simple_results.csv')
            df = pd.DataFrame(self.results)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            logger.info(f"结果已保存到: {csv_file}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        logger.info("\n" + "="*70)
        logger.info("简化版同步批量处理测试结果摘要")
        logger.info("="*70)
        
        for result in self.results:
            if result["status"] == "success":
                logger.info(f"批次大小: {result['batch_size']:>3} | "
                           f"耗时: {result['duration_seconds']:>6.2f}秒 | "
                           f"速度: {result['nodes_per_second']:>6.2f} 节点/秒 | "
                           f"成功: {result['success_count']:>3} | "
                           f"失败: {result['failed_count']:>3}")
            else:
                logger.info(f"批次大小: {result['batch_size']:>3} | 状态: 失败")
        
        logger.info("="*70)
    
    def cleanup(self):
        """清理资源"""
        if self.neo4j_conn:
            self.neo4j_conn.close()

def main():
    """主函数"""
    tester = SimpleSyncTester()
    
    try:
        # 建立连接
        if not tester.setup_neo4j_connection():
            logger.error("无法建立Neo4j连接，测试终止")
            return
        
        # 设置JSON文件路径
        json_file_path = os.path.join(PROJECT_DIR, "data", "lhasa_knowledge_graph.json")
        if not os.path.exists(json_file_path):
            logger.error(f"JSON文件不存在: {json_file_path}")
            return
        
        # 运行测试
        tester.run_all_batches(json_file_path)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
