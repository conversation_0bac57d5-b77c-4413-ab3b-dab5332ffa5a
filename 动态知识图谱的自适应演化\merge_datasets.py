#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并拉萨和林芝数据集，扩展到500个景点
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_lhasa_data(file_path: str) -> List[Dict]:
    """加载拉萨数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        nodes = data.get("nodes", [])
        logger.info(f"加载拉萨数据: {len(nodes)} 个节点")
        
        # 标准化数据格式
        standardized_nodes = []
        for node in nodes:
            standardized_node = {
                "name": node.get("name", ""),
                "location": node.get("location", "拉萨市"),
                "address": node.get("address", ""),
                "description": node.get("description", ""),
                "ranking": node.get("ranking", ""),
                "visitor_percentage": node.get("visitor_percentage", "0%"),
                "pub_timestamp": node.get("pub_timestamp", ""),
                "source": "lhasa",
                "data_source": "拉萨景点数据"
            }
            standardized_nodes.append(standardized_node)
        
        return standardized_nodes
        
    except Exception as e:
        logger.error(f"加载拉萨数据失败: {e}")
        return []

def load_linzhi_data(file_path: str) -> List[Dict]:
    """加载林芝数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"加载林芝数据: {len(data)} 个节点")
        
        # 标准化数据格式
        standardized_nodes = []
        for item in data:
            # 处理林芝数据的字段映射
            name = item.get("名称", "")
            level = item.get("查看", "")
            keywords = item.get("关键词1", "")
            description = item.get("描述", "")
            
            # 构建标准化节点
            standardized_node = {
                "name": name,
                "location": "林芝市",
                "address": f"林芝市{name}",
                "description": f"{description} {keywords}".strip(),
                "ranking": level if level else "",
                "visitor_percentage": "50%",  # 默认值
                "pub_timestamp": "2025-07-23T20:00:00",
                "source": "linzhi",
                "data_source": "林芝景点数据",
                "level": level,
                "keywords": keywords
            }
            standardized_nodes.append(standardized_node)
        
        return standardized_nodes
        
    except Exception as e:
        logger.error(f"加载林芝数据失败: {e}")
        return []

def merge_and_expand_datasets(lhasa_nodes: List[Dict], linzhi_nodes: List[Dict], target_count: int = 500) -> List[Dict]:
    """合并并扩展数据集到目标数量"""
    
    # 合并数据
    all_nodes = lhasa_nodes + linzhi_nodes
    logger.info(f"合并后总节点数: {len(all_nodes)}")
    
    # 去重处理
    seen_names = set()
    unique_nodes = []
    duplicates = []
    
    for node in all_nodes:
        name = node["name"]
        if name in seen_names:
            duplicates.append(name)
        else:
            seen_names.add(name)
            unique_nodes.append(node)
    
    logger.info(f"去重后节点数: {len(unique_nodes)}")
    if duplicates:
        logger.info(f"发现重复节点: {len(duplicates)} 个")
    
    # 如果数据不足500个，进行扩展
    if len(unique_nodes) < target_count:
        logger.info(f"当前数据量 {len(unique_nodes)} 不足目标 {target_count}，开始扩展数据...")
        
        # 扩展策略：复制现有数据并添加变体
        expanded_nodes = unique_nodes.copy()
        base_nodes = unique_nodes.copy()
        
        expansion_count = 0
        while len(expanded_nodes) < target_count and expansion_count < target_count:
            for base_node in base_nodes:
                if len(expanded_nodes) >= target_count:
                    break
                
                # 创建变体节点
                variant_node = base_node.copy()
                variant_suffix = f"_变体{expansion_count + 1}"
                variant_node["name"] = base_node["name"] + variant_suffix
                variant_node["description"] = f"{base_node['description']} (扩展数据)"
                variant_node["is_variant"] = True
                variant_node["original_name"] = base_node["name"]
                
                expanded_nodes.append(variant_node)
                expansion_count += 1
        
        logger.info(f"扩展完成，最终节点数: {len(expanded_nodes)}")
        return expanded_nodes[:target_count]
    
    else:
        # 如果数据超过500个，随机选择500个
        import random
        random.shuffle(unique_nodes)
        selected_nodes = unique_nodes[:target_count]
        logger.info(f"数据量充足，随机选择 {target_count} 个节点")
        return selected_nodes

def create_merged_dataset():
    """创建合并的数据集"""
    
    # 文件路径
    lhasa_file = "data/lhasa_knowledge_graph.json"
    linzhi_file = "林芝旅游景点攻略_林芝打卡_必去景点大全_排名_推荐【携程攻略】.json"
    
    # 检查文件是否存在
    if not os.path.exists(lhasa_file):
        logger.error(f"拉萨数据文件不存在: {lhasa_file}")
        return None
    
    if not os.path.exists(linzhi_file):
        logger.error(f"林芝数据文件不存在: {linzhi_file}")
        return None
    
    # 加载数据
    lhasa_nodes = load_lhasa_data(lhasa_file)
    linzhi_nodes = load_linzhi_data(linzhi_file)
    
    if not lhasa_nodes and not linzhi_nodes:
        logger.error("无法加载任何数据")
        return None
    
    # 合并并扩展到500个
    merged_nodes = merge_and_expand_datasets(lhasa_nodes, linzhi_nodes, 500)
    
    # 创建最终的数据结构
    merged_dataset = {
        "nodes": merged_nodes,
        "relationships": [],  # 关系将通过API生成
        "metadata": {
            "total_nodes": len(merged_nodes),
            "lhasa_nodes": len(lhasa_nodes),
            "linzhi_nodes": len(linzhi_nodes),
            "created_at": "2025-07-23T20:00:00",
            "description": "拉萨+林芝合并数据集，扩展到500个景点"
        }
    }
    
    # 保存合并后的数据集
    output_file = "data/merged_500_knowledge_graph.json"
    os.makedirs("data", exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(merged_dataset, f, ensure_ascii=False, indent=2)
    
    logger.info(f"合并数据集已保存到: {output_file}")
    
    # 生成数据统计报告
    generate_dataset_report(merged_dataset, lhasa_nodes, linzhi_nodes)
    
    return merged_dataset

def generate_dataset_report(merged_dataset: Dict, lhasa_nodes: List[Dict], linzhi_nodes: List[Dict]):
    """生成数据集统计报告"""
    
    report = f"""
# 合并数据集统计报告

## 📊 数据概况
- **目标节点数**: 500个
- **实际节点数**: {len(merged_dataset['nodes'])}个
- **拉萨原始数据**: {len(lhasa_nodes)}个
- **林芝原始数据**: {len(linzhi_nodes)}个
- **合并前总数**: {len(lhasa_nodes) + len(linzhi_nodes)}个

## 📈 数据来源分布
"""
    
    # 统计数据来源
    source_count = {}
    location_count = {}
    variant_count = 0
    
    for node in merged_dataset['nodes']:
        source = node.get('source', 'unknown')
        location = node.get('location', 'unknown')
        
        source_count[source] = source_count.get(source, 0) + 1
        location_count[location] = location_count.get(location, 0) + 1
        
        if node.get('is_variant', False):
            variant_count += 1
    
    for source, count in source_count.items():
        report += f"- **{source}**: {count}个节点\n"
    
    report += f"\n## 🏙️ 地区分布\n"
    for location, count in location_count.items():
        report += f"- **{location}**: {count}个节点\n"
    
    report += f"\n## 🔄 数据扩展\n"
    report += f"- **原始数据**: {len(merged_dataset['nodes']) - variant_count}个\n"
    report += f"- **扩展数据**: {variant_count}个\n"
    
    # 显示前10个节点示例
    report += f"\n## 📋 数据示例（前10个节点）\n"
    report += f"| 序号 | 名称 | 位置 | 来源 | 描述 |\n"
    report += f"|------|------|------|------|------|\n"
    
    for i, node in enumerate(merged_dataset['nodes'][:10]):
        name = node['name'][:15] + "..." if len(node['name']) > 15 else node['name']
        location = node['location']
        source = node['source']
        desc = node['description'][:20] + "..." if len(node['description']) > 20 else node['description']
        report += f"| {i+1} | {name} | {location} | {source} | {desc} |\n"
    
    report += f"\n---\n报告生成时间: 2025-07-23 20:00:00\n"
    
    # 保存报告
    with open("merged_dataset_report.md", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(report)
    logger.info("数据集报告已生成: merged_dataset_report.md")

def main():
    """主函数"""
    logger.info("开始合并拉萨和林芝数据集...")
    
    merged_dataset = create_merged_dataset()
    
    if merged_dataset:
        logger.info("✅ 数据集合并完成！")
        logger.info(f"📁 输出文件: data/merged_500_knowledge_graph.json")
        logger.info(f"📊 总节点数: {len(merged_dataset['nodes'])}")
    else:
        logger.error("❌ 数据集合并失败")

if __name__ == "__main__":
    main()
