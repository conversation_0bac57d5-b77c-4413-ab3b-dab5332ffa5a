#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确时间监控器 - 以秒为单位记录实验进度
"""

import time
import json
import os
from datetime import datetime

def monitor_200_nodes_experiment():
    """监控200个节点实验的精确时间"""
    
    print("⏱️ 200个节点实验精确时间监控器")
    print("=" * 60)
    
    # 实验开始时间 (手动记录)
    start_time_str = "2025-08-01 22:29:45"
    start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
    
    print(f"🚀 实验开始时间: {start_time_str}")
    print(f"📊 预估API调用: 19,900次")
    print(f"⏰ 预估总时间: 3,080秒 (51.3分钟)")
    print("-" * 60)
    
    # 记录时间点
    time_records = []
    
    while True:
        current_time = datetime.now()
        elapsed_seconds = (current_time - start_time).total_seconds()
        
        # 检查检查点文件
        checkpoint_file = "checkpoint_real_batch_200.json"
        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint = json.load(f)
                
                processed = checkpoint.get('processed', 0)
                total = checkpoint.get('batch_size', 200)
                progress = checkpoint.get('progress_percentage', 0)
                speed = checkpoint.get('speed_nodes_per_sec', 0)
                
                # 记录时间点
                time_record = {
                    "timestamp": current_time.strftime("%H:%M:%S"),
                    "elapsed_seconds": int(elapsed_seconds),
                    "processed_nodes": processed,
                    "progress_percent": progress,
                    "current_speed": speed,
                    "estimated_remaining_seconds": int((total - processed) / speed) if speed > 0 else 0
                }
                
                # 避免重复记录相同进度
                if not time_records or time_records[-1]["processed_nodes"] != processed:
                    time_records.append(time_record)
                    
                    print(f"[{time_record['timestamp']}] "
                          f"已处理: {processed}/{total} ({progress:.1f}%) | "
                          f"耗时: {time_record['elapsed_seconds']}秒 | "
                          f"速度: {speed:.3f}节点/秒 | "
                          f"预计剩余: {time_record['estimated_remaining_seconds']}秒")
                
            except Exception as e:
                print(f"❌ 读取检查点失败: {e}")
        
        # 检查是否完成
        results_file = "sync_real_1000_results.json"
        if os.path.exists(results_file):
            try:
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                if results and results[-1].get('batch_size') == 200 and results[-1].get('status') == 'success':
                    end_time = datetime.now()
                    total_elapsed = (end_time - start_time).total_seconds()
                    
                    result = results[-1]
                    
                    print("\n" + "=" * 60)
                    print("🎉 200个节点实验完成！")
                    print("=" * 60)
                    print(f"⏰ 开始时间: {start_time_str}")
                    print(f"⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"⏰ 总耗时: {int(total_elapsed)}秒 ({total_elapsed/60:.1f}分钟)")
                    print(f"📊 处理速度: {result.get('nodes_per_second', 0):.3f}节点/秒")
                    print(f"📊 平均每节点: {total_elapsed/200:.1f}秒")
                    print(f"📊 API调用: {result.get('api_calls_estimated', 0):,}次")
                    print(f"📊 平均每次API: {result.get('avg_time_per_api_call', 0):.3f}秒")
                    print(f"📊 成功率: {result.get('success_count', 0)/200*100:.1f}%")
                    
                    # 保存时间记录
                    with open("time_records_200_nodes.json", 'w', encoding='utf-8') as f:
                        json.dump({
                            "experiment": "200_nodes_sync",
                            "start_time": start_time_str,
                            "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S'),
                            "total_seconds": int(total_elapsed),
                            "time_records": time_records,
                            "final_result": result
                        }, f, ensure_ascii=False, indent=2)
                    
                    print(f"💾 详细时间记录已保存: time_records_200_nodes.json")
                    break
                    
            except Exception as e:
                print(f"❌ 检查结果失败: {e}")
        
        time.sleep(30)  # 每30秒检查一次

if __name__ == "__main__":
    try:
        monitor_200_nodes_experiment()
    except KeyboardInterrupt:
        print("\n⏹️ 监控器已停止")
