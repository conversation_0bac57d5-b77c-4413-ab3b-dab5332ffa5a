# Neo4j CRUD 操作模块
import logging
from neo4j_connection import Neo4jConnection
from typing import Dict, List, Optional
from neo4j.exceptions import Neo4jError

logger = logging.getLogger(__name__)

class Neo4jCRUD:
    def __init__(self, connection: Neo4jConnection):
        """
        初始化 Neo4jCRUD，持有 Neo4jConnection 的实例。
        """
        self.connection = connection
        if not self.connection or not self.connection.driver:
            raise ValueError("Neo4jConnection 未成功初始化，driver 不可用。")
        logger.info("Neo4jCRUD 初始化完成")

    def _execute_transaction(self, tx_function, *args, **kwargs):
        """
        通用的事务执行函数，处理会话和异常。
        """
        try:
            with self.connection.driver.session() as session:
                # 根据函数名显式选择读或写事务
                write_functions = ['_create_node_tx', '_update_node_tx', '_create_relationship_tx']
                if tx_function.__name__ in write_functions:
                    return session.execute_write(tx_function, *args, **kwargs)
                return session.execute_read(tx_function, *args, **kwargs)
        except Neo4jError as e:
            logger.error(f"Neo4j 事务执行失败: {e.message} (代码: {e.code})")
            return None
        except Exception as e:
            logger.error(f"执行数据库操作时发生未知错误: {e}")
            return None

    def create_node(self, label: str, properties: Dict) -> Optional[Dict]:
        """创建或合并节点"""
        if not properties.get("name"):
            logger.error(f"创建节点失败：属性中缺少 'name' 字段。属性: {properties}")
            return None

        def _create_node_tx(tx, name, props):
            query = f"""
            MERGE (n:{label} {{name: $name}})
            SET n += $props
            RETURN n
            """
            result = tx.run(query, name=name, props=props)
            record = result.single()
            return dict(record["n"]) if record else None

        logger.debug(f"执行创建节点: label={label}, properties={properties}")
        return self._execute_transaction(_create_node_tx, properties.get("name"), properties)

    def update_node(self, label: str, name: str, properties: Dict) -> Optional[Dict]:
        """更新节点"""

        def _update_node_tx(tx, node_name, props):
            query = f"""
            MATCH (n:{label} {{name: $node_name}})
            SET n += $props
            RETURN n
            """
            result = tx.run(query, node_name=node_name, props=props)
            record = result.single()
            return dict(record["n"]) if record else None

        return self._execute_transaction(_update_node_tx, name, properties)

    def get_node(self, label: str, name: str) -> Optional[Dict]:
        """获取节点"""

        def _get_node_tx(tx, node_name):
            query = f"MATCH (n:{label} {{name: $node_name}}) RETURN n"
            result = tx.run(query, name=node_name)
            record = result.single()
            return dict(record["n"]) if record else None

        return self._execute_transaction(_get_node_tx, name)

    def create_relationship(self, source_label: str, source_name: str, target_label: str, target_name: str,
                            rel_type: str, properties: Dict) -> bool:
        """创建关系"""

        def _create_relationship_tx(tx, src_name, tgt_name, props):
            query = f"""
            MATCH (a:{source_label} {{name: $src_name}})
            MATCH (b:{target_label} {{name: $tgt_name}})
            MERGE (a)-[r:{rel_type}]->(b)
            SET r += $props
            RETURN r
            """
            result = tx.run(query, src_name=src_name, tgt_name=tgt_name, props=props)
            return result.single() is not None

        return self._execute_transaction(_create_relationship_tx, source_name, target_name, properties) or False

    def get_relationships(self, source_label: str, source_name: str) -> List[Dict]:
        """获取与指定节点相关的所有出向关系"""

        def _get_relationships_tx(tx, src_name):
            query = f"""
            MATCH (a:{source_label} {{name: $src_name}})-[r]->(b)
            RETURN type(r) as type, a.name as source_name, b.name as target_name, properties(r) as properties
            """
            result = tx.run(query, src_name=src_name)
            return [
                {
                    "source_name": record["source_name"],
                    "target_name": record["target_name"],
                    "type": record["type"],
                    "properties": dict(record["properties"])
                }
                for record in result
            ]

        return self._execute_transaction(_get_relationships_tx, source_name) or []