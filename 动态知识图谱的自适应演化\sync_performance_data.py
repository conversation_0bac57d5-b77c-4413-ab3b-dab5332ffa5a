#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步性能数据导出脚本
生成详细的性能数据表格，用于与异步版本对比
"""

import pandas as pd
import json
from datetime import datetime

def create_detailed_performance_data():
    """创建详细的性能数据"""
    
    # 原始实验数据
    sync_data = {
        'batch_size': [10, 50, 100, 150, 200, 250],
        'start_time': [
            '2025-07-23 19:39:58',
            '2025-07-23 19:41:58', 
            '2025-07-23 19:51:16',
            '2025-07-23 20:11:26',
            '2025-07-23 20:53:58',
            '2025-07-23 22:24:39'
        ],
        'end_time': [
            '2025-07-23 19:41:56',
            '2025-07-23 19:51:14',
            '2025-07-23 20:11:24', 
            '2025-07-23 20:53:56',
            '2025-07-23 21:25:47',
            '2025-07-23 23:00:47'
        ],
        'duration_seconds': [117.98, 555.91, 1207.37, 1568.88, 1909.31, 2167.16],
        'nodes_per_second': [0.085, 0.090, 0.083, 0.096, 0.105, 0.115],
        'success_count': [10, 50, 100, 150, 200, 250],
        'failed_count': [0, 0, 0, 0, 0, 0]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(sync_data)
    
    # 计算额外的性能指标
    df['duration_minutes'] = df['duration_seconds'] / 60
    df['duration_hours'] = df['duration_minutes'] / 60
    df['success_rate'] = (df['success_count'] / df['batch_size']) * 100
    df['avg_time_per_node'] = df['duration_seconds'] / df['batch_size']
    df['processing_type'] = 'Synchronous'
    
    # 计算增长率
    df['time_growth_rate'] = df['duration_seconds'].pct_change() * 100
    df['speed_change_rate'] = df['nodes_per_second'].pct_change() * 100
    
    return df

def create_comparison_template():
    """创建对比模板"""
    sync_df = create_detailed_performance_data()
    
    # 创建异步数据模板（空数据，待填入）
    async_template = sync_df.copy()
    async_template['processing_type'] = 'Asynchronous'
    async_template['duration_seconds'] = 0
    async_template['duration_minutes'] = 0
    async_template['duration_hours'] = 0
    async_template['nodes_per_second'] = 0
    async_template['avg_time_per_node'] = 0
    async_template['time_growth_rate'] = 0
    async_template['speed_change_rate'] = 0
    async_template['start_time'] = 'TBD'
    async_template['end_time'] = 'TBD'
    
    # 合并数据
    comparison_df = pd.concat([sync_df, async_template], ignore_index=True)
    
    return comparison_df

def export_performance_data():
    """导出性能数据"""
    
    print("📊 生成同步性能数据...")
    
    # 1. 生成详细的同步数据
    sync_df = create_detailed_performance_data()
    
    # 2. 生成对比模板
    comparison_df = create_comparison_template()
    
    # 3. 导出为多种格式
    
    # Excel格式 - 多个工作表
    with pd.ExcelWriter('sync_performance_analysis.xlsx', engine='openpyxl') as writer:
        sync_df.to_excel(writer, sheet_name='Sync_Performance', index=False)
        comparison_df.to_excel(writer, sheet_name='Sync_vs_Async_Template', index=False)
        
        # 创建汇总统计表
        summary_stats = {
            'Metric': [
                'Total Nodes Processed',
                'Total Processing Time (minutes)',
                'Average Processing Speed (nodes/sec)',
                'Overall Success Rate (%)',
                'Fastest Batch Speed (nodes/sec)',
                'Slowest Batch Speed (nodes/sec)',
                'Speed Variance',
                'Linear Growth Coefficient'
            ],
            'Synchronous': [
                sync_df['batch_size'].sum(),
                sync_df['duration_minutes'].sum(),
                sync_df['nodes_per_second'].mean(),
                sync_df['success_rate'].mean(),
                sync_df['nodes_per_second'].max(),
                sync_df['nodes_per_second'].min(),
                sync_df['nodes_per_second'].var(),
                sync_df['duration_seconds'].corr(sync_df['batch_size'])
            ],
            'Asynchronous': ['TBD'] * 8
        }
        
        summary_df = pd.DataFrame(summary_stats)
        summary_df.to_excel(writer, sheet_name='Performance_Summary', index=False)
    
    # CSV格式
    sync_df.to_csv('sync_performance_detailed.csv', index=False, encoding='utf-8-sig')
    comparison_df.to_csv('sync_vs_async_comparison_template.csv', index=False, encoding='utf-8-sig')
    
    # JSON格式
    sync_data_json = sync_df.to_dict('records')
    with open('sync_performance_data.json', 'w', encoding='utf-8') as f:
        json.dump(sync_data_json, f, ensure_ascii=False, indent=2)
    
    # 4. 生成性能报告
    generate_performance_report(sync_df)
    
    print("✅ 性能数据导出完成！")
    print("\n📁 生成的文件:")
    print("   - sync_performance_analysis.xlsx (Excel多工作表)")
    print("   - sync_performance_detailed.csv (详细CSV数据)")
    print("   - sync_vs_async_comparison_template.csv (对比模板)")
    print("   - sync_performance_data.json (JSON格式)")
    print("   - sync_performance_summary.txt (性能报告)")

def generate_performance_report(df):
    """生成性能报告"""
    
    report = f"""
# 同步批量处理性能分析报告

## 📊 实验概况
- 测试日期: 2025年7月23日
- 测试批次: {len(df)} 个
- 总处理节点数: {df['batch_size'].sum()} 个
- 总处理时间: {df['duration_minutes'].sum():.1f} 分钟
- 整体成功率: {df['success_rate'].mean():.1f}%

## 📈 关键性能指标

### 处理时间分析
- 最短处理时间: {df['duration_minutes'].min():.1f} 分钟 ({df.loc[df['duration_minutes'].idxmin(), 'batch_size']} 个节点)
- 最长处理时间: {df['duration_minutes'].max():.1f} 分钟 ({df.loc[df['duration_minutes'].idxmax(), 'batch_size']} 个节点)
- 平均处理时间: {df['duration_minutes'].mean():.1f} 分钟
- 时间增长相关性: {df['duration_seconds'].corr(df['batch_size']):.3f} (接近1表示线性增长)

### 处理速度分析
- 最高处理速度: {df['nodes_per_second'].max():.3f} 节点/秒 ({df.loc[df['nodes_per_second'].idxmax(), 'batch_size']} 个节点批次)
- 最低处理速度: {df['nodes_per_second'].min():.3f} 节点/秒 ({df.loc[df['nodes_per_second'].idxmin(), 'batch_size']} 个节点批次)
- 平均处理速度: {df['nodes_per_second'].mean():.3f} 节点/秒
- 速度标准差: {df['nodes_per_second'].std():.3f}

### 每节点平均处理时间
- 最快: {df['avg_time_per_node'].min():.1f} 秒/节点
- 最慢: {df['avg_time_per_node'].max():.1f} 秒/节点
- 平均: {df['avg_time_per_node'].mean():.1f} 秒/节点

## 📋 详细数据表

| 批次大小 | 处理时间(分钟) | 处理速度(节点/秒) | 每节点时间(秒) | 成功率 |
|---------|---------------|------------------|---------------|--------|"""

    for _, row in df.iterrows():
        report += f"\n| {row['batch_size']:>7} | {row['duration_minutes']:>13.1f} | {row['nodes_per_second']:>16.3f} | {row['avg_time_per_node']:>13.1f} | {row['success_rate']:>5.0f}% |"

    report += f"""

## 🔍 性能特征分析

1. **线性扩展性**: 处理时间与批次大小的相关性为 {df['duration_seconds'].corr(df['batch_size']):.3f}，表明系统具有良好的线性扩展特性。

2. **处理速度稳定性**: 速度标准差为 {df['nodes_per_second'].std():.3f}，相对平均速度 {df['nodes_per_second'].mean():.3f} 的变异系数为 {(df['nodes_per_second'].std()/df['nodes_per_second'].mean()*100):.1f}%。

3. **批次效率**: 大批次处理并未显示出明显的效率优势，表明当前架构的瓶颈主要在于串行处理限制。

4. **成功率**: 所有批次均达到100%成功率，显示系统稳定性良好。

## 🚀 异步版本预期改进

基于同步版本的性能特征，异步版本预期在以下方面有所改进：

1. **并发处理**: 预期速度提升 2-5 倍
2. **资源利用**: 更好的CPU和网络资源利用率
3. **扩展性**: 可能实现超线性性能增长
4. **稳定性**: 更好的错误恢复和容错机制

---
报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

    with open('sync_performance_summary.txt', 'w', encoding='utf-8') as f:
        f.write(report)

def main():
    """主函数"""
    export_performance_data()

if __name__ == "__main__":
    main()
