import json
import logging
from typing import Dict, List
from neo4j_crud import Neo4jCRUD
from utils import compute_dynamic_weight

logger = logging.getLogger(__name__)

class ConflictResolver:
    def __init__(self, crud: Neo4jCRUD):
        self.crud = crud
        logger.info("ConflictResolver initialized")

    def check_relationship_conflict(self, new_rel: Dict, existing_rels: List[Dict]) -> bool:
        """
        检查新关系是否与现有关系冲突
        """
        try:
            new_source = new_rel.get("source_name")
            new_target = new_rel.get("target_name")
            new_type = new_rel.get("type")
            new_confidence = new_rel.get("properties", {}).get("confidence", 0.0)

            for existing_rel in existing_rels:
                if (existing_rel["source_name"] == new_source and
                        existing_rel["target_name"] == new_target and
                        existing_rel["type"] == new_type):
                    existing_confidence = existing_rel["properties"].get("confidence", 0.0)
                    if existing_confidence >= new_confidence:
                        logger.warning(
                            f"关系冲突: {new_source} -> {new_target} ({new_type})，现有置信度 {existing_confidence} >= 新置信度 {new_confidence}")
                        return True
            return False
        except Exception as e:
            logger.error(f"检查关系冲突失败: {e}")
            return True  # 默认拒绝新关系以避免错误

    def resolve_from_queue(self, neo4j_conn: 'Neo4jConnection', source_type: str, metrics: Dict):
        """从冲突队列中解析并更新图"""
        from text_processor import process_json_files
        conflict_queue_path = "conflict_queue.json"
        try:
            with open(conflict_queue_path, "r", encoding="utf-8") as f:
                conflicts = json.load(f)
            if not conflicts:
                logger.info("冲突队列为空")
                return

            for conflict in conflicts:
                try:
                    result = process_json_files(
                        neo4j_conn=neo4j_conn,
                        json_file_path=conflict.get("file_path"),
                        crawl_timestamp=conflict.get("timestamp"),
                        source_type=source_type,
                        metrics=metrics
                    )
                    logger.info(f"处理冲突文件 {conflict.get('file_path')}: {result}")
                except Exception as e:
                    logger.error(f"处理冲突文件失败 {conflict.get('file_path')}: {e}")

            with open(conflict_queue_path, "w", encoding="utf-8") as f:
                json.dump([], f)
            logger.info("冲突队列已清空")
        except Exception as e:
            logger.error(f"从队列解析失败: {e}")