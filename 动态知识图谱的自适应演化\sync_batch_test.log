2025-07-23 19:39:58,345 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-07-23 19:39:58,385 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-07-23 19:39:58,385 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-07-23 19:39:58,386 - INFO - __main__ - 开始同步批量处理测试
2025-07-23 19:39:58,387 - INFO - __main__ - 加载数据完成，总节点数: 260, 去重后: 258
2025-07-23 19:39:58,388 - INFO - __main__ - 
==================================================
2025-07-23 19:39:58,388 - INFO - __main__ - 开始处理批次大小: 10
2025-07-23 19:39:58,388 - INFO - __main__ - ==================================================
2025-07-23 19:39:58,388 - INFO - __main__ - 开始处理批次，大小: 10
2025-07-23 19:39:58,682 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:39:58,687 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:39:58,687 - INFO - text_processor - 清空所有节点和关系
2025-07-23 19:39:58,818 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 19:39:58,819 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:39:58,819 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:39:58,819 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:40:06,279 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 19:40:06,500 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 19:40:10,362 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 19:40:10,378 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 19:40:14,715 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 19:40:14,730 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 19:40:14,731 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:40:37,149 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {"type": "NEARBY", "reason": "布达拉宫和大昭寺都位于拉萨市中心区域，地理位置相近", "confidence": 0.9, "direction": "bidirectional"},
    {"type": "SIMILAR_TYPE", "reason": "两者都是藏传佛教寺庙，属于宗教场所", "confidence": 0.85, "direction": "bidirectional"},
    {"type": "COMPLEMENTARY_VISIT", "reason": "常被安排一起游览，适合信众朝拜 ARGs", "confidence": 0.75, "direction": "bidirectional"},
    {"type": "HISTORICAL_LINK", "reason": "都源自藏传佛教，有共同的历史和文化背景", "confidence": 0.8, "direction": "bidirectional"},
    {"type": "CULTURAL_RELATED", "reason": "都属于藏传佛教文化体系，有深厚的文化关联", "confidence": 0.9, "direction": "bidirectional"}
]
```
2025-07-23 19:40:37,149 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 19:40:37,149 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都在拉萨市及其周边区域",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是typeof景点",
        "confidence": 0.5,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:40:37,150 - INFO - __main__ - 已处理 3/10 个节点
2025-07-23 19:40:37,150 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:40:37,150 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:40:37,150 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:40:43,511 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 19:40:43,631 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 19:40:49,272 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 19:40:49,291 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 19:40:49,291 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:40:56,274 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 19:40:56,372 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 19:40:56,373 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:41:18,425 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是宗教或文化景点",
        "confidence": 0.6,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览，内容不同但相关",
        "confidence": 0.65,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都有历史背景",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于西藏文化",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:18,427 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙或与宗教文化相关",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:18,427 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "八廓街和《文成公主》大型史诗剧都位于拉萨市中心区域",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:18,428 - INFO - __main__ - 已处理 6/10 个节点
2025-07-23 19:41:18,428 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:41:18,428 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:41:18,428 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:41:23,967 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 19:41:23,979 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 19:41:28,265 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 19:41:28,276 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 19:41:34,569 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 19:41:34,582 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 19:41:34,582 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:41:51,175 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:51,177 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于同一文化体系",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:51,177 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:41:51,178 - INFO - __main__ - 已处理 9/10 个节点
2025-07-23 19:41:51,178 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:41:51,178 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:41:51,178 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:41:56,781 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 19:41:56,796 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 19:41:56,796 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:41:56,796 - INFO - __main__ - 已处理 10/10 个节点
2025-07-23 19:41:56,798 - INFO - __main__ - 批次处理完成 - 大小: 10, 耗时: 117.98秒, 成功: 10, 失败: 0
2025-07-23 19:41:58,803 - INFO - __main__ - 
==================================================
2025-07-23 19:41:58,803 - INFO - __main__ - 开始处理批次大小: 50
2025-07-23 19:41:58,803 - INFO - __main__ - ==================================================
2025-07-23 19:41:58,803 - INFO - __main__ - 开始处理批次，大小: 50
2025-07-23 19:41:58,843 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:41:58,848 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:41:58,848 - INFO - text_processor - 清空所有节点和关系
2025-07-23 19:41:58,888 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 19:41:58,895 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 19:41:58,897 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 19:41:58,897 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:41:58,897 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:41:58,897 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:42:02,402 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 19:42:02,420 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 19:42:07,759 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 19:42:07,781 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 19:42:10,449 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 19:42:10,464 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 19:42:10,464 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:42:40,175 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览",
        "confidence": 0.85,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙，共享深厚的文化历史",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:42:40,175 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "布达拉宫位于拉萨市，而纳木措位于当雄县，地理上并非同一区域",
        "confidence": 0.4,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "布达拉宫是宗教景点，纳木是自然景观，类型不同",
        "confidence": 0.3,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "布达拉宫吸引朝圣者，纳木则适合观日出，但两者未直接互补",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "布达拉宫有丰富历史，而纳木措的历史背景未知，关联性低",
        "confidence": 0.3,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "布达拉宫属于藏传佛教，而纳木则较少涉及宗教文化",
        "confidence": 0.3,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:42:40,176 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙和自然景观，都属于西藏文化体系",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:42:40,176 - INFO - __main__ - 已处理 3/50 个节点
2025-07-23 19:42:40,177 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:42:40,177 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:42:40,177 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:42:45,348 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 19:42:45,366 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 19:42:52,594 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 19:42:52,611 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 19:42:52,612 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:43:00,248 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 19:43:00,264 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 19:43:00,264 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:43:21,304 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于拉萨市中心，都对当地文化有重要影响",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:43:21,304 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "色拉寺和《文成公主》大型史诗剧都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的文化和娱乐景点",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都涉及西藏的文化和历史元素",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:43:21,306 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "HISTORICAL_LINK",
        "reason": "都涉及藏传佛教的历史和文化背景，包括转经道的历史",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于同一文化体系（藏传佛教）",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:43:21,306 - INFO - __main__ - 已处理 6/50 个节点
2025-07-23 19:43:21,306 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:43:21,307 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:43:21,307 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:43:26,259 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 19:43:26,280 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 19:43:34,283 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 19:43:34,302 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 19:43:38,632 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 19:43:38,646 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 19:43:38,646 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:44:13,981 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:13,981 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心，位置相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:13,982 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于宗教文化景点，与西藏文化有关",
        "confidence": 0.85,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览，两者都适合拍照打卡",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:13,982 - INFO - __main__ - 已处理 9/50 个节点
2025-07-23 19:44:13,984 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:44:13,984 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:44:13,984 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:44:21,999 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 19:44:22,016 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 19:44:31,272 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 19:44:31,291 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 19:44:34,425 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 19:44:34,486 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 19:44:34,486 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:44:55,840 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:55,840 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都属于宗教文化景点，都与藏传佛教有关",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:55,841 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:44:55,841 - INFO - __main__ - 已处理 12/50 个节点
2025-07-23 19:44:55,841 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:44:55,841 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:44:55,841 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:44:55,841 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:45:04,383 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 19:45:04,399 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 19:45:08,989 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 19:45:09,002 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 19:45:18,333 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 19:45:18,349 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 19:45:18,349 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 19:45:26,814 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览，位于同一区域",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:45:26,814 - INFO - __main__ - 已处理 15/50 个节点
2025-07-23 19:45:26,815 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:45:26,815 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:45:26,815 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:45:30,727 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 19:45:30,741 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 19:45:36,092 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 19:45:36,104 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 19:45:41,657 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 19:45:41,669 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 19:45:41,669 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:46:09,421 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化体系内",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:46:09,567 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]

```
2025-07-23 19:46:09,568 - INFO - __main__ - 已处理 18/50 个节点
2025-07-23 19:46:09,568 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:46:09,568 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:46:09,569 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:46:18,637 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 19:46:18,700 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 19:46:26,779 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 19:46:26,790 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 19:46:32,842 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 19:46:32,855 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 19:46:32,856 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 19:46:38,737 - ERROR - text_processor - JSON 解析失败 for 拉鲁湿地国家级自然保护区 -> 羊八井: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 19:46:38,737 - INFO - __main__ - 已处理 21/50 个节点
2025-07-23 19:46:38,737 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:46:38,737 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:46:38,737 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:46:38,739 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:46:46,186 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 19:46:46,201 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 19:46:53,972 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 19:46:53,983 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 19:47:01,525 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 19:47:01,537 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 19:47:01,537 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:47:24,400 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:47:24,401 - ERROR - text_processor - JSON 解析失败 for 甘丹寺 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 19:47:24,401 - INFO - __main__ - 已处理 24/50 个节点
2025-07-23 19:47:24,401 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:47:24,402 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:47:24,402 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:47:32,334 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 19:47:32,349 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 19:47:40,725 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 19:47:40,737 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 19:47:44,627 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 19:47:44,639 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 19:47:44,639 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:48:15,691 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 布达拉宫广场: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "和平解放纪念碑和布达拉宫都是西藏的重要文化象征，都涉及国家象征和宗教文化",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:15,691 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "和平解放纪念碑和那根拉山口都属于拉萨市的重要地标，位于同一地区的西部边缘",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "拉萨作为重要的宗教和政治中心，其历史和文化与当雄县紧密相连",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "和平解放纪念碑是重要的政治和历史象征，而拉萨市的核心区域包括当雄县",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:15,693 - ERROR - text_processor - JSON 解析失败 for 布达拉宫广场 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于西藏自治区，布达拉宫广场在拉萨市中心，而那根拉山口在当雄县，地理位置相近。",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都位于西藏自治区，且都具有重要的宗教和文化意义。",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:15,693 - INFO - __main__ - 已处理 27/50 个节点
2025-07-23 19:48:15,693 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:48:15,693 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:48:15,693 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:48:22,152 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如寺印经院
2025-07-23 19:48:22,166 - INFO - text_processor - 成功处理实体: 木如寺印经院
2025-07-23 19:48:26,675 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 下密寺
2025-07-23 19:48:26,691 - INFO - text_processor - 成功处理实体: 下密寺
2025-07-23 19:48:29,620 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驻藏大臣衙门遗址
2025-07-23 19:48:29,638 - INFO - text_processor - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 19:48:29,639 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:48:57,048 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 下密寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一宗教体系",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:57,048 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都位于拉萨市中心，可以一起游览",
        "confidence": 0.6,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "可以一起游览",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都有历史背景",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于西藏文化",
        "confidence": 0.4,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:57,050 - ERROR - text_processor - JSON 解析失败 for 下密寺 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "两者都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:48:57,050 - INFO - __main__ - 已处理 30/50 个节点
2025-07-23 19:48:57,050 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:48:57,051 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:48:57,051 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:49:05,939 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺路
2025-07-23 19:49:05,975 - INFO - text_processor - 成功处理实体: 小昭寺路
2025-07-23 19:49:05,975 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:49:10,775 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨民族文化艺术宫
2025-07-23 19:49:10,788 - INFO - text_processor - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 19:49:10,789 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:49:26,011 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-07-23 19:49:26,026 - INFO - text_processor - 成功处理实体: 5238
2025-07-23 19:49:26,026 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 19:49:26,028 - INFO - __main__ - 已处理 33/50 个节点
2025-07-23 19:49:26,028 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:49:26,028 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:49:26,028 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:49:26,028 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:49:31,071 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措扎西岛
2025-07-23 19:49:31,086 - INFO - text_processor - 成功处理实体: 纳木措扎西岛
2025-07-23 19:49:31,086 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:49:42,542 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药文化博览中心
2025-07-23 19:49:42,555 - INFO - text_processor - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 19:49:50,635 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-07-23 19:49:50,679 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-07-23 19:49:50,679 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:49:50,679 - INFO - __main__ - 已处理 36/50 个节点
2025-07-23 19:49:50,679 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:49:50,680 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:49:50,680 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:49:50,680 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:49:58,079 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甲玛王宫
2025-07-23 19:49:58,092 - INFO - text_processor - 成功处理实体: 甲玛王宫
2025-07-23 19:50:02,172 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎希寺
2025-07-23 19:50:02,185 - INFO - text_processor - 成功处理实体: 扎希寺
2025-07-23 19:50:07,667 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-07-23 19:50:07,678 - INFO - text_processor - 成功处理实体: 楚布寺
2025-07-23 19:50:07,678 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 19:50:07,678 - INFO - __main__ - 已处理 39/50 个节点
2025-07-23 19:50:07,680 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:50:07,680 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:50:07,680 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:50:17,322 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞干布出生地
2025-07-23 19:50:17,340 - INFO - text_processor - 成功处理实体: 松赞干布出生地
2025-07-23 19:50:20,997 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 聂当度母殿和聂当大佛
2025-07-23 19:50:21,014 - INFO - text_processor - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 19:50:30,313 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-07-23 19:50:30,327 - INFO - text_processor - 成功处理实体: 策门林寺
2025-07-23 19:50:30,327 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:50:30,328 - INFO - __main__ - 已处理 42/50 个节点
2025-07-23 19:50:30,328 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:50:30,328 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:50:30,328 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:50:35,185 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鲁普岩寺
2025-07-23 19:50:35,198 - INFO - text_processor - 成功处理实体: 鲁普岩寺
2025-07-23 19:50:40,695 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仙足岛
2025-07-23 19:50:40,709 - INFO - text_processor - 成功处理实体: 仙足岛
2025-07-23 19:50:45,533 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-07-23 19:50:45,549 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-07-23 19:50:45,549 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:50:45,549 - INFO - __main__ - 已处理 45/50 个节点
2025-07-23 19:50:45,551 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:50:45,551 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:50:45,551 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:50:50,616 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺
2025-07-23 19:50:50,632 - INFO - text_processor - 成功处理实体: 乃琼寺
2025-07-23 19:50:54,052 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次巴拉康寺
2025-07-23 19:50:54,067 - INFO - text_processor - 成功处理实体: 次巴拉康寺
2025-07-23 19:50:59,390 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 清政府驻藏大臣衙门旧址
2025-07-23 19:50:59,403 - INFO - text_processor - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 19:50:59,403 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:50:59,404 - INFO - __main__ - 已处理 48/50 个节点
2025-07-23 19:50:59,404 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:50:59,404 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:50:59,404 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:50:59,404 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:51:04,591 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热振寺
2025-07-23 19:51:04,604 - INFO - text_processor - 成功处理实体: 热振寺
2025-07-23 19:51:14,794 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 关帝庙
2025-07-23 19:51:14,809 - INFO - text_processor - 成功处理实体: 关帝庙
2025-07-23 19:51:14,810 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 19:51:14,810 - INFO - __main__ - 已处理 50/50 个节点
2025-07-23 19:51:14,810 - INFO - __main__ - 批次处理完成 - 大小: 50, 耗时: 555.91秒, 成功: 50, 失败: 0
2025-07-23 19:51:16,822 - INFO - __main__ - 
==================================================
2025-07-23 19:51:16,822 - INFO - __main__ - 开始处理批次大小: 100
2025-07-23 19:51:16,823 - INFO - __main__ - ==================================================
2025-07-23 19:51:16,823 - INFO - __main__ - 开始处理批次，大小: 100
2025-07-23 19:51:16,864 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:51:16,867 - INFO - neo4j_connection - 数据库已清空
2025-07-23 19:51:16,868 - INFO - text_processor - 清空所有节点和关系
2025-07-23 19:51:16,873 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 19:51:16,879 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 19:51:16,880 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 19:51:16,880 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:51:16,880 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:51:16,880 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:51:22,761 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 19:51:22,776 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 19:51:26,744 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 19:51:26,835 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 19:51:36,601 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 19:51:36,611 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 19:51:36,612 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:52:11,104 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙，富有共同的历史和文化背景",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览，方便游览者同时体验不同类型的宗教建筑物",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:52:11,105 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "两者都吸引游客前来体验西藏的不同方面，布达拉宫是宗教景点，纳木措是自然景观，适合一起游览",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于西藏的文化和宗教体系，属于藏族文化",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:52:11,105 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 19:52:11,105 - INFO - __main__ - 已处理 3/100 个节点
2025-07-23 19:52:11,105 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:52:11,105 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:52:11,105 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:52:16,493 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 19:52:16,564 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 19:52:21,815 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 19:52:21,826 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 19:52:21,826 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:52:31,820 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 19:52:31,869 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 19:52:31,869 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:52:54,610 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教的寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:52:54,610 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:52:54,610 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都与藏传佛教文化有关，文成公主大型史诗剧反映了当地文化和历史",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:52:54,611 - INFO - __main__ - 已处理 6/100 个节点
2025-07-23 19:52:54,611 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:52:54,611 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:52:54,611 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:52:59,901 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 19:52:59,916 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 19:53:07,017 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 19:53:07,029 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 19:53:11,382 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 19:53:11,393 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 19:53:11,394 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:53:30,928 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:53:30,929 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域，地理位置相近",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:53:30,930 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域，地理位置相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:53:30,930 - INFO - __main__ - 已处理 9/100 个节点
2025-07-23 19:53:30,930 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:53:30,930 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:53:30,930 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:53:35,485 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 19:53:35,500 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 19:53:39,826 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 19:53:39,839 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 19:53:46,064 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 19:53:46,122 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 19:53:46,123 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:54:09,773 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
  {
    "type": "NEARBY",
    "reason": "两者都位于拉萨市中心区域，地理位置相近",
    "confidence": 0.7,
    "direction": "bidirectional"
  },
  {
    "type": "SIMILAR_TYPE",
    "reason": "都是藏传佛教寺庙，类型相似",
    "confidence": 0.8,
    "direction": "bidirectional"
  }
]
```
2025-07-23 19:54:09,773 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都涉及到藏传文化",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "一起游览的景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:54:09,774 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:54:09,774 - INFO - __main__ - 已处理 12/100 个节点
2025-07-23 19:54:09,774 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:54:09,774 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:54:09,775 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:54:09,775 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:54:18,624 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 19:54:18,635 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 19:54:24,859 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 19:54:24,872 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 19:54:29,311 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 19:54:29,321 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 19:54:29,322 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 19:54:36,215 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:54:36,215 - INFO - __main__ - 已处理 15/100 个节点
2025-07-23 19:54:36,216 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:54:36,216 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:54:36,216 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:54:44,382 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 19:54:44,395 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 19:54:48,208 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 19:54:48,220 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 19:54:52,558 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 19:54:52,571 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 19:54:52,571 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:55:28,645 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:55:28,646 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于宗教文化景点，都是西藏的重要文化地标",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:55:28,647 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:55:28,648 - INFO - __main__ - 已处理 18/100 个节点
2025-07-23 19:55:28,648 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:55:28,648 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:55:28,648 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:55:35,573 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 19:55:35,624 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 19:55:40,589 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 19:55:40,602 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 19:55:57,635 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 19:55:57,648 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 19:55:57,648 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 19:56:04,318 - ERROR - text_processor - JSON 解析失败 for 拉鲁湿地国家级自然保护区 -> 羊八井: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 19:56:04,318 - INFO - __main__ - 已处理 21/100 个节点
2025-07-23 19:56:04,319 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:56:04,319 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:56:04,319 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:56:04,319 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:56:16,430 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 19:56:16,448 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 19:56:24,902 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 19:56:24,913 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 19:56:34,563 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 19:56:34,574 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 19:56:34,575 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:57:11,861 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 甘丹寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，文化背景相关",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:57:11,861 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点位于墨竹工卡县，地理位置相邻",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都位于西藏文化地区，墨竹工卡县",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:57:11,862 - ERROR - text_processor - JSON 解析失败 for 甘丹寺 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[] {}
```
2025-07-23 19:57:11,862 - INFO - __main__ - 已处理 24/100 个节点
2025-07-23 19:57:11,863 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:57:11,863 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:57:11,863 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:57:18,867 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 19:57:18,878 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 19:57:24,633 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 19:57:24,647 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 19:57:29,799 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 19:57:29,817 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 19:57:29,817 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:57:48,338 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 布达拉宫广场: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:57:48,339 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于西藏文化体系中重要的历史和自然景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:57:48,340 - ERROR - text_processor - JSON 解析失败 for 布达拉宫广场 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NONE",
        "reason": "没有明确的地理或文化关联",
        "confidence": 0.0,
        "direction": "none"
    }
]
```
2025-07-23 19:57:48,340 - INFO - __main__ - 已处理 27/100 个节点
2025-07-23 19:57:48,340 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:57:48,340 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:57:48,341 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:57:52,492 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如寺印经院
2025-07-23 19:57:52,503 - INFO - text_processor - 成功处理实体: 木如寺印经院
2025-07-23 19:57:58,782 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 下密寺
2025-07-23 19:57:58,795 - INFO - text_processor - 成功处理实体: 下密寺
2025-07-23 19:58:12,995 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驻藏大臣衙门遗址
2025-07-23 19:58:13,007 - INFO - text_processor - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 19:58:13,007 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 19:58:47,985 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 下密寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一宗教体系，藏传佛教格鲁派",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:58:47,986 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "有共同的历史背景或文化联系",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一文化体系或宗教体系",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:58:47,987 - ERROR - text_processor - JSON 解析失败 for 下密寺 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.98,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "驻藏大臣曾在此办公",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的不同类型景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 19:58:47,987 - INFO - __main__ - 已处理 30/100 个节点
2025-07-23 19:58:47,988 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:58:47,988 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:58:47,988 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:58:51,573 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺路
2025-07-23 19:58:51,605 - INFO - text_processor - 成功处理实体: 小昭寺路
2025-07-23 19:58:51,606 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:58:56,369 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨民族文化艺术宫
2025-07-23 19:58:56,383 - INFO - text_processor - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 19:58:56,383 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:59:04,699 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-07-23 19:59:04,708 - INFO - text_processor - 成功处理实体: 5238
2025-07-23 19:59:04,708 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 19:59:04,708 - INFO - __main__ - 已处理 33/100 个节点
2025-07-23 19:59:04,709 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:59:04,709 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:59:04,709 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:59:04,709 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:59:21,004 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措扎西岛
2025-07-23 19:59:21,014 - INFO - text_processor - 成功处理实体: 纳木措扎西岛
2025-07-23 19:59:21,014 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:59:31,779 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药文化博览中心
2025-07-23 19:59:31,787 - INFO - text_processor - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 19:59:36,603 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-07-23 19:59:36,626 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-07-23 19:59:36,627 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 19:59:36,627 - INFO - __main__ - 已处理 36/100 个节点
2025-07-23 19:59:36,627 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:59:36,627 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:59:36,627 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 19:59:36,627 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 19:59:42,086 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甲玛王宫
2025-07-23 19:59:42,097 - INFO - text_processor - 成功处理实体: 甲玛王宫
2025-07-23 19:59:46,787 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎希寺
2025-07-23 19:59:46,844 - INFO - text_processor - 成功处理实体: 扎希寺
2025-07-23 19:59:56,205 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-07-23 19:59:56,215 - INFO - text_processor - 成功处理实体: 楚布寺
2025-07-23 19:59:56,215 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 19:59:56,215 - INFO - __main__ - 已处理 39/100 个节点
2025-07-23 19:59:56,215 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 19:59:56,216 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 19:59:56,216 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:00:03,362 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞干布出生地
2025-07-23 20:00:03,371 - INFO - text_processor - 成功处理实体: 松赞干布出生地
2025-07-23 20:00:08,239 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 聂当度母殿和聂当大佛
2025-07-23 20:00:08,249 - INFO - text_processor - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 20:00:18,187 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-07-23 20:00:18,197 - INFO - text_processor - 成功处理实体: 策门林寺
2025-07-23 20:00:18,197 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:00:18,197 - INFO - __main__ - 已处理 42/100 个节点
2025-07-23 20:00:18,197 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:00:18,197 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:00:18,197 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:00:22,598 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鲁普岩寺
2025-07-23 20:00:22,607 - INFO - text_processor - 成功处理实体: 鲁普岩寺
2025-07-23 20:00:28,322 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仙足岛
2025-07-23 20:00:28,334 - INFO - text_processor - 成功处理实体: 仙足岛
2025-07-23 20:00:33,796 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-07-23 20:00:33,808 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-07-23 20:00:33,808 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:00:33,809 - INFO - __main__ - 已处理 45/100 个节点
2025-07-23 20:00:33,809 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:00:33,809 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:00:33,809 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:00:38,400 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺
2025-07-23 20:00:38,416 - INFO - text_processor - 成功处理实体: 乃琼寺
2025-07-23 20:00:45,271 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次巴拉康寺
2025-07-23 20:00:45,286 - INFO - text_processor - 成功处理实体: 次巴拉康寺
2025-07-23 20:00:53,017 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 清政府驻藏大臣衙门旧址
2025-07-23 20:00:53,028 - INFO - text_processor - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 20:00:53,028 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:00:53,028 - INFO - __main__ - 已处理 48/100 个节点
2025-07-23 20:00:53,029 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:00:53,029 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:00:53,029 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:00:53,030 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:02,073 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热振寺
2025-07-23 20:01:02,090 - INFO - text_processor - 成功处理实体: 热振寺
2025-07-23 20:01:10,573 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 关帝庙
2025-07-23 20:01:10,582 - INFO - text_processor - 成功处理实体: 关帝庙
2025-07-23 20:01:10,582 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:17,882 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-07-23 20:01:17,897 - INFO - text_processor - 成功处理实体: 喜德林
2025-07-23 20:01:17,897 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:01:17,898 - INFO - __main__ - 已处理 51/100 个节点
2025-07-23 20:01:17,898 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:01:17,898 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:01:17,898 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:01:17,898 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:26,789 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白色寺
2025-07-23 20:01:26,804 - INFO - text_processor - 成功处理实体: 白色寺
2025-07-23 20:01:26,804 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:37,018 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-07-23 20:01:37,027 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-23 20:01:37,028 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:44,093 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木寺
2025-07-23 20:01:44,103 - INFO - text_processor - 成功处理实体: 纳木寺
2025-07-23 20:01:44,103 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:01:44,103 - INFO - __main__ - 已处理 54/100 个节点
2025-07-23 20:01:44,103 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:01:44,103 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:01:44,104 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:01:44,104 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:01:55,163 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县楚杰寺
2025-07-23 20:01:55,171 - INFO - text_processor - 成功处理实体: 林周县楚杰寺
2025-07-23 20:01:55,172 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:02,379 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 中华文化园露营地
2025-07-23 20:02:02,390 - INFO - text_processor - 成功处理实体: 中华文化园露营地
2025-07-23 20:02:02,390 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:13,881 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 秀色才纳净土博览园
2025-07-23 20:02:13,891 - INFO - text_processor - 成功处理实体: 秀色才纳净土博览园
2025-07-23 20:02:13,891 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:02:13,892 - INFO - __main__ - 已处理 57/100 个节点
2025-07-23 20:02:13,892 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:02:13,892 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:02:13,892 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:02:13,892 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:25,271 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶马古道马帮落脚点遗址
2025-07-23 20:02:25,323 - INFO - text_processor - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-23 20:02:25,323 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:41,013 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 山野星空露营-吉隆营地
2025-07-23 20:02:41,026 - INFO - text_processor - 成功处理实体: 山野星空露营-吉隆营地
2025-07-23 20:02:41,026 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:46,768 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 奇秀园林
2025-07-23 20:02:46,783 - INFO - text_processor - 成功处理实体: 奇秀园林
2025-07-23 20:02:46,783 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:02:46,784 - INFO - __main__ - 已处理 60/100 个节点
2025-07-23 20:02:46,784 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:02:46,784 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:02:46,784 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:02:46,784 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:02:58,769 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛堆吉曲康桑区
2025-07-23 20:02:58,782 - INFO - text_processor - 成功处理实体: 洛堆吉曲康桑区
2025-07-23 20:02:58,783 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:03:13,754 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-07-23 20:03:13,763 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-07-23 20:03:13,763 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:03:23,355 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔玉贡康寺
2025-07-23 20:03:23,363 - INFO - text_processor - 成功处理实体: 塔玉贡康寺
2025-07-23 20:03:23,363 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:03:23,363 - INFO - __main__ - 已处理 63/100 个节点
2025-07-23 20:03:23,364 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:03:23,364 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:03:23,364 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:03:23,364 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:03:36,898 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉布寺
2025-07-23 20:03:36,912 - INFO - text_processor - 成功处理实体: 吉布寺
2025-07-23 20:03:36,913 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:03:44,113 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 美珠杂日生态乐园
2025-07-23 20:03:44,124 - INFO - text_processor - 成功处理实体: 美珠杂日生态乐园
2025-07-23 20:03:44,124 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:03:57,067 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 舒适的露营地
2025-07-23 20:03:57,081 - INFO - text_processor - 成功处理实体: 舒适的露营地
2025-07-23 20:03:57,081 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:03:57,081 - INFO - __main__ - 已处理 66/100 个节点
2025-07-23 20:03:57,081 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:03:57,081 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:03:57,082 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:03:57,082 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:01,174 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 钱学森雕像
2025-07-23 20:04:01,186 - INFO - text_processor - 成功处理实体: 钱学森雕像
2025-07-23 20:04:01,186 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:12,481 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-07-23 20:04:12,495 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-23 20:04:12,496 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:17,598 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 明珠林
2025-07-23 20:04:17,607 - INFO - text_processor - 成功处理实体: 明珠林
2025-07-23 20:04:17,607 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:04:17,607 - INFO - __main__ - 已处理 69/100 个节点
2025-07-23 20:04:17,607 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:04:17,607 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:04:17,607 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:04:17,607 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:24,737 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏林周草牧业科技小院
2025-07-23 20:04:24,750 - INFO - text_processor - 成功处理实体: 西藏林周草牧业科技小院
2025-07-23 20:04:24,750 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:39,096 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 内马厩
2025-07-23 20:04:39,106 - INFO - text_processor - 成功处理实体: 内马厩
2025-07-23 20:04:39,106 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:50,209 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尤隆布
2025-07-23 20:04:50,224 - INFO - text_processor - 成功处理实体: 尤隆布
2025-07-23 20:04:50,224 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:04:50,225 - INFO - __main__ - 已处理 72/100 个节点
2025-07-23 20:04:50,225 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:04:50,225 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:04:50,225 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:04:50,225 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:04:58,577 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙日秋摩
2025-07-23 20:04:58,598 - INFO - text_processor - 成功处理实体: 龙日秋摩
2025-07-23 20:04:58,599 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:05:06,400 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-07-23 20:05:06,417 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-07-23 20:05:06,417 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:05:12,469 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布朗
2025-07-23 20:05:12,484 - INFO - text_processor - 成功处理实体: 崩布朗
2025-07-23 20:05:12,484 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:05:12,484 - INFO - __main__ - 已处理 75/100 个节点
2025-07-23 20:05:12,485 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:05:12,485 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:05:12,485 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:05:12,485 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:05:52,286 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 加错
2025-07-23 20:05:52,296 - INFO - text_processor - 成功处理实体: 加错
2025-07-23 20:05:52,297 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:06:07,065 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达尔多拉
2025-07-23 20:06:07,078 - INFO - text_processor - 成功处理实体: 达尔多拉
2025-07-23 20:06:07,078 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:06:29,078 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县杰堆寺
2025-07-23 20:06:29,089 - INFO - text_processor - 成功处理实体: 林周县杰堆寺
2025-07-23 20:06:29,089 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:06:29,089 - INFO - __main__ - 已处理 78/100 个节点
2025-07-23 20:06:29,089 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:06:29,090 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:06:29,090 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:06:29,090 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:06:42,994 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎日阿布塘
2025-07-23 20:06:43,004 - INFO - text_processor - 成功处理实体: 扎日阿布塘
2025-07-23 20:06:43,004 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:06:49,429 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关区净土国家级旅游景区
2025-07-23 20:06:49,439 - INFO - text_processor - 成功处理实体: 城关区净土国家级旅游景区
2025-07-23 20:06:49,439 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:07:11,529 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-07-23 20:07:11,540 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-07-23 20:07:11,540 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:07:11,541 - INFO - __main__ - 已处理 81/100 个节点
2025-07-23 20:07:11,541 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:07:11,541 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:07:11,541 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:07:11,541 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:07:24,001 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城北萨斯格桑林卡
2025-07-23 20:07:24,009 - INFO - text_processor - 成功处理实体: 城北萨斯格桑林卡
2025-07-23 20:07:24,010 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:07:38,242 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏唐卡画院
2025-07-23 20:07:38,251 - INFO - text_processor - 成功处理实体: 西藏唐卡画院
2025-07-23 20:07:38,251 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:07:52,696 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨奇趣昆虫科普展
2025-07-23 20:07:52,706 - INFO - text_processor - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-23 20:07:52,707 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:07:52,707 - INFO - __main__ - 已处理 84/100 个节点
2025-07-23 20:07:52,707 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:07:52,707 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:07:52,707 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:07:52,707 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:08:05,395 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-07-23 20:08:05,406 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-07-23 20:08:05,406 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:08:10,888 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 涉溪山谷露营地
2025-07-23 20:08:10,900 - INFO - text_processor - 成功处理实体: 涉溪山谷露营地
2025-07-23 20:08:10,901 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:08:22,637 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尼木吞巴非遗中心
2025-07-23 20:08:22,646 - INFO - text_processor - 成功处理实体: 尼木吞巴非遗中心
2025-07-23 20:08:22,646 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:08:22,646 - INFO - __main__ - 已处理 87/100 个节点
2025-07-23 20:08:22,646 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:08:22,647 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:08:22,647 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:08:22,647 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:08:36,297 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 群巴
2025-07-23 20:08:36,306 - INFO - text_processor - 成功处理实体: 群巴
2025-07-23 20:08:36,306 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:08:49,642 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热卡扎日追寺
2025-07-23 20:08:49,652 - INFO - text_processor - 成功处理实体: 热卡扎日追寺
2025-07-23 20:08:49,652 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:09:02,257 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-07-23 20:09:02,266 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-07-23 20:09:02,266 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:09:02,266 - INFO - __main__ - 已处理 90/100 个节点
2025-07-23 20:09:02,267 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:09:02,267 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:09:02,267 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:09:02,267 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:09:11,284 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-07-23 20:09:11,297 - INFO - text_processor - 成功处理实体: 曲苏
2025-07-23 20:09:11,297 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:09:27,904 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-07-23 20:09:27,929 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-23 20:09:27,930 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:09:41,144 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 梦创拉萨
2025-07-23 20:09:41,155 - INFO - text_processor - 成功处理实体: 梦创拉萨
2025-07-23 20:09:41,155 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:09:41,156 - INFO - __main__ - 已处理 93/100 个节点
2025-07-23 20:09:41,156 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:09:41,156 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:09:41,156 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:09:41,156 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:10:00,855 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛日扎扎
2025-07-23 20:10:00,864 - INFO - text_processor - 成功处理实体: 玛日扎扎
2025-07-23 20:10:00,864 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:10:10,389 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪格拉山
2025-07-23 20:10:10,398 - INFO - text_processor - 成功处理实体: 雪格拉山
2025-07-23 20:10:10,398 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:10:25,585 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 密宗院
2025-07-23 20:10:25,596 - INFO - text_processor - 成功处理实体: 密宗院
2025-07-23 20:10:25,598 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:10:25,598 - INFO - __main__ - 已处理 96/100 个节点
2025-07-23 20:10:25,598 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:10:25,598 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:10:25,598 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:10:25,598 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:10:40,198 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 科比台球纪念馆
2025-07-23 20:10:40,207 - INFO - text_processor - 成功处理实体: 科比台球纪念馆
2025-07-23 20:10:40,208 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:10:58,736 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-07-23 20:10:58,747 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-07-23 20:10:58,747 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:11:09,956 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 颂吉东阁大昭寺店
2025-07-23 20:11:09,968 - INFO - text_processor - 成功处理实体: 颂吉东阁大昭寺店
2025-07-23 20:11:09,968 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:11:09,968 - INFO - __main__ - 已处理 99/100 个节点
2025-07-23 20:11:09,968 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:11:09,968 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:11:09,969 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:11:09,969 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:11:24,238 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水木林卡
2025-07-23 20:11:24,246 - INFO - text_processor - 成功处理实体: 水木林卡
2025-07-23 20:11:24,246 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:11:24,246 - INFO - __main__ - 已处理 100/100 个节点
2025-07-23 20:11:24,247 - INFO - __main__ - 批次处理完成 - 大小: 100, 耗时: 1207.37秒, 成功: 100, 失败: 0
2025-07-23 20:11:26,255 - INFO - __main__ - 
==================================================
2025-07-23 20:11:26,255 - INFO - __main__ - 开始处理批次大小: 150
2025-07-23 20:11:26,256 - INFO - __main__ - ==================================================
2025-07-23 20:11:26,256 - INFO - __main__ - 开始处理批次，大小: 150
2025-07-23 20:11:26,286 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:11:26,288 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:11:26,288 - INFO - text_processor - 清空所有节点和关系
2025-07-23 20:11:26,290 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:11:26,293 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:11:26,294 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 20:11:26,294 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:11:26,294 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:11:26,294 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:11:31,250 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 20:11:31,261 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 20:11:44,371 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 20:11:44,438 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 20:11:53,592 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 20:11:53,603 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 20:11:53,604 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:12:33,200 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一宗教体系",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:12:33,201 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "布达拉宫位于拉萨市，而纳木措位于当雄县，地理上虽都在西藏，但地理位置相距较远，不具备地理位置相近的条件",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "布达拉宫是藏传佛教寺庙，而纳木措是自然景观，类型不同",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "布达拉宫是宗教场所，纳木措是自然景观，暂时无法确定是否适合一起游览",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "布达拉宫有悠久的历史作为政治和宗教中心，而纳木措在历史上主要是农业和牧业区，两者历史背景不相关",
        "confidence": 0.5,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "布达拉宫是藏传佛教文化，纳木措也有着悠久的藏传佛教历史，都属于 same cultural system",
        "confidence": 0.5,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:12:33,202 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 20:12:33,202 - INFO - __main__ - 已处理 3/150 个节点
2025-07-23 20:12:33,203 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:12:33,203 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:12:33,203 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:12:40,836 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 20:12:40,850 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 20:13:05,557 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 20:13:05,601 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 20:13:05,602 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:13:15,985 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 20:13:16,032 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 20:13:16,032 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:14:12,140 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:14:12,319 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "八廓街作为文化象征，与大型文化史诗有深厚关联",
        "confidence": 0.6,
        "direction": "forward"
    }
]
```
2025-07-23 20:14:12,320 - INFO - __main__ - 已处理 6/150 个节点
2025-07-23 20:14:12,320 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:14:12,320 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:14:12,321 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:14:26,017 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 20:14:26,029 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 20:15:04,716 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 20:15:04,731 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 20:15:15,944 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 20:15:15,959 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 20:15:15,959 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:15:57,687 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "都在拉萨市中心",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都在拉萨市区的宗教文化区",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:15:57,687 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "可以一起游览以增强体验",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:15:57,688 - INFO - __main__ - 已处理 9/150 个节点
2025-07-23 20:15:57,688 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:15:57,688 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:15:57,688 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:16:11,603 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 20:16:11,616 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 20:16:28,297 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 20:16:28,345 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 20:16:38,412 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 20:16:38,421 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 20:16:38,423 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:17:24,700 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙，都属于宗教建筑",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "NEARBY",
        "reason": "两个景点都被列为“景点主题”为宗教建筑，位置相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，有共同的宗教背景",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:17:24,701 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，可以相互补充",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:17:24,701 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域，地理位置相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，小昭寺是宗教景点，西藏博物馆是文化遗址",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是关于西藏的文化景点，具有文化相关性",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:17:24,701 - INFO - __main__ - 已处理 12/150 个节点
2025-07-23 20:17:24,702 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:17:24,702 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:17:24,702 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:17:24,702 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:17:33,479 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 20:17:33,488 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 20:17:46,702 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 20:17:46,714 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 20:17:54,576 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 20:17:54,589 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 20:17:54,589 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:18:11,373 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙，可能共享宗教传统",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:18:11,373 - INFO - __main__ - 已处理 15/150 个节点
2025-07-23 20:18:11,374 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:18:11,374 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:18:11,374 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:18:20,745 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 20:18:20,756 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 20:18:33,511 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 20:18:33,522 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 20:18:40,417 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 20:18:40,429 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 20:18:40,430 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:19:08,729 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:19:08,730 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化体系",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都涉及重要历史人物和文化背景",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:19:08,730 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:19:08,732 - INFO - __main__ - 已处理 18/150 个节点
2025-07-23 20:19:08,732 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:19:08,732 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:19:08,733 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:19:16,866 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 20:19:16,912 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 20:19:30,239 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 20:19:30,250 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 20:19:36,230 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 20:19:36,243 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 20:19:36,243 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:19:47,912 - ERROR - text_processor - JSON 解析失败 for 拉鲁湿地国家级自然保护区 -> 羊八井: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:19:47,912 - INFO - __main__ - 已处理 21/150 个节点
2025-07-23 20:19:47,912 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:19:47,913 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:19:47,913 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:19:47,913 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:19:55,237 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 20:19:55,252 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 20:20:04,741 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 20:20:04,750 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 20:20:13,205 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 20:20:13,218 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 20:20:13,219 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:20:56,433 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 甘丹寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:20:56,433 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于墨竹工卡县，地理位置相近",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:20:56,433 - ERROR - text_processor - JSON 解析失败 for 甘丹寺 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 20:20:56,435 - INFO - __main__ - 已处理 24/150 个节点
2025-07-23 20:20:56,435 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:20:56,435 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:20:56,435 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:21:04,595 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 20:21:04,606 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 20:21:13,877 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 20:21:13,887 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 20:21:38,423 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 20:21:38,437 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 20:21:38,438 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:21:51,351 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-07-23 20:21:51,351 - INFO - text_processor - 关闭资源
2025-07-23 20:27:12,393 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-07-23 20:27:12,411 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-07-23 20:27:12,411 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-07-23 20:27:12,413 - INFO - __main__ - 开始同步批量处理测试
2025-07-23 20:27:12,413 - INFO - __main__ - 加载数据完成，总节点数: 260, 去重后: 258
2025-07-23 20:27:12,413 - INFO - __main__ - 
==================================================
2025-07-23 20:27:12,414 - INFO - __main__ - 开始处理批次大小: 10
2025-07-23 20:27:12,414 - INFO - __main__ - ==================================================
2025-07-23 20:27:12,414 - INFO - __main__ - 开始处理批次，大小: 10
2025-07-23 20:27:12,437 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:27:12,439 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:27:12,439 - INFO - text_processor - 清空所有节点和关系
2025-07-23 20:27:12,440 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:27:12,444 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:27:12,445 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 20:27:12,445 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:27:12,445 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:27:12,445 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:27:25,684 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 20:27:25,745 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 20:27:47,201 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-07-23 20:27:47,217 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-07-23 20:27:47,217 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-07-23 20:27:47,218 - INFO - __main__ - 开始同步批量处理测试
2025-07-23 20:27:47,220 - INFO - __main__ - 加载数据完成，总节点数: 260, 去重后: 258
2025-07-23 20:27:47,220 - INFO - __main__ - 
==================================================
2025-07-23 20:27:47,220 - INFO - __main__ - 开始处理批次大小: 150
2025-07-23 20:27:47,220 - INFO - __main__ - ==================================================
2025-07-23 20:27:47,220 - INFO - __main__ - 开始处理批次，大小: 150
2025-07-23 20:27:47,232 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:27:47,235 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:27:47,235 - INFO - text_processor - 清空所有节点和关系
2025-07-23 20:27:47,238 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:27:47,244 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:27:47,244 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 20:27:47,245 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:27:47,245 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:27:47,245 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:27:54,206 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 20:27:54,220 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 20:28:11,693 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 20:28:11,756 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 20:28:19,687 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 20:28:19,697 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 20:28:19,698 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:28:50,226 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "有共同的历史背景或文化联系",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:28:50,230 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 20:28:50,231 - INFO - __main__ - 已处理 3/150 个节点
2025-07-23 20:28:50,231 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:28:50,231 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:28:50,231 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:29:00,862 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 20:29:00,871 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 20:29:05,794 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 20:29:05,804 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 20:29:05,804 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:29:31,314 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 20:29:31,356 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 20:29:31,356 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:29:53,889 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，都是拉萨的著名旅游景点",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化体系中的宗教建筑和文化场所",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:29:53,890 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙或与宗教文化相关",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:29:53,890 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
  {
    "type": "NEARBY",
    "reason": "两个景点都位于拉萨市中心区域",
    "confidence": 0.8,
    "direction": "bidirectional"
  }
]
```
2025-07-23 20:29:53,891 - INFO - __main__ - 已处理 6/150 个节点
2025-07-23 20:29:53,891 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:29:53,891 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:29:53,891 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:29:58,727 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 20:29:58,792 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 20:30:03,706 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 20:30:03,715 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 20:30:10,835 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 20:30:10,841 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 20:30:10,843 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:30:58,299 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:30:58,299 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于同一城市，位于布达拉宫附近，都涉及文化和历史元素",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:30:58,311 - INFO - __main__ - 已处理 9/150 个节点
2025-07-23 20:30:58,311 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:30:58,311 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:30:58,311 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:31:02,695 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 20:31:02,706 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 20:31:06,466 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 20:31:06,476 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 20:31:25,670 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 20:31:25,703 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 20:31:25,703 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:31:55,949 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:31:55,949 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化，涉及 Same cultural heritage.",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:31:55,949 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:31:55,949 - INFO - __main__ - 已处理 12/150 个节点
2025-07-23 20:31:55,949 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:31:55,949 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:31:55,950 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:31:55,950 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:32:04,903 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 20:32:04,914 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 20:32:14,736 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 20:32:14,743 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 20:32:23,998 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 20:32:24,007 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 20:32:24,008 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:32:31,875 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙，都与宗教信仰有关",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "扎基寺的香火旺盛，/z仓姑寺/的信众可能也会前往/z扎基寺/", 
        "confidence": 0.8,
        "direction": "forward"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，有共同的历史背景",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:32:31,875 - INFO - __main__ - 已处理 15/150 个节点
2025-07-23 20:32:31,875 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:32:31,875 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:32:31,877 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:32:51,091 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 20:32:51,100 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 20:33:02,174 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 20:33:02,184 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 20:33:06,239 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 20:33:06,248 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 20:33:06,248 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:33:24,714 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:33:24,714 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是宗教建筑",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教", 
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:33:24,715 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "帕邦喀位于拉萨河旁边，可能与之有历史联系",
        "confidence": 0.6,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:33:24,716 - INFO - __main__ - 已处理 18/150 个节点
2025-07-23 20:33:24,716 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:33:24,716 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:33:24,716 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:33:30,565 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 20:33:30,609 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 20:33:39,754 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 20:33:39,762 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 20:33:48,566 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 20:33:48,577 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 20:33:48,577 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:33:55,376 - ERROR - text_processor - JSON 解析失败 for 拉鲁湿地国家级自然保护区 -> 羊八井: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:33:55,376 - INFO - __main__ - 已处理 21/150 个节点
2025-07-23 20:33:55,376 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:33:55,376 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:33:55,376 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:33:55,377 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:34:11,136 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 20:34:11,143 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 20:34:16,375 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 20:34:16,384 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 20:34:25,973 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 20:34:25,984 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 20:34:25,984 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:34:50,514 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 甘丹寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，都属于同一宗教体系",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:34:50,515 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于墨竹工卡县，地理位置相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:34:50,541 - INFO - __main__ - 已处理 24/150 个节点
2025-07-23 20:34:50,542 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:34:50,542 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:34:50,542 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:34:54,136 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 20:34:54,147 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 20:34:58,305 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 20:34:58,314 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 20:35:04,515 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 20:35:04,526 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 20:35:04,526 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:35:25,091 - ERROR - text_processor - JSON 解析失败 for 布达拉宫广场 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 20:35:25,091 - INFO - __main__ - 已处理 27/150 个节点
2025-07-23 20:35:25,092 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:35:25,092 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:35:25,092 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:35:29,043 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如寺印经院
2025-07-23 20:35:29,052 - INFO - text_processor - 成功处理实体: 木如寺印经院
2025-07-23 20:35:33,038 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 下密寺
2025-07-23 20:35:33,047 - INFO - text_processor - 成功处理实体: 下密寺
2025-07-23 20:35:37,439 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驻藏大臣衙门遗址
2025-07-23 20:35:37,450 - INFO - text_processor - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 20:35:37,450 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:36:01,960 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 下密寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "一个作为寺庙，曾作为剧场使用，另一个从事密宗修行",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教体系",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:36:01,960 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "一个在寺庙内，一个在驻藏大臣雕像附近，可能适合一起游览",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:36:01,961 - ERROR - text_processor - JSON 解析失败 for 下密寺 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙或历史遗迹",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:36:01,961 - INFO - __main__ - 已处理 30/150 个节点
2025-07-23 20:36:01,961 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:36:01,961 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:36:01,961 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:36:05,175 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺路
2025-07-23 20:36:05,198 - INFO - text_processor - 成功处理实体: 小昭寺路
2025-07-23 20:36:05,198 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:36:19,077 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨民族文化艺术宫
2025-07-23 20:36:19,084 - INFO - text_processor - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 20:36:19,085 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:36:25,719 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-07-23 20:36:25,728 - INFO - text_processor - 成功处理实体: 5238
2025-07-23 20:36:25,729 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:36:25,729 - INFO - __main__ - 已处理 33/150 个节点
2025-07-23 20:36:25,729 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:36:25,729 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:36:25,729 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:36:25,729 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:36:32,207 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措扎西岛
2025-07-23 20:36:32,216 - INFO - text_processor - 成功处理实体: 纳木措扎西岛
2025-07-23 20:36:32,217 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:36:40,572 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药文化博览中心
2025-07-23 20:36:40,580 - INFO - text_processor - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 20:36:49,134 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-07-23 20:36:49,183 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-07-23 20:36:49,184 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:36:49,184 - INFO - __main__ - 已处理 36/150 个节点
2025-07-23 20:36:49,184 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:36:49,184 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:36:49,185 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:36:49,185 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:36:58,048 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甲玛王宫
2025-07-23 20:36:58,057 - INFO - text_processor - 成功处理实体: 甲玛王宫
2025-07-23 20:37:05,092 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎希寺
2025-07-23 20:37:05,099 - INFO - text_processor - 成功处理实体: 扎希寺
2025-07-23 20:37:12,469 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-07-23 20:37:12,478 - INFO - text_processor - 成功处理实体: 楚布寺
2025-07-23 20:37:12,478 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:37:12,478 - INFO - __main__ - 已处理 39/150 个节点
2025-07-23 20:37:12,478 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:37:12,478 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:37:12,479 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:37:19,698 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞干布出生地
2025-07-23 20:37:19,706 - INFO - text_processor - 成功处理实体: 松赞干布出生地
2025-07-23 20:37:26,272 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 聂当度母殿和聂当大佛
2025-07-23 20:37:26,280 - INFO - text_processor - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 20:37:42,578 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-07-23 20:37:42,590 - INFO - text_processor - 成功处理实体: 策门林寺
2025-07-23 20:37:42,590 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:37:42,590 - INFO - __main__ - 已处理 42/150 个节点
2025-07-23 20:37:42,592 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:37:42,592 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:37:42,592 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:37:48,858 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鲁普岩寺
2025-07-23 20:37:48,866 - INFO - text_processor - 成功处理实体: 鲁普岩寺
2025-07-23 20:38:13,717 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仙足岛
2025-07-23 20:38:13,729 - INFO - text_processor - 成功处理实体: 仙足岛
2025-07-23 20:38:24,402 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-07-23 20:38:24,413 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-07-23 20:38:24,413 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:38:24,413 - INFO - __main__ - 已处理 45/150 个节点
2025-07-23 20:38:24,413 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:38:24,413 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:38:24,414 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:38:26,593 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺
2025-07-23 20:38:26,603 - INFO - text_processor - 成功处理实体: 乃琼寺
2025-07-23 20:38:30,785 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次巴拉康寺
2025-07-23 20:38:30,792 - INFO - text_processor - 成功处理实体: 次巴拉康寺
2025-07-23 20:39:08,148 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 清政府驻藏大臣衙门旧址
2025-07-23 20:39:08,156 - INFO - text_processor - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 20:39:08,157 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 20:39:08,157 - INFO - __main__ - 已处理 48/150 个节点
2025-07-23 20:39:08,157 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:39:08,157 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:39:08,157 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:39:08,157 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:14,520 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热振寺
2025-07-23 20:39:14,530 - INFO - text_processor - 成功处理实体: 热振寺
2025-07-23 20:39:18,169 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 关帝庙
2025-07-23 20:39:18,178 - INFO - text_processor - 成功处理实体: 关帝庙
2025-07-23 20:39:18,178 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:23,951 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-07-23 20:39:23,964 - INFO - text_processor - 成功处理实体: 喜德林
2025-07-23 20:39:23,964 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:39:23,965 - INFO - __main__ - 已处理 51/150 个节点
2025-07-23 20:39:23,965 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:39:23,965 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:39:23,966 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:39:23,966 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:30,626 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白色寺
2025-07-23 20:39:30,636 - INFO - text_processor - 成功处理实体: 白色寺
2025-07-23 20:39:30,636 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:35,632 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-07-23 20:39:35,641 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-23 20:39:35,641 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:42,193 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木寺
2025-07-23 20:39:42,203 - INFO - text_processor - 成功处理实体: 纳木寺
2025-07-23 20:39:42,203 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:39:42,203 - INFO - __main__ - 已处理 54/150 个节点
2025-07-23 20:39:42,204 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:39:42,204 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:39:42,204 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:39:42,204 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:39:52,044 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县楚杰寺
2025-07-23 20:39:52,051 - INFO - text_processor - 成功处理实体: 林周县楚杰寺
2025-07-23 20:39:52,051 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:40:04,533 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 中华文化园露营地
2025-07-23 20:40:04,542 - INFO - text_processor - 成功处理实体: 中华文化园露营地
2025-07-23 20:40:04,542 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:40:09,790 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 秀色才纳净土博览园
2025-07-23 20:40:09,799 - INFO - text_processor - 成功处理实体: 秀色才纳净土博览园
2025-07-23 20:40:09,800 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:40:09,800 - INFO - __main__ - 已处理 57/150 个节点
2025-07-23 20:40:09,800 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:40:09,800 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:40:09,800 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:40:09,800 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:40:23,659 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶马古道马帮落脚点遗址
2025-07-23 20:40:23,702 - INFO - text_processor - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-23 20:40:23,703 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:40:32,813 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 山野星空露营-吉隆营地
2025-07-23 20:40:32,828 - INFO - text_processor - 成功处理实体: 山野星空露营-吉隆营地
2025-07-23 20:40:32,828 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:40:51,453 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 奇秀园林
2025-07-23 20:40:51,464 - INFO - text_processor - 成功处理实体: 奇秀园林
2025-07-23 20:40:51,464 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:40:51,464 - INFO - __main__ - 已处理 60/150 个节点
2025-07-23 20:40:51,464 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:40:51,465 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:40:51,465 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:40:51,465 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:09,909 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛堆吉曲康桑区
2025-07-23 20:41:09,917 - INFO - text_processor - 成功处理实体: 洛堆吉曲康桑区
2025-07-23 20:41:09,918 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:14,612 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-07-23 20:41:14,619 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-07-23 20:41:14,619 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:25,506 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔玉贡康寺
2025-07-23 20:41:25,513 - INFO - text_processor - 成功处理实体: 塔玉贡康寺
2025-07-23 20:41:25,513 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:41:25,514 - INFO - __main__ - 已处理 63/150 个节点
2025-07-23 20:41:25,514 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:41:25,514 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:41:25,514 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:41:25,514 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:32,605 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉布寺
2025-07-23 20:41:32,611 - INFO - text_processor - 成功处理实体: 吉布寺
2025-07-23 20:41:32,611 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:38,368 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 美珠杂日生态乐园
2025-07-23 20:41:38,376 - INFO - text_processor - 成功处理实体: 美珠杂日生态乐园
2025-07-23 20:41:38,376 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:47,330 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 舒适的露营地
2025-07-23 20:41:47,336 - INFO - text_processor - 成功处理实体: 舒适的露营地
2025-07-23 20:41:47,337 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:41:47,337 - INFO - __main__ - 已处理 66/150 个节点
2025-07-23 20:41:47,337 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:41:47,337 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:41:47,337 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:41:47,338 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:41:53,216 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 钱学森雕像
2025-07-23 20:41:53,224 - INFO - text_processor - 成功处理实体: 钱学森雕像
2025-07-23 20:41:53,224 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:42:02,581 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-07-23 20:42:02,587 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-23 20:42:02,587 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:42:17,174 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 明珠林
2025-07-23 20:42:17,181 - INFO - text_processor - 成功处理实体: 明珠林
2025-07-23 20:42:17,181 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:42:17,181 - INFO - __main__ - 已处理 69/150 个节点
2025-07-23 20:42:17,181 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:42:17,181 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:42:17,181 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:42:17,181 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:42:25,626 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏林周草牧业科技小院
2025-07-23 20:42:25,632 - INFO - text_processor - 成功处理实体: 西藏林周草牧业科技小院
2025-07-23 20:42:25,632 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:42:39,849 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 内马厩
2025-07-23 20:42:39,856 - INFO - text_processor - 成功处理实体: 内马厩
2025-07-23 20:42:39,857 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:42:56,259 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尤隆布
2025-07-23 20:42:56,270 - INFO - text_processor - 成功处理实体: 尤隆布
2025-07-23 20:42:56,270 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:42:56,270 - INFO - __main__ - 已处理 72/150 个节点
2025-07-23 20:42:56,270 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:42:56,271 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:42:56,271 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:42:56,271 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:03,445 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙日秋摩
2025-07-23 20:43:03,450 - INFO - text_processor - 成功处理实体: 龙日秋摩
2025-07-23 20:43:03,451 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:14,805 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-07-23 20:43:14,812 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-07-23 20:43:14,812 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:18,861 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布朗
2025-07-23 20:43:18,867 - INFO - text_processor - 成功处理实体: 崩布朗
2025-07-23 20:43:18,867 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:43:18,867 - INFO - __main__ - 已处理 75/150 个节点
2025-07-23 20:43:18,867 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:43:18,867 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:43:18,867 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:43:18,867 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:27,960 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 加错
2025-07-23 20:43:27,969 - INFO - text_processor - 成功处理实体: 加错
2025-07-23 20:43:27,969 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:33,517 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达尔多拉
2025-07-23 20:43:33,524 - INFO - text_processor - 成功处理实体: 达尔多拉
2025-07-23 20:43:33,524 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:45,661 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县杰堆寺
2025-07-23 20:43:45,667 - INFO - text_processor - 成功处理实体: 林周县杰堆寺
2025-07-23 20:43:45,667 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:43:45,667 - INFO - __main__ - 已处理 78/150 个节点
2025-07-23 20:43:45,667 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:43:45,667 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:43:45,667 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:43:45,667 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:43:52,206 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎日阿布塘
2025-07-23 20:43:52,212 - INFO - text_processor - 成功处理实体: 扎日阿布塘
2025-07-23 20:43:52,213 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:03,824 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关区净土国家级旅游景区
2025-07-23 20:44:03,830 - INFO - text_processor - 成功处理实体: 城关区净土国家级旅游景区
2025-07-23 20:44:03,830 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:08,661 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-07-23 20:44:08,669 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-07-23 20:44:08,669 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:44:08,669 - INFO - __main__ - 已处理 81/150 个节点
2025-07-23 20:44:08,669 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:44:08,669 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:44:08,669 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:44:08,669 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:14,950 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城北萨斯格桑林卡
2025-07-23 20:44:14,958 - INFO - text_processor - 成功处理实体: 城北萨斯格桑林卡
2025-07-23 20:44:14,959 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:34,083 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏唐卡画院
2025-07-23 20:44:34,089 - INFO - text_processor - 成功处理实体: 西藏唐卡画院
2025-07-23 20:44:34,089 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:43,237 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨奇趣昆虫科普展
2025-07-23 20:44:43,243 - INFO - text_processor - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-23 20:44:43,243 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:44:43,243 - INFO - __main__ - 已处理 84/150 个节点
2025-07-23 20:44:43,243 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:44:43,243 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:44:43,243 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:44:43,243 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:44:53,068 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-07-23 20:44:53,073 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-07-23 20:44:53,073 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:04,714 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 涉溪山谷露营地
2025-07-23 20:45:04,721 - INFO - text_processor - 成功处理实体: 涉溪山谷露营地
2025-07-23 20:45:04,722 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:13,261 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尼木吞巴非遗中心
2025-07-23 20:45:13,268 - INFO - text_processor - 成功处理实体: 尼木吞巴非遗中心
2025-07-23 20:45:13,268 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:45:13,268 - INFO - __main__ - 已处理 87/150 个节点
2025-07-23 20:45:13,268 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:45:13,268 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:45:13,268 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:45:13,268 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:19,735 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 群巴
2025-07-23 20:45:19,742 - INFO - text_processor - 成功处理实体: 群巴
2025-07-23 20:45:19,742 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:24,731 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热卡扎日追寺
2025-07-23 20:45:24,740 - INFO - text_processor - 成功处理实体: 热卡扎日追寺
2025-07-23 20:45:24,740 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:29,831 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-07-23 20:45:29,837 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-07-23 20:45:29,837 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:45:29,837 - INFO - __main__ - 已处理 90/150 个节点
2025-07-23 20:45:29,837 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:45:29,839 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:45:29,839 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:45:29,839 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:36,125 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-07-23 20:45:36,134 - INFO - text_processor - 成功处理实体: 曲苏
2025-07-23 20:45:36,134 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:42,783 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-07-23 20:45:42,801 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-23 20:45:42,801 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:45:50,772 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 梦创拉萨
2025-07-23 20:45:50,778 - INFO - text_processor - 成功处理实体: 梦创拉萨
2025-07-23 20:45:50,779 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:45:50,779 - INFO - __main__ - 已处理 93/150 个节点
2025-07-23 20:45:50,779 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:45:50,779 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:45:50,779 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:45:50,779 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:01,639 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛日扎扎
2025-07-23 20:46:01,645 - INFO - text_processor - 成功处理实体: 玛日扎扎
2025-07-23 20:46:01,647 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:10,260 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪格拉山
2025-07-23 20:46:10,267 - INFO - text_processor - 成功处理实体: 雪格拉山
2025-07-23 20:46:10,267 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:18,558 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 密宗院
2025-07-23 20:46:18,565 - INFO - text_processor - 成功处理实体: 密宗院
2025-07-23 20:46:18,566 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:46:18,566 - INFO - __main__ - 已处理 96/150 个节点
2025-07-23 20:46:18,566 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:46:18,566 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:46:18,566 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:46:18,566 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:32,091 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 科比台球纪念馆
2025-07-23 20:46:32,105 - INFO - text_processor - 成功处理实体: 科比台球纪念馆
2025-07-23 20:46:32,106 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:46,003 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-07-23 20:46:46,013 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-07-23 20:46:46,013 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:54,399 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 颂吉东阁大昭寺店
2025-07-23 20:46:54,405 - INFO - text_processor - 成功处理实体: 颂吉东阁大昭寺店
2025-07-23 20:46:54,405 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:46:54,406 - INFO - __main__ - 已处理 99/150 个节点
2025-07-23 20:46:54,406 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:46:54,406 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:46:54,406 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:46:54,406 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:46:59,750 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水木林卡
2025-07-23 20:46:59,778 - INFO - text_processor - 成功处理实体: 水木林卡
2025-07-23 20:46:59,778 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:06,389 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 柳梧桑女林卡
2025-07-23 20:47:06,394 - INFO - text_processor - 成功处理实体: 柳梧桑女林卡
2025-07-23 20:47:06,394 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:14,179 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思源山
2025-07-23 20:47:14,187 - INFO - text_processor - 成功处理实体: 思源山
2025-07-23 20:47:14,187 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:47:14,187 - INFO - __main__ - 已处理 102/150 个节点
2025-07-23 20:47:14,187 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:47:14,188 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:47:14,188 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:47:14,188 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:21,527 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德曲郭卓吉林卡
2025-07-23 20:47:21,537 - INFO - text_processor - 成功处理实体: 喜德曲郭卓吉林卡
2025-07-23 20:47:21,537 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:27,019 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 24小时自助图书馆(西藏图书馆店)
2025-07-23 20:47:27,027 - INFO - text_processor - 成功处理实体: 24小时自助图书馆(西藏图书馆店)
2025-07-23 20:47:27,027 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:34,412 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 朗唐寺
2025-07-23 20:47:34,419 - INFO - text_processor - 成功处理实体: 朗唐寺
2025-07-23 20:47:34,420 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:47:34,420 - INFO - __main__ - 已处理 105/150 个节点
2025-07-23 20:47:34,420 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:47:34,420 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:47:34,420 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:47:34,420 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:39,471 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 智昭产业园净土生态体验馆
2025-07-23 20:47:39,480 - INFO - text_processor - 成功处理实体: 智昭产业园净土生态体验馆
2025-07-23 20:47:39,480 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:47:44,589 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏擦擦文化展览馆
2025-07-23 20:47:44,596 - INFO - text_processor - 成功处理实体: 西藏擦擦文化展览馆
2025-07-23 20:47:44,596 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:00,716 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-供灯室
2025-07-23 20:48:00,724 - INFO - text_processor - 成功处理实体: 大昭寺-供灯室
2025-07-23 20:48:00,724 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:48:00,724 - INFO - __main__ - 已处理 108/150 个节点
2025-07-23 20:48:00,724 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:48:00,724 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:48:00,724 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:48:00,724 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:10,486 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 20:48:10,494 - INFO - text_processor - 成功处理实体: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 20:48:10,494 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:16,714 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达巧拉康
2025-07-23 20:48:16,722 - INFO - text_processor - 成功处理实体: 达巧拉康
2025-07-23 20:48:16,723 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:22,676 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨湾(网红打卡点)
2025-07-23 20:48:22,682 - INFO - text_processor - 成功处理实体: 拉萨湾(网红打卡点)
2025-07-23 20:48:22,683 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:48:22,683 - INFO - __main__ - 已处理 111/150 个节点
2025-07-23 20:48:22,683 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:48:22,683 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:48:22,683 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:48:22,683 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:28,350 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞拉康
2025-07-23 20:48:28,357 - INFO - text_processor - 成功处理实体: 松赞拉康
2025-07-23 20:48:28,357 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:41,116 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 20:48:41,123 - INFO - text_processor - 成功处理实体: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 20:48:41,123 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:52,254 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德勒林卡
2025-07-23 20:48:52,260 - INFO - text_processor - 成功处理实体: 德勒林卡
2025-07-23 20:48:52,260 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:48:52,261 - INFO - __main__ - 已处理 114/150 个节点
2025-07-23 20:48:52,261 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:48:52,261 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:48:52,261 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:48:52,261 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:48:58,503 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羌仓
2025-07-23 20:48:58,513 - INFO - text_processor - 成功处理实体: 羌仓
2025-07-23 20:48:58,513 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:08,649 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 依拉
2025-07-23 20:49:08,656 - INFO - text_processor - 成功处理实体: 依拉
2025-07-23 20:49:08,656 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:13,574 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玉隆
2025-07-23 20:49:13,579 - INFO - text_processor - 成功处理实体: 玉隆
2025-07-23 20:49:13,580 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:49:13,580 - INFO - __main__ - 已处理 117/150 个节点
2025-07-23 20:49:13,580 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:49:13,580 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:49:13,580 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:49:13,580 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:21,908 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 20:49:21,916 - INFO - text_processor - 成功处理实体: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 20:49:21,916 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:30,217 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林宗萨
2025-07-23 20:49:30,223 - INFO - text_processor - 成功处理实体: 德吉林宗萨
2025-07-23 20:49:30,223 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:42,202 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 强嘎日追寺
2025-07-23 20:49:42,208 - INFO - text_processor - 成功处理实体: 强嘎日追寺
2025-07-23 20:49:42,209 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:49:42,209 - INFO - __main__ - 已处理 120/150 个节点
2025-07-23 20:49:42,209 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:49:42,209 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:49:42,209 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:49:42,209 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:48,551 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺-厨房
2025-07-23 20:49:48,557 - INFO - text_processor - 成功处理实体: 乃琼寺-厨房
2025-07-23 20:49:48,558 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:49:59,582 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷拉
2025-07-23 20:49:59,590 - INFO - text_processor - 成功处理实体: 列卜廷拉
2025-07-23 20:49:59,590 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:05,455 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-07-23 20:50:05,462 - INFO - text_processor - 成功处理实体: 阿热康参
2025-07-23 20:50:05,462 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:50:05,462 - INFO - __main__ - 已处理 123/150 个节点
2025-07-23 20:50:05,462 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:50:05,463 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:50:05,463 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:50:05,463 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:16,240 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺-卓玛拉康
2025-07-23 20:50:16,248 - INFO - text_processor - 成功处理实体: 哲蚌寺-卓玛拉康
2025-07-23 20:50:16,248 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:23,895 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园纪念碑
2025-07-23 20:50:23,903 - INFO - text_processor - 成功处理实体: 宗角禄康公园纪念碑
2025-07-23 20:50:23,904 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:29,555 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺辩经场
2025-07-23 20:50:29,562 - INFO - text_processor - 成功处理实体: 小昭寺辩经场
2025-07-23 20:50:29,562 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:50:29,563 - INFO - __main__ - 已处理 126/150 个节点
2025-07-23 20:50:29,563 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:50:29,563 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:50:29,563 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:50:29,563 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:35,820 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺扎迪(林卡)
2025-07-23 20:50:35,826 - INFO - text_processor - 成功处理实体: 色拉寺扎迪(林卡)
2025-07-23 20:50:35,826 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:42,149 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错国家风景区
2025-07-23 20:50:42,154 - INFO - text_processor - 成功处理实体: 纳木错国家风景区
2025-07-23 20:50:42,154 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:53,010 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 国家级拉萨经开区
2025-07-23 20:50:53,018 - INFO - text_processor - 成功处理实体: 国家级拉萨经开区
2025-07-23 20:50:53,018 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:50:53,018 - INFO - __main__ - 已处理 129/150 个节点
2025-07-23 20:50:53,018 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:50:53,018 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:50:53,019 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:50:53,019 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:50:58,158 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰热康参
2025-07-23 20:50:58,163 - INFO - text_processor - 成功处理实体: 恰热康参
2025-07-23 20:50:58,163 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:06,928 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺杰扎仓
2025-07-23 20:51:06,934 - INFO - text_processor - 成功处理实体: 色拉寺杰扎仓
2025-07-23 20:51:06,934 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:17,638 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 丁真隆巴
2025-07-23 20:51:17,646 - INFO - text_processor - 成功处理实体: 丁真隆巴
2025-07-23 20:51:17,647 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:51:17,647 - INFO - __main__ - 已处理 132/150 个节点
2025-07-23 20:51:17,647 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:51:17,647 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:51:17,647 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:51:17,647 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:28,908 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那若果
2025-07-23 20:51:28,914 - INFO - text_processor - 成功处理实体: 那若果
2025-07-23 20:51:28,915 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:37,349 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 所布多纳
2025-07-23 20:51:37,356 - INFO - text_processor - 成功处理实体: 所布多纳
2025-07-23 20:51:37,356 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:45,818 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉大乘洲
2025-07-23 20:51:45,829 - INFO - text_processor - 成功处理实体: 色拉大乘洲
2025-07-23 20:51:45,829 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:51:45,829 - INFO - __main__ - 已处理 135/150 个节点
2025-07-23 20:51:45,830 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:51:45,830 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:51:45,830 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:51:45,830 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:51:54,194 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错
2025-07-23 20:51:54,200 - INFO - text_processor - 成功处理实体: 纳木错
2025-07-23 20:51:54,201 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:02,348 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 悠远的天空自驾车营地
2025-07-23 20:52:02,355 - INFO - text_processor - 成功处理实体: 悠远的天空自驾车营地
2025-07-23 20:52:02,355 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:15,764 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪江憩居鲁突
2025-07-23 20:52:15,773 - INFO - text_processor - 成功处理实体: 雪江憩居鲁突
2025-07-23 20:52:15,773 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:52:15,773 - INFO - __main__ - 已处理 138/150 个节点
2025-07-23 20:52:15,773 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:52:15,773 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:52:15,773 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:52:15,774 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:26,674 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑珠颇章
2025-07-23 20:52:26,682 - INFO - text_processor - 成功处理实体: 桑珠颇章
2025-07-23 20:52:26,682 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:36,955 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 千年古树
2025-07-23 20:52:36,962 - INFO - text_processor - 成功处理实体: 千年古树
2025-07-23 20:52:36,964 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:44,962 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨之眼
2025-07-23 20:52:44,969 - INFO - text_processor - 成功处理实体: 拉萨之眼
2025-07-23 20:52:44,969 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:52:44,969 - INFO - __main__ - 已处理 141/150 个节点
2025-07-23 20:52:44,969 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:52:44,969 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:52:44,969 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:52:44,969 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:51,932 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨市综合展馆
2025-07-23 20:52:51,940 - INFO - text_processor - 成功处理实体: 拉萨市综合展馆
2025-07-23 20:52:51,941 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:52:58,237 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 顶杰寺
2025-07-23 20:52:58,245 - INFO - text_processor - 成功处理实体: 顶杰寺
2025-07-23 20:52:58,246 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:11,855 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗格岭
2025-07-23 20:53:11,864 - INFO - text_processor - 成功处理实体: 罗格岭
2025-07-23 20:53:11,865 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:53:11,865 - INFO - __main__ - 已处理 144/150 个节点
2025-07-23 20:53:11,865 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:53:11,866 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:53:11,866 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:53:11,867 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:18,940 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏医药文化馆
2025-07-23 20:53:18,948 - INFO - text_processor - 成功处理实体: 藏医药文化馆
2025-07-23 20:53:18,948 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:27,381 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日月湖
2025-07-23 20:53:27,387 - INFO - text_processor - 成功处理实体: 日月湖
2025-07-23 20:53:27,388 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:38,291 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-07-23 20:53:38,300 - INFO - text_processor - 成功处理实体: 水上乐园
2025-07-23 20:53:38,300 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:53:38,301 - INFO - __main__ - 已处理 147/150 个节点
2025-07-23 20:53:38,301 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:53:38,301 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:53:38,302 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:53:38,302 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:43,750 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白纳村
2025-07-23 20:53:43,761 - INFO - text_processor - 成功处理实体: 白纳村
2025-07-23 20:53:43,761 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:48,411 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拼尤明丘
2025-07-23 20:53:48,420 - INFO - text_processor - 成功处理实体: 拼尤明丘
2025-07-23 20:53:48,421 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:53:56,117 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驳叶
2025-07-23 20:53:56,125 - INFO - text_processor - 成功处理实体: 驳叶
2025-07-23 20:53:56,126 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 20:53:56,126 - INFO - __main__ - 已处理 150/150 个节点
2025-07-23 20:53:56,126 - INFO - __main__ - 批次处理完成 - 大小: 150, 耗时: 1568.88秒, 成功: 150, 失败: 0
2025-07-23 20:53:58,129 - INFO - __main__ - 
==================================================
2025-07-23 20:53:58,129 - INFO - __main__ - 开始处理批次大小: 200
2025-07-23 20:53:58,129 - INFO - __main__ - ==================================================
2025-07-23 20:53:58,129 - INFO - __main__ - 开始处理批次，大小: 200
2025-07-23 20:53:58,160 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:53:58,162 - INFO - neo4j_connection - 数据库已清空
2025-07-23 20:53:58,162 - INFO - text_processor - 清空所有节点和关系
2025-07-23 20:53:58,167 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 20:53:58,170 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 20:53:58,171 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 20:53:58,171 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:53:58,172 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:53:58,172 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:54:01,941 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 20:54:01,999 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 20:54:09,903 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 20:54:09,909 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 20:54:13,051 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 20:54:13,101 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 20:54:13,101 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:54:55,275 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "布达拉宫和大昭寺都位于拉萨市中心区域，是拉萨著名的宗教和文化地标。",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "两者都是宗教寺庙，都具有重要的宗教意义。",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，布达拉宫和大昭寺都是拉萨的核心景点，通常游客会一起参观。",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "两者都有长期的历史和文化意义，且与藏传佛教密切相关。",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，属于同一宗教体系和文化背景。",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:54:55,276 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
2025-07-23 20:54:55,277 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于同一个区域，分别位于拉萨市和当雄县，地理位置上相近。",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:54:55,277 - INFO - __main__ - 已处理 3/200 个节点
2025-07-23 20:54:55,277 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:54:55,277 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:54:55,278 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:55:00,310 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 20:55:00,324 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 20:55:05,347 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 20:55:05,354 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 20:55:05,354 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:55:19,402 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 20:55:19,432 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 20:55:19,433 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:55:42,567 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:55:42,567 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙和文化活动，都属于同一宗教体系",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都涉及藏传佛教的历史文化背景",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:55:42,568 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "八廓街是藏传佛教文化景点，而《文成公主》大型史诗剧可能吸引同样喜欢历史文化的游客",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:55:42,568 - INFO - __main__ - 已处理 6/200 个节点
2025-07-23 20:55:42,568 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:55:42,569 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:55:42,569 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:55:45,925 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 20:55:45,934 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 20:55:50,511 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 20:55:50,517 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 20:55:56,607 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 20:55:56,645 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 20:55:56,646 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:56:15,245 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:15,245 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:15,246 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教文化景点",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:15,246 - INFO - __main__ - 已处理 9/200 个节点
2025-07-23 20:56:15,247 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:56:15,247 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:56:15,247 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:56:28,011 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 20:56:28,018 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 20:56:32,562 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 20:56:32,571 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 20:56:37,551 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 20:56:37,558 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 20:56:37,558 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:56:57,520 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，可以体验不同风格的寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:57,520 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "它们都与藏传佛教文化相关",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:57,520 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:56:57,520 - INFO - __main__ - 已处理 12/200 个节点
2025-07-23 20:56:57,520 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:56:57,521 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:56:57,521 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:56:57,521 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:57:01,698 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 20:57:01,734 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 20:57:05,986 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 20:57:05,994 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 20:57:10,290 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 20:57:10,297 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 20:57:10,298 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:57:19,674 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:57:19,674 - INFO - __main__ - 已处理 15/200 个节点
2025-07-23 20:57:19,674 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:57:19,674 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:57:19,675 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:57:26,031 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 20:57:26,039 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 20:57:31,607 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 20:57:31,615 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 20:57:35,802 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 20:57:35,810 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 20:57:35,811 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:58:00,755 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
  {
    "type": "NEARBY",
    "reason": "两个景点都位于拉萨市中心区域",
    "confidence": 0.8,
    "direction": "bidirectional"
  }
]
```
2025-07-23 20:58:00,755 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于西藏的宗教文化遗址，历史上有共同的背景",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:58:00,756 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
  {
    "type": "NEARBY",
    "reason": "两个景点都位于拉萨市中心区域",
    "confidence": 0.8,
    "direction": "bidirectional"
  },
  {
    "type": "CULTURAL_RELATED",
    "reason": "都与藏传佛教历史和文化有关",
    "confidence": 0.9,
    "direction": "bidirectional"
  },
  {
    "type": "COMPLEMENTARY_VISIT",
    "reason": "适合一起游览的景点",
    "confidence": 0.7,
    "direction": "bidirectional"
  }
]
```
2025-07-23 20:58:00,756 - INFO - __main__ - 已处理 18/200 个节点
2025-07-23 20:58:00,756 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:58:00,756 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:58:00,757 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:58:06,563 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 20:58:06,593 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 20:58:10,127 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 20:58:10,132 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 20:58:15,354 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 20:58:15,363 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 20:58:15,363 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 20:58:21,361 - INFO - __main__ - 已处理 21/200 个节点
2025-07-23 20:58:21,362 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:58:21,362 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:58:21,362 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:58:21,364 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 20:58:26,810 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 20:58:26,819 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 20:58:32,555 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 20:58:32,565 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 20:58:38,889 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 20:58:38,900 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 20:58:38,901 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:58:57,343 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 甘丹寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "两个寺庙都属于藏传佛教，属于同一文化体系",
        "confidence": 0.8,
        "direction": "bidirectional"
    }, 
    {
        "type": "SIMILAR_TYPE",
        "reason": "两个景点都属于寺庙类型",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:58:57,344 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是拉萨的著名景点",
        "confidence": 0.6,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "有共同的历史背景",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:58:57,349 - INFO - __main__ - 已处理 24/200 个节点
2025-07-23 20:58:57,349 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:58:57,349 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:58:57,349 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 20:59:08,163 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 20:59:08,169 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 20:59:10,208 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 20:59:10,215 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 20:59:19,928 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 20:59:19,936 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 20:59:19,936 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 20:59:55,368 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 布达拉宫广场: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都是拉萨的重要历史地标，具有共同的历史背景",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化体系，具有宗教意义",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:59:55,368 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

根据上述思考过程，以下是两个西藏景点之间的关系分析结果：

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "和平解放纪念碑是重要的藏传佛教圣地，而那根拉山口与宗教朝圣和纳木错 relates有关，两者都属于藏传佛教文化景观。",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 20:59:55,368 - ERROR - text_processor - JSON 解析失败 for 布达拉宫广场 -> 那根拉山口: Invalid control character at: line 3 column 4 (char 5), content: 

["{
    "type": "NOT/
    CULTURAL_RELATED",
    "reason": "布达拉宫广场是西藏著名的宗教和历史遗址，而那根拉山口是通往纳木错的重要山口，两者来自不同的文化和历史背景，没有直接的文化关联",
    "confidence": 0.7,
    "direction": "none"
}]

[]
2025-07-23 20:59:55,368 - INFO - __main__ - 已处理 27/200 个节点
2025-07-23 20:59:55,370 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 20:59:55,370 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 20:59:55,370 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:00:01,585 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如寺印经院
2025-07-23 21:00:01,592 - INFO - text_processor - 成功处理实体: 木如寺印经院
2025-07-23 21:00:06,468 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 下密寺
2025-07-23 21:00:06,475 - INFO - text_processor - 成功处理实体: 下密寺
2025-07-23 21:00:15,031 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驻藏大臣衙门遗址
2025-07-23 21:00:15,039 - INFO - text_processor - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 21:00:15,039 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 21:00:50,008 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 下密寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是藏传佛教格鲁派寺庙",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:00:50,008 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都涉及西藏的历史背景",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:00:50,009 - ERROR - text_processor - JSON 解析失败 for 下密寺 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "两者都属于藏传佛教文化体系，下密寺是密宗修习地，驻藏大臣衙门遗址涉及历史和政治，但都与藏传佛教有关。",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:00:50,009 - INFO - __main__ - 已处理 30/200 个节点
2025-07-23 21:00:50,009 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:00:50,010 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:00:50,010 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:00:54,176 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺路
2025-07-23 21:00:54,203 - INFO - text_processor - 成功处理实体: 小昭寺路
2025-07-23 21:00:54,204 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:01:06,436 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨民族文化艺术宫
2025-07-23 21:01:06,444 - INFO - text_processor - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 21:01:06,445 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:01:14,456 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-07-23 21:01:14,464 - INFO - text_processor - 成功处理实体: 5238
2025-07-23 21:01:14,464 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:01:14,464 - INFO - __main__ - 已处理 33/200 个节点
2025-07-23 21:01:14,464 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:01:14,464 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:01:14,464 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:01:14,464 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:01:24,694 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措扎西岛
2025-07-23 21:01:24,701 - INFO - text_processor - 成功处理实体: 纳木措扎西岛
2025-07-23 21:01:24,701 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:01:36,718 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药文化博览中心
2025-07-23 21:01:36,725 - INFO - text_processor - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 21:01:41,752 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-07-23 21:01:41,762 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-07-23 21:01:41,762 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 21:01:41,762 - INFO - __main__ - 已处理 36/200 个节点
2025-07-23 21:01:41,763 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:01:41,763 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:01:41,763 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:01:41,763 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:01:50,281 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甲玛王宫
2025-07-23 21:01:50,287 - INFO - text_processor - 成功处理实体: 甲玛王宫
2025-07-23 21:01:54,848 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎希寺
2025-07-23 21:01:54,855 - INFO - text_processor - 成功处理实体: 扎希寺
2025-07-23 21:02:00,479 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-07-23 21:02:00,485 - INFO - text_processor - 成功处理实体: 楚布寺
2025-07-23 21:02:00,486 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:02:00,486 - INFO - __main__ - 已处理 39/200 个节点
2025-07-23 21:02:00,486 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:02:00,486 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:02:00,486 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:02:04,355 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞干布出生地
2025-07-23 21:02:04,362 - INFO - text_processor - 成功处理实体: 松赞干布出生地
2025-07-23 21:02:09,843 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 聂当度母殿和聂当大佛
2025-07-23 21:02:09,880 - INFO - text_processor - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 21:02:16,606 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-07-23 21:02:16,613 - INFO - text_processor - 成功处理实体: 策门林寺
2025-07-23 21:02:16,614 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 21:02:16,614 - INFO - __main__ - 已处理 42/200 个节点
2025-07-23 21:02:16,614 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:02:16,614 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:02:16,614 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:02:21,468 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鲁普岩寺
2025-07-23 21:02:21,475 - INFO - text_processor - 成功处理实体: 鲁普岩寺
2025-07-23 21:02:26,823 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仙足岛
2025-07-23 21:02:26,829 - INFO - text_processor - 成功处理实体: 仙足岛
2025-07-23 21:02:31,860 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-07-23 21:02:31,869 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-07-23 21:02:31,870 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 21:02:31,870 - INFO - __main__ - 已处理 45/200 个节点
2025-07-23 21:02:31,870 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:02:31,870 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:02:31,870 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:02:34,934 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺
2025-07-23 21:02:34,944 - INFO - text_processor - 成功处理实体: 乃琼寺
2025-07-23 21:02:42,732 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次巴拉康寺
2025-07-23 21:02:42,743 - INFO - text_processor - 成功处理实体: 次巴拉康寺
2025-07-23 21:02:47,868 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 清政府驻藏大臣衙门旧址
2025-07-23 21:02:47,876 - INFO - text_processor - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 21:02:47,876 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 21:02:47,876 - INFO - __main__ - 已处理 48/200 个节点
2025-07-23 21:02:47,876 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:02:47,876 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:02:47,876 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:02:47,877 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:02:59,304 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热振寺
2025-07-23 21:02:59,320 - INFO - text_processor - 成功处理实体: 热振寺
2025-07-23 21:03:03,887 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 关帝庙
2025-07-23 21:03:03,894 - INFO - text_processor - 成功处理实体: 关帝庙
2025-07-23 21:03:03,894 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:13,785 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-07-23 21:03:13,795 - INFO - text_processor - 成功处理实体: 喜德林
2025-07-23 21:03:13,795 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:03:13,795 - INFO - __main__ - 已处理 51/200 个节点
2025-07-23 21:03:13,795 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:03:13,796 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:03:13,796 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:03:13,796 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:20,557 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白色寺
2025-07-23 21:03:20,565 - INFO - text_processor - 成功处理实体: 白色寺
2025-07-23 21:03:20,566 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:26,369 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-07-23 21:03:26,376 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-23 21:03:26,376 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:33,510 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木寺
2025-07-23 21:03:33,516 - INFO - text_processor - 成功处理实体: 纳木寺
2025-07-23 21:03:33,516 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:03:33,516 - INFO - __main__ - 已处理 54/200 个节点
2025-07-23 21:03:33,516 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:03:33,516 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:03:33,517 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:03:33,517 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:39,466 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县楚杰寺
2025-07-23 21:03:39,475 - INFO - text_processor - 成功处理实体: 林周县楚杰寺
2025-07-23 21:03:39,475 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:47,328 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 中华文化园露营地
2025-07-23 21:03:47,335 - INFO - text_processor - 成功处理实体: 中华文化园露营地
2025-07-23 21:03:47,335 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:03:55,697 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 秀色才纳净土博览园
2025-07-23 21:03:55,704 - INFO - text_processor - 成功处理实体: 秀色才纳净土博览园
2025-07-23 21:03:55,704 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:03:55,704 - INFO - __main__ - 已处理 57/200 个节点
2025-07-23 21:03:55,704 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:03:55,704 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:03:55,705 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:03:55,705 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:00,788 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶马古道马帮落脚点遗址
2025-07-23 21:04:00,820 - INFO - text_processor - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-23 21:04:00,820 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:08,122 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 山野星空露营-吉隆营地
2025-07-23 21:04:08,128 - INFO - text_processor - 成功处理实体: 山野星空露营-吉隆营地
2025-07-23 21:04:08,128 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:13,389 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 奇秀园林
2025-07-23 21:04:13,395 - INFO - text_processor - 成功处理实体: 奇秀园林
2025-07-23 21:04:13,396 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:04:13,396 - INFO - __main__ - 已处理 60/200 个节点
2025-07-23 21:04:13,397 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:04:13,397 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:04:13,397 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:04:13,397 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:20,428 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛堆吉曲康桑区
2025-07-23 21:04:20,436 - INFO - text_processor - 成功处理实体: 洛堆吉曲康桑区
2025-07-23 21:04:20,437 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:30,003 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-07-23 21:04:30,012 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-07-23 21:04:30,012 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:45,742 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔玉贡康寺
2025-07-23 21:04:45,748 - INFO - text_processor - 成功处理实体: 塔玉贡康寺
2025-07-23 21:04:45,748 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:04:45,749 - INFO - __main__ - 已处理 63/200 个节点
2025-07-23 21:04:45,749 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:04:45,749 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:04:45,749 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:04:45,749 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:04:51,037 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉布寺
2025-07-23 21:04:51,043 - INFO - text_processor - 成功处理实体: 吉布寺
2025-07-23 21:04:51,043 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:01,485 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 美珠杂日生态乐园
2025-07-23 21:05:01,491 - INFO - text_processor - 成功处理实体: 美珠杂日生态乐园
2025-07-23 21:05:01,491 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:06,457 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 舒适的露营地
2025-07-23 21:05:06,465 - INFO - text_processor - 成功处理实体: 舒适的露营地
2025-07-23 21:05:06,465 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:05:06,465 - INFO - __main__ - 已处理 66/200 个节点
2025-07-23 21:05:06,465 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:05:06,465 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:05:06,466 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:05:06,466 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:11,256 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 钱学森雕像
2025-07-23 21:05:11,261 - INFO - text_processor - 成功处理实体: 钱学森雕像
2025-07-23 21:05:11,262 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:17,924 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-07-23 21:05:17,929 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-23 21:05:17,930 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:23,432 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 明珠林
2025-07-23 21:05:23,437 - INFO - text_processor - 成功处理实体: 明珠林
2025-07-23 21:05:23,437 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:05:23,438 - INFO - __main__ - 已处理 69/200 个节点
2025-07-23 21:05:23,438 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:05:23,438 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:05:23,438 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:05:23,438 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:33,814 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏林周草牧业科技小院
2025-07-23 21:05:33,824 - INFO - text_processor - 成功处理实体: 西藏林周草牧业科技小院
2025-07-23 21:05:33,824 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:46,624 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 内马厩
2025-07-23 21:05:46,637 - INFO - text_processor - 成功处理实体: 内马厩
2025-07-23 21:05:46,637 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:05:53,736 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尤隆布
2025-07-23 21:05:53,743 - INFO - text_processor - 成功处理实体: 尤隆布
2025-07-23 21:05:53,743 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:05:53,744 - INFO - __main__ - 已处理 72/200 个节点
2025-07-23 21:05:53,744 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:05:53,744 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:05:53,744 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:05:53,744 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:10,282 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙日秋摩
2025-07-23 21:06:10,289 - INFO - text_processor - 成功处理实体: 龙日秋摩
2025-07-23 21:06:10,290 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:19,727 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-07-23 21:06:19,732 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-07-23 21:06:19,732 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:25,803 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布朗
2025-07-23 21:06:25,810 - INFO - text_processor - 成功处理实体: 崩布朗
2025-07-23 21:06:25,810 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:06:25,811 - INFO - __main__ - 已处理 75/200 个节点
2025-07-23 21:06:25,811 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:06:25,811 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:06:25,811 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:06:25,811 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:31,655 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 加错
2025-07-23 21:06:31,661 - INFO - text_processor - 成功处理实体: 加错
2025-07-23 21:06:31,661 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:39,383 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达尔多拉
2025-07-23 21:06:39,390 - INFO - text_processor - 成功处理实体: 达尔多拉
2025-07-23 21:06:39,390 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:06:50,709 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县杰堆寺
2025-07-23 21:06:50,715 - INFO - text_processor - 成功处理实体: 林周县杰堆寺
2025-07-23 21:06:50,715 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:06:50,715 - INFO - __main__ - 已处理 78/200 个节点
2025-07-23 21:06:50,715 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:06:50,715 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:06:50,715 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:06:50,716 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:00,428 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎日阿布塘
2025-07-23 21:07:00,436 - INFO - text_processor - 成功处理实体: 扎日阿布塘
2025-07-23 21:07:00,436 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:05,695 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关区净土国家级旅游景区
2025-07-23 21:07:05,702 - INFO - text_processor - 成功处理实体: 城关区净土国家级旅游景区
2025-07-23 21:07:05,703 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:11,392 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-07-23 21:07:11,400 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-07-23 21:07:11,400 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:07:11,400 - INFO - __main__ - 已处理 81/200 个节点
2025-07-23 21:07:11,401 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:07:11,401 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:07:11,401 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:07:11,401 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:19,677 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城北萨斯格桑林卡
2025-07-23 21:07:19,688 - INFO - text_processor - 成功处理实体: 城北萨斯格桑林卡
2025-07-23 21:07:19,689 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:25,888 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏唐卡画院
2025-07-23 21:07:25,894 - INFO - text_processor - 成功处理实体: 西藏唐卡画院
2025-07-23 21:07:25,895 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:31,303 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨奇趣昆虫科普展
2025-07-23 21:07:31,309 - INFO - text_processor - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-23 21:07:31,309 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:07:31,309 - INFO - __main__ - 已处理 84/200 个节点
2025-07-23 21:07:31,309 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:07:31,309 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:07:31,309 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:07:31,310 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:38,137 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-07-23 21:07:38,144 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-07-23 21:07:38,144 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:46,038 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 涉溪山谷露营地
2025-07-23 21:07:46,044 - INFO - text_processor - 成功处理实体: 涉溪山谷露营地
2025-07-23 21:07:46,044 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:07:57,245 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尼木吞巴非遗中心
2025-07-23 21:07:57,256 - INFO - text_processor - 成功处理实体: 尼木吞巴非遗中心
2025-07-23 21:07:57,257 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:07:57,257 - INFO - __main__ - 已处理 87/200 个节点
2025-07-23 21:07:57,258 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:07:57,258 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:07:57,258 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:07:57,259 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:08:10,003 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 群巴
2025-07-23 21:08:10,013 - INFO - text_processor - 成功处理实体: 群巴
2025-07-23 21:08:10,013 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:08:24,563 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热卡扎日追寺
2025-07-23 21:08:24,572 - INFO - text_processor - 成功处理实体: 热卡扎日追寺
2025-07-23 21:08:24,572 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:08:28,864 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-07-23 21:08:28,871 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-07-23 21:08:28,872 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:08:28,872 - INFO - __main__ - 已处理 90/200 个节点
2025-07-23 21:08:28,872 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:08:28,872 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:08:28,872 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:08:28,873 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:08:42,276 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-07-23 21:08:42,282 - INFO - text_processor - 成功处理实体: 曲苏
2025-07-23 21:08:42,283 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:08:58,534 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-07-23 21:08:58,540 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-23 21:08:58,540 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:09:13,114 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 梦创拉萨
2025-07-23 21:09:13,120 - INFO - text_processor - 成功处理实体: 梦创拉萨
2025-07-23 21:09:13,120 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:09:13,120 - INFO - __main__ - 已处理 93/200 个节点
2025-07-23 21:09:13,121 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:09:13,121 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:09:13,121 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:09:13,121 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:09:20,496 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛日扎扎
2025-07-23 21:09:20,502 - INFO - text_processor - 成功处理实体: 玛日扎扎
2025-07-23 21:09:20,503 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:09:29,874 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪格拉山
2025-07-23 21:09:29,897 - INFO - text_processor - 成功处理实体: 雪格拉山
2025-07-23 21:09:29,897 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:09:39,309 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 密宗院
2025-07-23 21:09:39,317 - INFO - text_processor - 成功处理实体: 密宗院
2025-07-23 21:09:39,318 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:09:39,318 - INFO - __main__ - 已处理 96/200 个节点
2025-07-23 21:09:39,318 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:09:39,318 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:09:39,318 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:09:39,318 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:09:50,584 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 科比台球纪念馆
2025-07-23 21:09:50,590 - INFO - text_processor - 成功处理实体: 科比台球纪念馆
2025-07-23 21:09:50,591 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:01,685 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-07-23 21:10:01,691 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-07-23 21:10:01,691 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:20,424 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 颂吉东阁大昭寺店
2025-07-23 21:10:20,430 - INFO - text_processor - 成功处理实体: 颂吉东阁大昭寺店
2025-07-23 21:10:20,430 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:10:20,431 - INFO - __main__ - 已处理 99/200 个节点
2025-07-23 21:10:20,431 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:10:20,431 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:10:20,431 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:10:20,431 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:26,330 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水木林卡
2025-07-23 21:10:26,336 - INFO - text_processor - 成功处理实体: 水木林卡
2025-07-23 21:10:26,336 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:35,275 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 柳梧桑女林卡
2025-07-23 21:10:35,284 - INFO - text_processor - 成功处理实体: 柳梧桑女林卡
2025-07-23 21:10:35,284 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:42,263 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思源山
2025-07-23 21:10:42,270 - INFO - text_processor - 成功处理实体: 思源山
2025-07-23 21:10:42,270 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:10:42,270 - INFO - __main__ - 已处理 102/200 个节点
2025-07-23 21:10:42,270 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:10:42,270 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:10:42,271 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:10:42,271 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:10:51,860 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德曲郭卓吉林卡
2025-07-23 21:10:51,869 - INFO - text_processor - 成功处理实体: 喜德曲郭卓吉林卡
2025-07-23 21:10:51,869 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:03,881 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 24小时自助图书馆(西藏图书馆店)
2025-07-23 21:11:03,890 - INFO - text_processor - 成功处理实体: 24小时自助图书馆(西藏图书馆店)
2025-07-23 21:11:03,891 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:16,638 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 朗唐寺
2025-07-23 21:11:16,646 - INFO - text_processor - 成功处理实体: 朗唐寺
2025-07-23 21:11:16,646 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:11:16,647 - INFO - __main__ - 已处理 105/200 个节点
2025-07-23 21:11:16,647 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:11:16,647 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:11:16,647 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:11:16,647 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:26,037 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 智昭产业园净土生态体验馆
2025-07-23 21:11:26,044 - INFO - text_processor - 成功处理实体: 智昭产业园净土生态体验馆
2025-07-23 21:11:26,044 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:32,337 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏擦擦文化展览馆
2025-07-23 21:11:32,346 - INFO - text_processor - 成功处理实体: 西藏擦擦文化展览馆
2025-07-23 21:11:32,346 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:48,328 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-供灯室
2025-07-23 21:11:48,334 - INFO - text_processor - 成功处理实体: 大昭寺-供灯室
2025-07-23 21:11:48,334 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:11:48,334 - INFO - __main__ - 已处理 108/200 个节点
2025-07-23 21:11:48,335 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:11:48,335 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:11:48,335 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:11:48,335 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:11:57,354 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 21:11:57,360 - INFO - text_processor - 成功处理实体: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 21:11:57,360 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:07,873 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达巧拉康
2025-07-23 21:12:07,879 - INFO - text_processor - 成功处理实体: 达巧拉康
2025-07-23 21:12:07,880 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:17,159 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨湾(网红打卡点)
2025-07-23 21:12:17,168 - INFO - text_processor - 成功处理实体: 拉萨湾(网红打卡点)
2025-07-23 21:12:17,168 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:12:17,168 - INFO - __main__ - 已处理 111/200 个节点
2025-07-23 21:12:17,169 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:12:17,169 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:12:17,169 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:12:17,169 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:34,423 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞拉康
2025-07-23 21:12:34,431 - INFO - text_processor - 成功处理实体: 松赞拉康
2025-07-23 21:12:34,431 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:41,579 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 21:12:41,584 - INFO - text_processor - 成功处理实体: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 21:12:41,584 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:48,027 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德勒林卡
2025-07-23 21:12:48,056 - INFO - text_processor - 成功处理实体: 德勒林卡
2025-07-23 21:12:48,056 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:12:48,056 - INFO - __main__ - 已处理 114/200 个节点
2025-07-23 21:12:48,057 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:12:48,057 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:12:48,057 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:12:48,057 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:12:55,335 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羌仓
2025-07-23 21:12:55,341 - INFO - text_processor - 成功处理实体: 羌仓
2025-07-23 21:12:55,342 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:00,785 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 依拉
2025-07-23 21:13:00,793 - INFO - text_processor - 成功处理实体: 依拉
2025-07-23 21:13:00,793 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:08,354 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玉隆
2025-07-23 21:13:08,361 - INFO - text_processor - 成功处理实体: 玉隆
2025-07-23 21:13:08,361 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:13:08,361 - INFO - __main__ - 已处理 117/200 个节点
2025-07-23 21:13:08,361 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:13:08,361 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:13:08,361 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:13:08,362 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:16,047 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 21:13:16,055 - INFO - text_processor - 成功处理实体: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 21:13:16,055 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:25,515 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林宗萨
2025-07-23 21:13:25,523 - INFO - text_processor - 成功处理实体: 德吉林宗萨
2025-07-23 21:13:25,523 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:31,801 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 强嘎日追寺
2025-07-23 21:13:31,807 - INFO - text_processor - 成功处理实体: 强嘎日追寺
2025-07-23 21:13:31,808 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:13:31,808 - INFO - __main__ - 已处理 120/200 个节点
2025-07-23 21:13:31,808 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:13:31,808 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:13:31,808 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:13:31,808 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:40,029 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺-厨房
2025-07-23 21:13:40,035 - INFO - text_processor - 成功处理实体: 乃琼寺-厨房
2025-07-23 21:13:40,036 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:13:53,818 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷拉
2025-07-23 21:13:53,827 - INFO - text_processor - 成功处理实体: 列卜廷拉
2025-07-23 21:13:53,828 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:07,794 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-07-23 21:14:07,800 - INFO - text_processor - 成功处理实体: 阿热康参
2025-07-23 21:14:07,800 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:14:07,800 - INFO - __main__ - 已处理 123/200 个节点
2025-07-23 21:14:07,800 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:14:07,800 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:14:07,801 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:14:07,801 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:20,217 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺-卓玛拉康
2025-07-23 21:14:20,226 - INFO - text_processor - 成功处理实体: 哲蚌寺-卓玛拉康
2025-07-23 21:14:20,226 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:25,845 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园纪念碑
2025-07-23 21:14:25,851 - INFO - text_processor - 成功处理实体: 宗角禄康公园纪念碑
2025-07-23 21:14:25,852 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:31,877 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺辩经场
2025-07-23 21:14:31,885 - INFO - text_processor - 成功处理实体: 小昭寺辩经场
2025-07-23 21:14:31,885 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:14:31,886 - INFO - __main__ - 已处理 126/200 个节点
2025-07-23 21:14:31,886 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:14:31,886 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:14:31,886 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:14:31,886 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:37,906 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺扎迪(林卡)
2025-07-23 21:14:37,913 - INFO - text_processor - 成功处理实体: 色拉寺扎迪(林卡)
2025-07-23 21:14:37,913 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:14:50,843 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错国家风景区
2025-07-23 21:14:50,850 - INFO - text_processor - 成功处理实体: 纳木错国家风景区
2025-07-23 21:14:50,850 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:05,862 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 国家级拉萨经开区
2025-07-23 21:15:05,868 - INFO - text_processor - 成功处理实体: 国家级拉萨经开区
2025-07-23 21:15:05,868 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:15:05,868 - INFO - __main__ - 已处理 129/200 个节点
2025-07-23 21:15:05,868 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:15:05,868 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:15:05,869 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:15:05,869 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:14,968 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰热康参
2025-07-23 21:15:14,974 - INFO - text_processor - 成功处理实体: 恰热康参
2025-07-23 21:15:14,974 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:20,585 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺杰扎仓
2025-07-23 21:15:20,591 - INFO - text_processor - 成功处理实体: 色拉寺杰扎仓
2025-07-23 21:15:20,592 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:29,662 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 丁真隆巴
2025-07-23 21:15:29,668 - INFO - text_processor - 成功处理实体: 丁真隆巴
2025-07-23 21:15:29,669 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:15:29,669 - INFO - __main__ - 已处理 132/200 个节点
2025-07-23 21:15:29,669 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:15:29,669 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:15:29,669 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:15:29,670 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:42,377 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那若果
2025-07-23 21:15:42,385 - INFO - text_processor - 成功处理实体: 那若果
2025-07-23 21:15:42,385 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:48,380 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 所布多纳
2025-07-23 21:15:48,386 - INFO - text_processor - 成功处理实体: 所布多纳
2025-07-23 21:15:48,386 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:15:58,684 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉大乘洲
2025-07-23 21:15:58,691 - INFO - text_processor - 成功处理实体: 色拉大乘洲
2025-07-23 21:15:58,691 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:15:58,691 - INFO - __main__ - 已处理 135/200 个节点
2025-07-23 21:15:58,691 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:15:58,691 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:15:58,691 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:15:58,691 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:16:12,052 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错
2025-07-23 21:16:12,058 - INFO - text_processor - 成功处理实体: 纳木错
2025-07-23 21:16:12,058 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:16:24,525 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 悠远的天空自驾车营地
2025-07-23 21:16:24,532 - INFO - text_processor - 成功处理实体: 悠远的天空自驾车营地
2025-07-23 21:16:24,532 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:16:38,116 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪江憩居鲁突
2025-07-23 21:16:38,123 - INFO - text_processor - 成功处理实体: 雪江憩居鲁突
2025-07-23 21:16:38,123 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:16:38,123 - INFO - __main__ - 已处理 138/200 个节点
2025-07-23 21:16:38,123 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:16:38,124 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:16:38,124 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:16:38,124 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:16:47,464 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑珠颇章
2025-07-23 21:16:47,470 - INFO - text_processor - 成功处理实体: 桑珠颇章
2025-07-23 21:16:47,470 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:16:59,399 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 千年古树
2025-07-23 21:16:59,406 - INFO - text_processor - 成功处理实体: 千年古树
2025-07-23 21:16:59,406 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:11,557 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨之眼
2025-07-23 21:17:11,564 - INFO - text_processor - 成功处理实体: 拉萨之眼
2025-07-23 21:17:11,565 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:17:11,565 - INFO - __main__ - 已处理 141/200 个节点
2025-07-23 21:17:11,565 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:17:11,565 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:17:11,565 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:17:11,566 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:19,684 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨市综合展馆
2025-07-23 21:17:19,691 - INFO - text_processor - 成功处理实体: 拉萨市综合展馆
2025-07-23 21:17:19,692 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:25,884 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 顶杰寺
2025-07-23 21:17:25,891 - INFO - text_processor - 成功处理实体: 顶杰寺
2025-07-23 21:17:25,891 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:31,494 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗格岭
2025-07-23 21:17:31,502 - INFO - text_processor - 成功处理实体: 罗格岭
2025-07-23 21:17:31,503 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:17:31,503 - INFO - __main__ - 已处理 144/200 个节点
2025-07-23 21:17:31,503 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:17:31,503 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:17:31,503 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:17:31,503 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:35,782 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏医药文化馆
2025-07-23 21:17:35,788 - INFO - text_processor - 成功处理实体: 藏医药文化馆
2025-07-23 21:17:35,788 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:40,874 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日月湖
2025-07-23 21:17:40,880 - INFO - text_processor - 成功处理实体: 日月湖
2025-07-23 21:17:40,881 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:17:54,422 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-07-23 21:17:54,430 - INFO - text_processor - 成功处理实体: 水上乐园
2025-07-23 21:17:54,430 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:17:54,431 - INFO - __main__ - 已处理 147/200 个节点
2025-07-23 21:17:54,431 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:17:54,431 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:17:54,431 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:17:54,431 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:08,697 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白纳村
2025-07-23 21:18:08,704 - INFO - text_processor - 成功处理实体: 白纳村
2025-07-23 21:18:08,704 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:21,448 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拼尤明丘
2025-07-23 21:18:21,454 - INFO - text_processor - 成功处理实体: 拼尤明丘
2025-07-23 21:18:21,455 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:30,906 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驳叶
2025-07-23 21:18:30,913 - INFO - text_processor - 成功处理实体: 驳叶
2025-07-23 21:18:30,914 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:18:30,914 - INFO - __main__ - 已处理 150/200 个节点
2025-07-23 21:18:30,914 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:18:30,914 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:18:30,915 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:18:30,915 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:38,408 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 勒望来坰
2025-07-23 21:18:38,416 - INFO - text_processor - 成功处理实体: 勒望来坰
2025-07-23 21:18:38,416 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:45,091 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 斋囊
2025-07-23 21:18:45,098 - INFO - text_processor - 成功处理实体: 斋囊
2025-07-23 21:18:45,098 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:18:55,161 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 绿子
2025-07-23 21:18:55,168 - INFO - text_processor - 成功处理实体: 绿子
2025-07-23 21:18:55,168 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:18:55,168 - INFO - __main__ - 已处理 153/200 个节点
2025-07-23 21:18:55,169 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:18:55,169 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:18:55,169 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:18:55,170 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:01,827 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列玛拉
2025-07-23 21:19:01,837 - INFO - text_processor - 成功处理实体: 列玛拉
2025-07-23 21:19:01,838 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:13,734 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿巴吉康萨巴
2025-07-23 21:19:13,741 - INFO - text_processor - 成功处理实体: 阿巴吉康萨巴
2025-07-23 21:19:13,741 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:20,810 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 苏布列日
2025-07-23 21:19:20,816 - INFO - text_processor - 成功处理实体: 苏布列日
2025-07-23 21:19:20,817 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:19:20,817 - INFO - __main__ - 已处理 156/200 个节点
2025-07-23 21:19:20,817 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:19:20,817 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:19:20,817 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:19:20,817 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:27,170 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松格果觉(白宫门厅)
2025-07-23 21:19:27,178 - INFO - text_processor - 成功处理实体: 松格果觉(白宫门厅)
2025-07-23 21:19:27,178 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:32,610 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林卡
2025-07-23 21:19:32,617 - INFO - text_processor - 成功处理实体: 德吉林卡
2025-07-23 21:19:32,617 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:42,545 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 支吾康参
2025-07-23 21:19:42,553 - INFO - text_processor - 成功处理实体: 支吾康参
2025-07-23 21:19:42,553 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:19:42,554 - INFO - __main__ - 已处理 159/200 个节点
2025-07-23 21:19:42,554 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:19:42,554 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:19:42,554 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:19:42,554 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:19:49,234 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔布康参
2025-07-23 21:19:49,241 - INFO - text_processor - 成功处理实体: 塔布康参
2025-07-23 21:19:49,241 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:00,850 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰杰玛
2025-07-23 21:20:00,858 - INFO - text_processor - 成功处理实体: 恰杰玛
2025-07-23 21:20:00,858 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:08,617 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 申扎琼
2025-07-23 21:20:08,623 - INFO - text_processor - 成功处理实体: 申扎琼
2025-07-23 21:20:08,623 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:20:08,624 - INFO - __main__ - 已处理 162/200 个节点
2025-07-23 21:20:08,624 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:20:08,624 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:20:08,624 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:20:08,624 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:19,307 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达孜映休闲露营景区
2025-07-23 21:20:19,314 - INFO - text_processor - 成功处理实体: 达孜映休闲露营景区
2025-07-23 21:20:19,314 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:27,517 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 札利贝札囊
2025-07-23 21:20:27,525 - INFO - text_processor - 成功处理实体: 札利贝札囊
2025-07-23 21:20:27,525 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:38,412 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思泽
2025-07-23 21:20:38,419 - INFO - text_processor - 成功处理实体: 思泽
2025-07-23 21:20:38,419 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:20:38,419 - INFO - __main__ - 已处理 165/200 个节点
2025-07-23 21:20:38,419 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:20:38,420 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:20:38,420 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:20:38,420 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:20:55,285 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 救八难度母像
2025-07-23 21:20:55,293 - INFO - text_processor - 成功处理实体: 救八难度母像
2025-07-23 21:20:55,293 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:08,841 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德布
2025-07-23 21:21:08,850 - INFO - text_processor - 成功处理实体: 德布
2025-07-23 21:21:08,851 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:14,730 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏巴吉仓
2025-07-23 21:21:14,738 - INFO - text_processor - 成功处理实体: 藏巴吉仓
2025-07-23 21:21:14,738 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:21:14,738 - INFO - __main__ - 已处理 168/200 个节点
2025-07-23 21:21:14,738 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:21:14,739 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:21:14,739 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:21:14,739 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:22,971 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 弥勒法轮像
2025-07-23 21:21:22,978 - INFO - text_processor - 成功处理实体: 弥勒法轮像
2025-07-23 21:21:22,978 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:31,228 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑顶大门
2025-07-23 21:21:31,235 - INFO - text_processor - 成功处理实体: 桑顶大门
2025-07-23 21:21:31,235 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:38,531 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德玛康参
2025-07-23 21:21:38,537 - INFO - text_processor - 成功处理实体: 德玛康参
2025-07-23 21:21:38,537 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:21:38,537 - INFO - __main__ - 已处理 171/200 个节点
2025-07-23 21:21:38,537 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:21:38,537 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:21:38,538 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:21:38,538 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:21:52,487 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏墨龙
2025-07-23 21:21:52,494 - INFO - text_processor - 成功处理实体: 西藏墨龙
2025-07-23 21:21:52,494 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:01,254 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两路精神纪念馆
2025-07-23 21:22:01,259 - INFO - text_processor - 成功处理实体: 两路精神纪念馆
2025-07-23 21:22:01,259 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:07,980 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 陕西民俗村
2025-07-23 21:22:07,987 - INFO - text_processor - 成功处理实体: 陕西民俗村
2025-07-23 21:22:07,987 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:22:07,988 - INFO - __main__ - 已处理 174/200 个节点
2025-07-23 21:22:07,988 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:22:07,988 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:22:07,988 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:22:07,988 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:16,986 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡德康参
2025-07-23 21:22:16,991 - INFO - text_processor - 成功处理实体: 贡德康参
2025-07-23 21:22:16,991 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:26,443 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡布岗
2025-07-23 21:22:26,449 - INFO - text_processor - 成功处理实体: 贡布岗
2025-07-23 21:22:26,450 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:32,258 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑康吉康
2025-07-23 21:22:32,265 - INFO - text_processor - 成功处理实体: 桑康吉康
2025-07-23 21:22:32,265 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:22:32,265 - INFO - __main__ - 已处理 177/200 个节点
2025-07-23 21:22:32,265 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:22:32,266 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:22:32,266 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:22:32,266 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:42,393 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 坛城院
2025-07-23 21:22:42,400 - INFO - text_processor - 成功处理实体: 坛城院
2025-07-23 21:22:42,400 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:50,517 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 墨脱石锅
2025-07-23 21:22:50,524 - INFO - text_processor - 成功处理实体: 墨脱石锅
2025-07-23 21:22:50,524 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:22:58,292 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关净土
2025-07-23 21:22:58,299 - INFO - text_processor - 成功处理实体: 城关净土
2025-07-23 21:22:58,300 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:22:58,300 - INFO - __main__ - 已处理 180/200 个节点
2025-07-23 21:22:58,300 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:22:58,300 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:22:58,300 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:22:58,300 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:06,037 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宝藏局雪造币厂
2025-07-23 21:23:06,044 - INFO - text_processor - 成功处理实体: 宝藏局雪造币厂
2025-07-23 21:23:06,044 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:11,757 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 札隆纳交
2025-07-23 21:23:11,768 - INFO - text_processor - 成功处理实体: 札隆纳交
2025-07-23 21:23:11,768 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:18,879 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西印经院
2025-07-23 21:23:18,884 - INFO - text_processor - 成功处理实体: 西印经院
2025-07-23 21:23:18,884 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:23:18,885 - INFO - __main__ - 已处理 183/200 个节点
2025-07-23 21:23:18,885 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:23:18,885 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:23:18,885 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:23:18,885 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:28,362 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫观景台
2025-07-23 21:23:28,368 - INFO - text_processor - 成功处理实体: 布达拉宫观景台
2025-07-23 21:23:28,368 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:34,877 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶文化馆
2025-07-23 21:23:34,884 - INFO - text_processor - 成功处理实体: 茶文化馆
2025-07-23 21:23:34,884 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:23:49,105 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏闽兴
2025-07-23 21:23:49,112 - INFO - text_processor - 成功处理实体: 西藏闽兴
2025-07-23 21:23:49,112 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:23:49,112 - INFO - __main__ - 已处理 186/200 个节点
2025-07-23 21:23:49,112 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:23:49,113 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:23:49,113 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:23:49,113 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:02,872 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 措钦大殿
2025-07-23 21:24:02,880 - INFO - text_processor - 成功处理实体: 措钦大殿
2025-07-23 21:24:02,880 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:13,200 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 猴爸
2025-07-23 21:24:13,206 - INFO - text_processor - 成功处理实体: 猴爸
2025-07-23 21:24:13,207 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:22,435 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 唐金寺
2025-07-23 21:24:22,440 - INFO - text_processor - 成功处理实体: 唐金寺
2025-07-23 21:24:22,441 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:24:22,441 - INFO - __main__ - 已处理 189/200 个节点
2025-07-23 21:24:22,441 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:24:22,441 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:24:22,441 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:24:22,441 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:29,648 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 巴嘎日
2025-07-23 21:24:29,654 - INFO - text_processor - 成功处理实体: 巴嘎日
2025-07-23 21:24:29,654 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:35,650 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 露野营地(城西店)
2025-07-23 21:24:35,656 - INFO - text_processor - 成功处理实体: 露野营地(城西店)
2025-07-23 21:24:35,656 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:47,462 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 冬加夏日
2025-07-23 21:24:47,469 - INFO - text_processor - 成功处理实体: 冬加夏日
2025-07-23 21:24:47,470 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:24:47,470 - INFO - __main__ - 已处理 192/200 个节点
2025-07-23 21:24:47,470 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:24:47,470 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:24:47,470 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:24:47,470 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:24:53,780 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-07-23 21:24:53,789 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-07-23 21:24:53,790 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:03,120 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲水净土百亩连栋温室
2025-07-23 21:25:03,126 - INFO - text_processor - 成功处理实体: 曲水净土百亩连栋温室
2025-07-23 21:25:03,126 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:11,685 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷囊
2025-07-23 21:25:11,691 - INFO - text_processor - 成功处理实体: 列卜廷囊
2025-07-23 21:25:11,692 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:25:11,692 - INFO - __main__ - 已处理 195/200 个节点
2025-07-23 21:25:11,692 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:25:11,692 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:25:11,692 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:25:11,693 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:18,854 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两岛街道综治网格一体化中心
2025-07-23 21:25:18,886 - INFO - text_processor - 成功处理实体: 两岛街道综治网格一体化中心
2025-07-23 21:25:18,886 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:28,544 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 堆龙德庆区旅游服务区
2025-07-23 21:25:28,550 - INFO - text_processor - 成功处理实体: 堆龙德庆区旅游服务区
2025-07-23 21:25:28,551 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:34,489 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-讲经场
2025-07-23 21:25:34,495 - INFO - text_processor - 成功处理实体: 大昭寺-讲经场
2025-07-23 21:25:34,495 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:25:34,495 - INFO - __main__ - 已处理 198/200 个节点
2025-07-23 21:25:34,495 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:25:34,496 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:25:34,496 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:25:34,496 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:41,312 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 历辈达赖喇嘛宝座
2025-07-23 21:25:41,318 - INFO - text_processor - 成功处理实体: 历辈达赖喇嘛宝座
2025-07-23 21:25:41,319 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:25:47,470 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴康参
2025-07-23 21:25:47,476 - INFO - text_processor - 成功处理实体: 洛巴康参
2025-07-23 21:25:47,476 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 21:25:47,476 - INFO - __main__ - 已处理 200/200 个节点
2025-07-23 21:25:47,476 - INFO - __main__ - 批次处理完成 - 大小: 200, 耗时: 1909.31秒, 成功: 200, 失败: 0
2025-07-23 21:25:49,480 - INFO - __main__ - 
==================================================
2025-07-23 21:25:49,480 - INFO - __main__ - 开始处理批次大小: 250
2025-07-23 21:25:49,480 - INFO - __main__ - ==================================================
2025-07-23 21:25:49,481 - INFO - __main__ - 开始处理批次，大小: 250
2025-07-23 21:25:49,503 - INFO - neo4j_connection - 数据库已清空
2025-07-23 21:25:49,507 - INFO - neo4j_connection - 数据库已清空
2025-07-23 21:25:49,508 - INFO - text_processor - 清空所有节点和关系
2025-07-23 21:25:49,510 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 21:25:49,513 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 21:25:49,513 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 21:25:49,514 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:25:49,514 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:25:49,515 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:25:53,439 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 21:25:53,469 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 21:25:56,724 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 21:25:56,733 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 21:26:02,466 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 21:26:02,485 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 21:26:02,485 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 21:26:25,532 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMCOMPLETELY VISIT",
        "reason": "互补游览",
        "confidence": 0.85,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "历史关联",
        "confidence": 0.6,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一文化体系",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:26:25,533 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "布达拉宫是宗教圣地，而纳木措是自然景观，适合不同性质的游览",
        "confidence": 0.6,
        "direction": "forward"
    }
]
```
2025-07-23 21:26:25,617 - INFO - __main__ - 已处理 3/250 个节点
2025-07-23 21:26:25,618 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:26:25,618 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:26:25,618 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:26:30,924 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 21:26:30,973 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 21:26:35,734 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 21:26:35,742 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 21:26:35,743 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 21:26:41,566 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 21:26:41,605 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 21:26:41,606 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 21:27:18,407 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:27:18,408 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是拉萨市的景点，且都具有宗教和文化背景",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都与西藏的文化和宗教历史有关",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:27:18,408 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "都在拉萨市",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教文化，同时涉及历史元素",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 21:27:18,408 - INFO - __main__ - 已处理 6/250 个节点
2025-07-23 21:27:18,408 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 21:27:18,408 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 21:27:18,408 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 21:27:23,522 - ERROR - neo4j.io - [#1209]  _: <CONNECTION> error: Failed to write data to connection IPv4Address(('localhost', 7687)) (ResolvedIPv6Address(('::1', 7687, 0, 0))): ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
2025-07-23 21:27:23,523 - WARNING - neo4j.pool - Transaction failed and will be retried in 0.968152591899828s (Failed to write data to connection IPv4Address(('localhost', 7687)) (ResolvedIPv6Address(('::1', 7687, 0, 0))))
2025-07-23 21:27:28,573 - WARNING - neo4j.pool - Transaction failed and will be retried in 1.6311683518533238s (Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。))
2025-07-23 21:27:34,317 - WARNING - neo4j.pool - Transaction failed and will be retried in 4.137593909031462s (Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。))
2025-07-23 21:27:42,584 - WARNING - neo4j.pool - Transaction failed and will be retried in 8.133338664531557s (Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。))
2025-07-23 21:27:54,789 - ERROR - neo4j_crud - 执行数据库操作时发生未知错误: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-23 21:27:54,792 - WARNING - knowledge_graph_updater - 创建 Attraction 节点失败: 罗布林卡
2025-07-23 21:27:54,792 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 21:28:06,000 - WARNING - neo4j.pool - Transaction failed and will be retried in 0.9488347978779079s (Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。))
2025-07-23 21:28:11,058 - WARNING - neo4j.pool - Transaction failed and will be retried in 1.7613979231544978s (Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。))
2025-07-23 22:24:39,618 - INFO - neo4j_connection - 初始化 Neo4j 连接: bolt://localhost:7687
2025-07-23 22:24:39,650 - INFO - neo4j_connection - Neo4j 连接验证成功
2025-07-23 22:24:39,650 - INFO - __main__ - 成功连接到 Neo4j: bolt://localhost:7687
2025-07-23 22:24:39,651 - INFO - __main__ - 开始同步批量处理测试
2025-07-23 22:24:39,653 - INFO - __main__ - 加载数据完成，总节点数: 260, 去重后: 258
2025-07-23 22:24:39,653 - INFO - __main__ - 
==================================================
2025-07-23 22:24:39,653 - INFO - __main__ - 开始处理批次大小: 250
2025-07-23 22:24:39,653 - INFO - __main__ - ==================================================
2025-07-23 22:24:39,653 - INFO - __main__ - 开始处理批次，大小: 250
2025-07-23 22:24:39,882 - INFO - neo4j_connection - 数据库已清空
2025-07-23 22:24:39,886 - INFO - neo4j_connection - 数据库已清空
2025-07-23 22:24:39,886 - INFO - text_processor - 清空所有节点和关系
2025-07-23 22:24:39,945 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX attraction_name IF NOT EXISTS FOR (e:Attraction) ON (e.name)` has no effect.} {description: `RANGE INDEX attraction_name FOR (e:Attraction) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX attraction_name IF NOT EXISTS FOR (a:Attraction) ON (a.name)'
2025-07-23 22:24:39,954 - INFO - neo4j.notifications - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX city_name IF NOT EXISTS FOR (e:City) ON (e.name)` has no effect.} {description: `RANGE INDEX city_name FOR (e:City) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX city_name IF NOT EXISTS FOR (c:City) ON (c.name)'
2025-07-23 22:24:39,955 - INFO - text_processor - 创建 Attraction 和 City 名称索引
2025-07-23 22:24:39,955 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:24:39,955 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:24:39,955 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:24:47,215 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫
2025-07-23 22:24:47,393 - INFO - text_processor - 成功处理实体: 布达拉宫
2025-07-23 22:24:51,766 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺
2025-07-23 22:24:51,781 - INFO - text_processor - 成功处理实体: 大昭寺
2025-07-23 22:24:56,522 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措
2025-07-23 22:24:56,540 - INFO - text_processor - 成功处理实体: 纳木措
2025-07-23 22:24:56,541 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:25:14,762 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 大昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "NEARBY",
        "reason": "在同一个地区的中心区",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "属于同一宗教体系",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:25:14,763 - ERROR - text_processor - JSON 解析失败 for 布达拉宫 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "布达拉宫位于拉萨市中心，而纳木措则位于当雄县，二者位置相近，属于同一区域。",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "布达拉宫是 Duckworth，而纳木措是一个自然景观，两者都适合于游客的不同兴趣，可以一起游览，欣赏不同的美丽景象。",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:25:14,763 - ERROR - text_processor - JSON 解析失败 for 大昭寺 -> 纳木措: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    null
]
```
2025-07-23 22:25:14,764 - INFO - __main__ - 已处理 3/250 个节点
2025-07-23 22:25:14,764 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:25:14,764 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:25:14,765 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:25:22,358 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺
2025-07-23 22:25:22,434 - INFO - text_processor - 成功处理实体: 色拉寺
2025-07-23 22:25:28,620 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 八廓街
2025-07-23 22:25:28,635 - INFO - text_processor - 成功处理实体: 八廓街
2025-07-23 22:25:28,635 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:25:40,858 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 《文成公主》大型史诗剧
2025-07-23 22:25:40,924 - INFO - text_processor - 成功处理实体: 《文成公主》大型史诗剧
2025-07-23 22:25:40,924 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:26:10,534 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 八廓街: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙或历史街区，文化背景深厚",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:10,535 - ERROR - text_processor - JSON 解析失败 for 色拉寺 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

根据分析，这两个景点位于同一城市核心区域，属于地理相邻关系。因此，关系列表如下：

```json
[
    {
        "type": "NEARBY",
        "reason": "两者都位于拉萨市中心区域，地理位置相近",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:10,535 - ERROR - text_processor - JSON 解析失败 for 八廓街 -> 《文成公主》大型史诗剧: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，一个是古街，一个是演出",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教文化体系的景点",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:10,535 - INFO - __main__ - 已处理 6/250 个节点
2025-07-23 22:26:10,535 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:26:10,536 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:26:10,536 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:26:18,966 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗布林卡
2025-07-23 22:26:18,978 - INFO - text_processor - 成功处理实体: 罗布林卡
2025-07-23 22:26:26,876 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 药王山
2025-07-23 22:26:26,889 - INFO - text_processor - 成功处理实体: 药王山
2025-07-23 22:26:33,927 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园
2025-07-23 22:26:33,941 - INFO - text_processor - 成功处理实体: 宗角禄康公园
2025-07-23 22:26:33,941 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:26:47,131 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 药王山: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:47,131 - ERROR - text_processor - JSON 解析失败 for 罗布林卡 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于拉萨市的著名景点，都是游客常去的地方",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:47,132 - ERROR - text_processor - JSON 解析失败 for 药王山 -> 宗角禄康公园: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:26:47,132 - INFO - __main__ - 已处理 9/250 个节点
2025-07-23 22:26:47,133 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:26:47,133 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:26:47,133 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:26:52,637 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺
2025-07-23 22:26:52,654 - INFO - text_processor - 成功处理实体: 哲蚌寺
2025-07-23 22:27:00,892 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺
2025-07-23 22:27:00,905 - INFO - text_processor - 成功处理实体: 小昭寺
2025-07-23 22:27:07,690 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏博物馆
2025-07-23 22:27:07,733 - INFO - text_processor - 成功处理实体: 西藏博物馆
2025-07-23 22:27:07,733 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:27:29,209 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 小昭寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙",
        "confidence": 0.85,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:27:29,210 - ERROR - text_processor - JSON 解析失败 for 哲蚌寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:27:29,210 - ERROR - text_processor - JSON 解析失败 for 小昭寺 -> 西藏博物馆: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点，提供宗教和文化体验",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:27:29,211 - INFO - __main__ - 已处理 12/250 个节点
2025-07-23 22:27:29,212 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:27:29,212 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:27:29,212 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:27:29,212 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:27:40,103 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 娘热民俗风情园
2025-07-23 22:27:40,114 - INFO - text_processor - 成功处理实体: 娘热民俗风情园
2025-07-23 22:27:43,638 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仓姑寺
2025-07-23 22:27:43,652 - INFO - text_processor - 成功处理实体: 仓姑寺
2025-07-23 22:27:48,628 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎基寺
2025-07-23 22:27:48,639 - INFO - text_processor - 成功处理实体: 扎基寺
2025-07-23 22:27:48,640 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 22:27:53,844 - ERROR - text_processor - JSON 解析失败 for 仓姑寺 -> 扎基寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "互补游览（适合一起游览的景点）",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:27:53,846 - INFO - __main__ - 已处理 15/250 个节点
2025-07-23 22:27:53,847 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:27:53,847 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:27:53,848 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:27:57,806 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏拉萨清真大寺
2025-07-23 22:27:57,819 - INFO - text_processor - 成功处理实体: 西藏拉萨清真大寺
2025-07-23 22:28:03,890 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨河
2025-07-23 22:28:03,901 - INFO - text_processor - 成功处理实体: 拉萨河
2025-07-23 22:28:08,674 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 帕邦喀
2025-07-23 22:28:08,684 - INFO - text_processor - 成功处理实体: 帕邦喀
2025-07-23 22:28:08,685 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:28:35,319 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 拉萨河: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "适合一起游览的景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:28:35,319 - ERROR - text_processor - JSON 解析失败 for 西藏拉萨清真大寺 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两者位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "可以作为宗教文化景点的互补游览",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于宗教文化景点",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:28:35,320 - ERROR - text_processor - JSON 解析失败 for 拉萨河 -> 帕邦喀: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "帕邦喀位于加鲁镇，而拉萨河位于市中心，两者在拉萨市中心有相近的地理位置",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:28:35,320 - INFO - __main__ - 已处理 18/250 个节点
2025-07-23 22:28:35,321 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:28:35,321 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:28:35,321 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:28:39,897 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉鲁湿地国家级自然保护区
2025-07-23 22:28:39,949 - INFO - text_processor - 成功处理实体: 拉鲁湿地国家级自然保护区
2025-07-23 22:28:53,571 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德寺
2025-07-23 22:28:53,583 - INFO - text_processor - 成功处理实体: 喜德寺
2025-07-23 22:28:59,502 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羊八井
2025-07-23 22:28:59,513 - INFO - text_processor - 成功处理实体: 羊八井
2025-07-23 22:28:59,513 - INFO - text_processor - 过滤后景点数量: 2
2025-07-23 22:29:12,287 - ERROR - text_processor - JSON 解析失败 for 拉鲁湿地国家级自然保护区 -> 羊八井: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域，地理位置接近",
        "confidence": 0.7,
        "direction": "bidirectional"
    },
    {
        "type": "SIMILAR_TYPE",
        "reason": "都是自然景观，都位于拉萨市",
        "confidence": 0.9,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:29:12,289 - INFO - __main__ - 已处理 21/250 个节点
2025-07-23 22:29:12,290 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:29:12,290 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:29:12,291 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:29:12,291 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:29:17,410 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 直贡噶举派寺庙群
2025-07-23 22:29:17,422 - INFO - text_processor - 成功处理实体: 直贡噶举派寺庙群
2025-07-23 22:29:21,638 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甘丹寺
2025-07-23 22:29:21,653 - INFO - text_processor - 成功处理实体: 甘丹寺
2025-07-23 22:29:26,426 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 米拉山口
2025-07-23 22:29:26,438 - INFO - text_processor - 成功处理实体: 米拉山口
2025-07-23 22:29:26,439 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:29:43,834 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 甘丹寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "SIMILAR_TYPE",
        "reason": "两个景点都是寺庙",
        "confidence": 0.8,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，可能具有相关的文化背景",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:29:43,835 - ERROR - text_processor - JSON 解析失败 for 直贡噶举派寺庙群 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于墨竹工卡县，地理位置大致相近",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:29:43,836 - ERROR - text_processor - JSON 解析失败 for 甘丹寺 -> 米拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "两个景点都与藏族文化和宗教有关，甘丹寺是藏传佛教寺庙，米拉山口也属于藏族文化IPint、",
        "confidence": 0.6,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:29:43,837 - INFO - __main__ - 已处理 24/250 个节点
2025-07-23 22:29:43,837 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:29:43,837 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:29:43,838 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:29:46,346 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 和平解放纪念碑
2025-07-23 22:29:46,359 - INFO - text_processor - 成功处理实体: 和平解放纪念碑
2025-07-23 22:29:49,701 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫广场
2025-07-23 22:29:49,713 - INFO - text_processor - 成功处理实体: 布达拉宫广场
2025-07-23 22:29:56,298 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那根拉山口
2025-07-23 22:29:56,308 - INFO - text_processor - 成功处理实体: 那根拉山口
2025-07-23 22:29:56,308 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:30:15,898 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 布达拉宫广场: Expecting value: line 3 column 1 (char 2), content: 

```json
[
  {
    "type": "NEARBY",
    "reason": "两个景点都位于拉萨市中心区域",
    "confidence": 0.9,
    "direction": "bidirectional"
  },
  {
    "type": "CULTURAL_RELATED",
    "reason": "都位于西藏，具有文化历史意义",
    "confidence": 0.8,
    "direction": "bidirectional"
  }
]
```
2025-07-23 22:30:15,898 - ERROR - text_processor - JSON 解析失败 for 和平解放纪念碑 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[]
```
2025-07-23 22:30:15,899 - ERROR - text_processor - JSON 解析失败 for 布达拉宫广场 -> 那根拉山口: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {"type": "CULTURAL_RELATED", "reason": "都与中国西藏地区的宗教文化有关" , "confidence": 0.6, "direction": "bidirectional"}
]
```
2025-07-23 22:30:15,899 - INFO - __main__ - 已处理 27/250 个节点
2025-07-23 22:30:15,899 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:30:15,900 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:30:15,900 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:30:23,098 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 木如寺印经院
2025-07-23 22:30:23,110 - INFO - text_processor - 成功处理实体: 木如寺印经院
2025-07-23 22:30:27,104 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 下密寺
2025-07-23 22:30:27,113 - INFO - text_processor - 成功处理实体: 下密寺
2025-07-23 22:30:35,424 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驻藏大臣衙门遗址
2025-07-23 22:30:35,436 - INFO - text_processor - 成功处理实体: 驻藏大臣衙门遗址
2025-07-23 22:30:35,436 - INFO - text_processor - 过滤后景点数量: 3
2025-07-23 22:30:56,673 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 下密寺: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "CULTURAL_RELATED",
        "reason": "都属于藏传佛教寺庙，并且都与格鲁派教派有关。",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "HISTORICAL_LINK",
        "reason": "都作为宗教场所存在于拉萨，并且可能有共同的历史背景。",
        "confidence": 0.7,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:30:56,674 - ERROR - text_processor - JSON 解析失败 for 木如寺印经院 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:30:56,675 - ERROR - text_processor - JSON 解析失败 for 下密寺 -> 驻藏大臣衙门遗址: Expecting value: line 3 column 1 (char 2), content: 

```json
[
    {
        "type": "NEARBY",
        "reason": "两个景点都位于拉萨市中心区域",
        "confidence": 0.9,
        "direction": "bidirectional"
    },
    {
        "type": "CULTURAL_RELATED",
        "reason": "都是藏传佛教寺庙，属于同一个宗教体系",
        "confidence": 0.85,
        "direction": "bidirectional"
    },
    {
        "type": "COMPLEMENTARY_VISIT",
        "reason": "都位于拉萨市中心，适合一起游览",
        "confidence": 0.8,
        "direction": "bidirectional"
    }
]
```
2025-07-23 22:30:56,675 - INFO - __main__ - 已处理 30/250 个节点
2025-07-23 22:30:56,676 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:30:56,676 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:30:56,676 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:31:03,625 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺路
2025-07-23 22:31:03,641 - INFO - text_processor - 成功处理实体: 小昭寺路
2025-07-23 22:31:03,642 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:31:10,450 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨民族文化艺术宫
2025-07-23 22:31:10,463 - INFO - text_processor - 成功处理实体: 拉萨民族文化艺术宫
2025-07-23 22:31:10,464 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:31:20,506 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 5238
2025-07-23 22:31:20,538 - INFO - text_processor - 成功处理实体: 5238
2025-07-23 22:31:20,539 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:31:20,539 - INFO - __main__ - 已处理 33/250 个节点
2025-07-23 22:31:20,539 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:31:20,539 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:31:20,539 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:31:20,540 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:31:32,653 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木措扎西岛
2025-07-23 22:31:32,665 - INFO - text_processor - 成功处理实体: 纳木措扎西岛
2025-07-23 22:31:32,665 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:31:39,624 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药文化博览中心
2025-07-23 22:31:39,636 - INFO - text_processor - 成功处理实体: 西藏藏医药文化博览中心
2025-07-23 22:31:47,016 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达扎路恭纪功碑
2025-07-23 22:31:47,062 - INFO - text_processor - 成功处理实体: 达扎路恭纪功碑
2025-07-23 22:31:47,062 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 22:31:47,062 - INFO - __main__ - 已处理 36/250 个节点
2025-07-23 22:31:47,062 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:31:47,062 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:31:47,062 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:31:47,062 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:32:00,504 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 甲玛王宫
2025-07-23 22:32:00,515 - INFO - text_processor - 成功处理实体: 甲玛王宫
2025-07-23 22:32:03,195 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎希寺
2025-07-23 22:32:03,206 - INFO - text_processor - 成功处理实体: 扎希寺
2025-07-23 22:32:11,073 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚布寺
2025-07-23 22:32:11,084 - INFO - text_processor - 成功处理实体: 楚布寺
2025-07-23 22:32:11,084 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:32:11,084 - INFO - __main__ - 已处理 39/250 个节点
2025-07-23 22:32:11,085 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:32:11,085 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:32:11,085 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:32:24,979 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞干布出生地
2025-07-23 22:32:24,991 - INFO - text_processor - 成功处理实体: 松赞干布出生地
2025-07-23 22:32:34,609 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 聂当度母殿和聂当大佛
2025-07-23 22:32:34,621 - INFO - text_processor - 成功处理实体: 聂当度母殿和聂当大佛
2025-07-23 22:32:46,274 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 策门林寺
2025-07-23 22:32:46,284 - INFO - text_processor - 成功处理实体: 策门林寺
2025-07-23 22:32:46,284 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 22:32:46,284 - INFO - __main__ - 已处理 42/250 个节点
2025-07-23 22:32:46,284 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:32:46,285 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:32:46,285 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:32:51,469 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鲁普岩寺
2025-07-23 22:32:51,481 - INFO - text_processor - 成功处理实体: 鲁普岩寺
2025-07-23 22:33:01,017 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仙足岛
2025-07-23 22:33:01,029 - INFO - text_processor - 成功处理实体: 仙足岛
2025-07-23 22:33:06,110 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲贡古遗址
2025-07-23 22:33:06,122 - INFO - text_processor - 成功处理实体: 曲贡古遗址
2025-07-23 22:33:06,122 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 22:33:06,122 - INFO - __main__ - 已处理 45/250 个节点
2025-07-23 22:33:06,122 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:33:06,123 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:33:06,123 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:33:09,863 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺
2025-07-23 22:33:09,872 - INFO - text_processor - 成功处理实体: 乃琼寺
2025-07-23 22:33:16,486 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次巴拉康寺
2025-07-23 22:33:16,498 - INFO - text_processor - 成功处理实体: 次巴拉康寺
2025-07-23 22:33:23,893 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 清政府驻藏大臣衙门旧址
2025-07-23 22:33:23,904 - INFO - text_processor - 成功处理实体: 清政府驻藏大臣衙门旧址
2025-07-23 22:33:23,905 - INFO - text_processor - 过滤后景点数量: 1
2025-07-23 22:33:23,905 - INFO - __main__ - 已处理 48/250 个节点
2025-07-23 22:33:23,905 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:33:23,905 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:33:23,905 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:33:23,906 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:33:31,412 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热振寺
2025-07-23 22:33:31,429 - INFO - text_processor - 成功处理实体: 热振寺
2025-07-23 22:33:39,908 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 关帝庙
2025-07-23 22:33:39,918 - INFO - text_processor - 成功处理实体: 关帝庙
2025-07-23 22:33:39,918 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:33:45,234 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德林
2025-07-23 22:33:45,254 - INFO - text_processor - 成功处理实体: 喜德林
2025-07-23 22:33:45,254 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:33:45,255 - INFO - __main__ - 已处理 51/250 个节点
2025-07-23 22:33:45,255 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:33:45,255 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:33:45,255 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:33:45,256 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:33:54,277 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白色寺
2025-07-23 22:33:54,290 - INFO - text_processor - 成功处理实体: 白色寺
2025-07-23 22:33:54,291 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:00,208 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏非遗博物馆附属建筑1栋
2025-07-23 22:34:00,221 - INFO - text_processor - 成功处理实体: 西藏非遗博物馆附属建筑1栋
2025-07-23 22:34:00,222 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:05,560 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木寺
2025-07-23 22:34:05,570 - INFO - text_processor - 成功处理实体: 纳木寺
2025-07-23 22:34:05,570 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:34:05,570 - INFO - __main__ - 已处理 54/250 个节点
2025-07-23 22:34:05,571 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:34:05,571 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:34:05,571 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:34:05,571 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:12,276 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县楚杰寺
2025-07-23 22:34:12,286 - INFO - text_processor - 成功处理实体: 林周县楚杰寺
2025-07-23 22:34:12,286 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:17,894 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 中华文化园露营地
2025-07-23 22:34:17,907 - INFO - text_processor - 成功处理实体: 中华文化园露营地
2025-07-23 22:34:17,907 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:33,191 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 秀色才纳净土博览园
2025-07-23 22:34:33,202 - INFO - text_processor - 成功处理实体: 秀色才纳净土博览园
2025-07-23 22:34:33,202 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:34:33,203 - INFO - __main__ - 已处理 57/250 个节点
2025-07-23 22:34:33,203 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:34:33,203 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:34:33,203 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:34:33,203 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:41,997 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶马古道马帮落脚点遗址
2025-07-23 22:34:42,008 - INFO - text_processor - 成功处理实体: 茶马古道马帮落脚点遗址
2025-07-23 22:34:42,008 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:46,740 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 山野星空露营-吉隆营地
2025-07-23 22:34:46,752 - INFO - text_processor - 成功处理实体: 山野星空露营-吉隆营地
2025-07-23 22:34:46,753 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:34:55,475 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 奇秀园林
2025-07-23 22:34:55,486 - INFO - text_processor - 成功处理实体: 奇秀园林
2025-07-23 22:34:55,486 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:34:55,487 - INFO - __main__ - 已处理 60/250 个节点
2025-07-23 22:34:55,487 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:34:55,487 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:34:55,487 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:34:55,487 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:01,074 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛堆吉曲康桑区
2025-07-23 22:35:01,089 - INFO - text_processor - 成功处理实体: 洛堆吉曲康桑区
2025-07-23 22:35:01,089 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:11,567 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格桑林卡
2025-07-23 22:35:11,580 - INFO - text_processor - 成功处理实体: 格桑林卡
2025-07-23 22:35:11,580 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:19,656 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔玉贡康寺
2025-07-23 22:35:19,667 - INFO - text_processor - 成功处理实体: 塔玉贡康寺
2025-07-23 22:35:19,668 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:35:19,668 - INFO - __main__ - 已处理 63/250 个节点
2025-07-23 22:35:19,669 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:35:19,669 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:35:19,669 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:35:19,669 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:27,058 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吉布寺
2025-07-23 22:35:27,070 - INFO - text_processor - 成功处理实体: 吉布寺
2025-07-23 22:35:27,070 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:44,271 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 美珠杂日生态乐园
2025-07-23 22:35:44,283 - INFO - text_processor - 成功处理实体: 美珠杂日生态乐园
2025-07-23 22:35:44,283 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:35:49,970 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 舒适的露营地
2025-07-23 22:35:49,980 - INFO - text_processor - 成功处理实体: 舒适的露营地
2025-07-23 22:35:49,980 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:35:49,980 - INFO - __main__ - 已处理 66/250 个节点
2025-07-23 22:35:49,981 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:35:49,981 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:35:49,981 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:35:49,981 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:00,234 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 钱学森雕像
2025-07-23 22:36:00,245 - INFO - text_processor - 成功处理实体: 钱学森雕像
2025-07-23 22:36:00,245 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:09,306 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎卓卓休闲露营基地
2025-07-23 22:36:09,316 - INFO - text_processor - 成功处理实体: 嘎卓卓休闲露营基地
2025-07-23 22:36:09,317 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:17,195 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 明珠林
2025-07-23 22:36:17,205 - INFO - text_processor - 成功处理实体: 明珠林
2025-07-23 22:36:17,205 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:36:17,205 - INFO - __main__ - 已处理 69/250 个节点
2025-07-23 22:36:17,205 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:36:17,205 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:36:17,206 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:36:17,206 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:23,196 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏林周草牧业科技小院
2025-07-23 22:36:23,247 - INFO - text_processor - 成功处理实体: 西藏林周草牧业科技小院
2025-07-23 22:36:23,247 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:28,086 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 内马厩
2025-07-23 22:36:28,097 - INFO - text_processor - 成功处理实体: 内马厩
2025-07-23 22:36:28,097 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:44,898 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尤隆布
2025-07-23 22:36:44,908 - INFO - text_processor - 成功处理实体: 尤隆布
2025-07-23 22:36:44,908 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:36:44,909 - INFO - __main__ - 已处理 72/250 个节点
2025-07-23 22:36:44,909 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:36:44,909 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:36:44,909 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:36:44,910 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:36:50,940 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙日秋摩
2025-07-23 22:36:50,951 - INFO - text_processor - 成功处理实体: 龙日秋摩
2025-07-23 22:36:50,952 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:01,270 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴机康
2025-07-23 22:37:01,281 - INFO - text_processor - 成功处理实体: 洛巴机康
2025-07-23 22:37:01,281 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:09,892 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布朗
2025-07-23 22:37:09,902 - INFO - text_processor - 成功处理实体: 崩布朗
2025-07-23 22:37:09,902 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:37:09,902 - INFO - __main__ - 已处理 75/250 个节点
2025-07-23 22:37:09,902 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:37:09,903 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:37:09,903 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:37:09,903 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:14,247 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 加错
2025-07-23 22:37:14,257 - INFO - text_processor - 成功处理实体: 加错
2025-07-23 22:37:14,258 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:19,895 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达尔多拉
2025-07-23 22:37:19,904 - INFO - text_processor - 成功处理实体: 达尔多拉
2025-07-23 22:37:19,904 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:27,976 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 林周县杰堆寺
2025-07-23 22:37:27,986 - INFO - text_processor - 成功处理实体: 林周县杰堆寺
2025-07-23 22:37:27,987 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:37:27,987 - INFO - __main__ - 已处理 78/250 个节点
2025-07-23 22:37:27,987 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:37:27,987 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:37:27,987 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:37:27,988 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:34,096 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 扎日阿布塘
2025-07-23 22:37:34,107 - INFO - text_processor - 成功处理实体: 扎日阿布塘
2025-07-23 22:37:34,107 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:40,825 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关区净土国家级旅游景区
2025-07-23 22:37:40,836 - INFO - text_processor - 成功处理实体: 城关区净土国家级旅游景区
2025-07-23 22:37:40,836 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:47,865 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎布友哒露营基地
2025-07-23 22:37:47,874 - INFO - text_processor - 成功处理实体: 嘎布友哒露营基地
2025-07-23 22:37:47,874 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:37:47,874 - INFO - __main__ - 已处理 81/250 个节点
2025-07-23 22:37:47,875 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:37:47,875 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:37:47,875 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:37:47,875 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:37:58,436 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城北萨斯格桑林卡
2025-07-23 22:37:58,449 - INFO - text_processor - 成功处理实体: 城北萨斯格桑林卡
2025-07-23 22:37:58,449 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:05,707 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏唐卡画院
2025-07-23 22:38:05,717 - INFO - text_processor - 成功处理实体: 西藏唐卡画院
2025-07-23 22:38:05,717 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:18,098 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨奇趣昆虫科普展
2025-07-23 22:38:18,108 - INFO - text_processor - 成功处理实体: 拉萨奇趣昆虫科普展
2025-07-23 22:38:18,109 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:38:18,109 - INFO - __main__ - 已处理 84/250 个节点
2025-07-23 22:38:18,109 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:38:18,110 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:38:18,110 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:38:18,110 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:26,654 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎雕像
2025-07-23 22:38:26,663 - INFO - text_processor - 成功处理实体: 吞米桑布扎雕像
2025-07-23 22:38:26,663 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:32,173 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 涉溪山谷露营地
2025-07-23 22:38:32,185 - INFO - text_processor - 成功处理实体: 涉溪山谷露营地
2025-07-23 22:38:32,186 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:39,408 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 尼木吞巴非遗中心
2025-07-23 22:38:39,416 - INFO - text_processor - 成功处理实体: 尼木吞巴非遗中心
2025-07-23 22:38:39,417 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:38:39,417 - INFO - __main__ - 已处理 87/250 个节点
2025-07-23 22:38:39,417 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:38:39,417 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:38:39,417 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:38:39,418 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:48,211 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 群巴
2025-07-23 22:38:48,220 - INFO - text_processor - 成功处理实体: 群巴
2025-07-23 22:38:48,220 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:38:55,015 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 热卡扎日追寺
2025-07-23 22:38:55,025 - INFO - text_processor - 成功处理实体: 热卡扎日追寺
2025-07-23 22:38:55,025 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:00,463 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 李四光雕像
2025-07-23 22:39:00,472 - INFO - text_processor - 成功处理实体: 李四光雕像
2025-07-23 22:39:00,472 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:39:00,472 - INFO - __main__ - 已处理 90/250 个节点
2025-07-23 22:39:00,473 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:39:00,473 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:39:00,473 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:39:00,473 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:11,656 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲苏
2025-07-23 22:39:11,666 - INFO - text_processor - 成功处理实体: 曲苏
2025-07-23 22:39:11,666 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:17,441 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏汉断桥玻璃阳光房
2025-07-23 22:39:17,449 - INFO - text_processor - 成功处理实体: 藏汉断桥玻璃阳光房
2025-07-23 22:39:17,449 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:27,800 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 梦创拉萨
2025-07-23 22:39:27,809 - INFO - text_processor - 成功处理实体: 梦创拉萨
2025-07-23 22:39:27,809 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:39:27,809 - INFO - __main__ - 已处理 93/250 个节点
2025-07-23 22:39:27,809 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:39:27,809 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:39:27,809 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:39:27,810 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:37,706 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛日扎扎
2025-07-23 22:39:37,714 - INFO - text_processor - 成功处理实体: 玛日扎扎
2025-07-23 22:39:37,714 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:44,623 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪格拉山
2025-07-23 22:39:44,632 - INFO - text_processor - 成功处理实体: 雪格拉山
2025-07-23 22:39:44,633 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:53,006 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 密宗院
2025-07-23 22:39:53,014 - INFO - text_processor - 成功处理实体: 密宗院
2025-07-23 22:39:53,014 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:39:53,014 - INFO - __main__ - 已处理 96/250 个节点
2025-07-23 22:39:53,015 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:39:53,015 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:39:53,015 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:39:53,015 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:39:58,368 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 科比台球纪念馆
2025-07-23 22:39:58,379 - INFO - text_processor - 成功处理实体: 科比台球纪念馆
2025-07-23 22:39:58,379 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:07,722 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡吉嘎彩林卡
2025-07-23 22:40:07,732 - INFO - text_processor - 成功处理实体: 贡吉嘎彩林卡
2025-07-23 22:40:07,732 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:15,209 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 颂吉东阁大昭寺店
2025-07-23 22:40:15,217 - INFO - text_processor - 成功处理实体: 颂吉东阁大昭寺店
2025-07-23 22:40:15,217 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:40:15,219 - INFO - __main__ - 已处理 99/250 个节点
2025-07-23 22:40:15,219 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:40:15,219 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:40:15,219 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:40:15,219 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:23,378 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水木林卡
2025-07-23 22:40:23,388 - INFO - text_processor - 成功处理实体: 水木林卡
2025-07-23 22:40:23,388 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:30,047 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 柳梧桑女林卡
2025-07-23 22:40:30,071 - INFO - text_processor - 成功处理实体: 柳梧桑女林卡
2025-07-23 22:40:30,071 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:39,309 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思源山
2025-07-23 22:40:39,318 - INFO - text_processor - 成功处理实体: 思源山
2025-07-23 22:40:39,318 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:40:39,318 - INFO - __main__ - 已处理 102/250 个节点
2025-07-23 22:40:39,319 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:40:39,319 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:40:39,319 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:40:39,319 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:45,432 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 喜德曲郭卓吉林卡
2025-07-23 22:40:45,441 - INFO - text_processor - 成功处理实体: 喜德曲郭卓吉林卡
2025-07-23 22:40:45,441 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:40:53,662 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 24小时自助图书馆(西藏图书馆店)
2025-07-23 22:40:53,682 - INFO - text_processor - 成功处理实体: 24小时自助图书馆(西藏图书馆店)
2025-07-23 22:40:53,682 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:00,108 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 朗唐寺
2025-07-23 22:41:00,148 - INFO - text_processor - 成功处理实体: 朗唐寺
2025-07-23 22:41:00,149 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:41:00,149 - INFO - __main__ - 已处理 105/250 个节点
2025-07-23 22:41:00,149 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:41:00,149 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:41:00,150 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:41:00,150 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:04,788 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 智昭产业园净土生态体验馆
2025-07-23 22:41:04,797 - INFO - text_processor - 成功处理实体: 智昭产业园净土生态体验馆
2025-07-23 22:41:04,797 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:12,526 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏擦擦文化展览馆
2025-07-23 22:41:12,536 - INFO - text_processor - 成功处理实体: 西藏擦擦文化展览馆
2025-07-23 22:41:12,536 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:18,395 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-供灯室
2025-07-23 22:41:18,403 - INFO - text_processor - 成功处理实体: 大昭寺-供灯室
2025-07-23 22:41:18,404 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:41:18,404 - INFO - __main__ - 已处理 108/250 个节点
2025-07-23 22:41:18,404 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:41:18,404 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:41:18,404 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:41:18,404 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:33,592 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 22:41:33,602 - INFO - text_processor - 成功处理实体: 龙皇精品紫铜佛像扎基寺分店
2025-07-23 22:41:33,602 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:41,267 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达巧拉康
2025-07-23 22:41:41,275 - INFO - text_processor - 成功处理实体: 达巧拉康
2025-07-23 22:41:41,275 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:48,651 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨湾(网红打卡点)
2025-07-23 22:41:48,659 - INFO - text_processor - 成功处理实体: 拉萨湾(网红打卡点)
2025-07-23 22:41:48,659 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:41:48,659 - INFO - __main__ - 已处理 111/250 个节点
2025-07-23 22:41:48,659 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:41:48,660 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:41:48,660 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:41:48,660 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:53,843 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松赞拉康
2025-07-23 22:41:53,851 - INFO - text_processor - 成功处理实体: 松赞拉康
2025-07-23 22:41:53,852 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:41:59,529 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 22:41:59,540 - INFO - text_processor - 成功处理实体: 拉萨南北山绿化造林·纳金山一号地
2025-07-23 22:41:59,541 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:10,214 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德勒林卡
2025-07-23 22:42:10,224 - INFO - text_processor - 成功处理实体: 德勒林卡
2025-07-23 22:42:10,224 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:42:10,224 - INFO - __main__ - 已处理 114/250 个节点
2025-07-23 22:42:10,225 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:42:10,225 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:42:10,225 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:42:10,225 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:24,826 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 羌仓
2025-07-23 22:42:24,834 - INFO - text_processor - 成功处理实体: 羌仓
2025-07-23 22:42:24,835 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:29,682 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 依拉
2025-07-23 22:42:29,691 - INFO - text_processor - 成功处理实体: 依拉
2025-07-23 22:42:29,691 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:43,190 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玉隆
2025-07-23 22:42:43,201 - INFO - text_processor - 成功处理实体: 玉隆
2025-07-23 22:42:43,201 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:42:43,201 - INFO - __main__ - 已处理 117/250 个节点
2025-07-23 22:42:43,201 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:42:43,202 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:42:43,202 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:42:43,202 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:51,183 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 22:42:51,192 - INFO - text_processor - 成功处理实体: 西藏佛学院拉萨市哲蚌寺分院
2025-07-23 22:42:51,192 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:42:59,849 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林宗萨
2025-07-23 22:42:59,859 - INFO - text_processor - 成功处理实体: 德吉林宗萨
2025-07-23 22:42:59,860 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:06,676 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 强嘎日追寺
2025-07-23 22:43:06,685 - INFO - text_processor - 成功处理实体: 强嘎日追寺
2025-07-23 22:43:06,685 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:43:06,685 - INFO - __main__ - 已处理 120/250 个节点
2025-07-23 22:43:06,686 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:43:06,686 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:43:06,686 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:43:06,686 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:13,322 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃琼寺-厨房
2025-07-23 22:43:13,332 - INFO - text_processor - 成功处理实体: 乃琼寺-厨房
2025-07-23 22:43:13,332 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:22,031 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷拉
2025-07-23 22:43:22,040 - INFO - text_processor - 成功处理实体: 列卜廷拉
2025-07-23 22:43:22,040 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:31,835 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿热康参
2025-07-23 22:43:31,844 - INFO - text_processor - 成功处理实体: 阿热康参
2025-07-23 22:43:31,844 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:43:31,845 - INFO - __main__ - 已处理 123/250 个节点
2025-07-23 22:43:31,845 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:43:31,845 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:43:31,845 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:43:31,845 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:48,895 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 哲蚌寺-卓玛拉康
2025-07-23 22:43:48,903 - INFO - text_processor - 成功处理实体: 哲蚌寺-卓玛拉康
2025-07-23 22:43:48,904 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:43:55,791 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宗角禄康公园纪念碑
2025-07-23 22:43:55,799 - INFO - text_processor - 成功处理实体: 宗角禄康公园纪念碑
2025-07-23 22:43:55,800 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:00,692 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 小昭寺辩经场
2025-07-23 22:44:00,701 - INFO - text_processor - 成功处理实体: 小昭寺辩经场
2025-07-23 22:44:00,701 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:44:00,702 - INFO - __main__ - 已处理 126/250 个节点
2025-07-23 22:44:00,702 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:44:00,702 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:44:00,702 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:44:00,702 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:13,249 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺扎迪(林卡)
2025-07-23 22:44:13,256 - INFO - text_processor - 成功处理实体: 色拉寺扎迪(林卡)
2025-07-23 22:44:13,257 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:21,447 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错国家风景区
2025-07-23 22:44:21,461 - INFO - text_processor - 成功处理实体: 纳木错国家风景区
2025-07-23 22:44:21,461 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:27,675 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 国家级拉萨经开区
2025-07-23 22:44:27,686 - INFO - text_processor - 成功处理实体: 国家级拉萨经开区
2025-07-23 22:44:27,686 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:44:27,686 - INFO - __main__ - 已处理 129/250 个节点
2025-07-23 22:44:27,687 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:44:27,687 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:44:27,687 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:44:27,687 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:35,814 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰热康参
2025-07-23 22:44:35,822 - INFO - text_processor - 成功处理实体: 恰热康参
2025-07-23 22:44:35,822 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:44,563 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉寺杰扎仓
2025-07-23 22:44:44,571 - INFO - text_processor - 成功处理实体: 色拉寺杰扎仓
2025-07-23 22:44:44,571 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:51,490 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 丁真隆巴
2025-07-23 22:44:51,500 - INFO - text_processor - 成功处理实体: 丁真隆巴
2025-07-23 22:44:51,501 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:44:51,501 - INFO - __main__ - 已处理 132/250 个节点
2025-07-23 22:44:51,501 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:44:51,501 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:44:51,501 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:44:51,502 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:44:58,502 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那若果
2025-07-23 22:44:58,510 - INFO - text_processor - 成功处理实体: 那若果
2025-07-23 22:44:58,510 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:06,688 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 所布多纳
2025-07-23 22:45:06,697 - INFO - text_processor - 成功处理实体: 所布多纳
2025-07-23 22:45:06,698 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:15,258 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 色拉大乘洲
2025-07-23 22:45:15,267 - INFO - text_processor - 成功处理实体: 色拉大乘洲
2025-07-23 22:45:15,268 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:45:15,268 - INFO - __main__ - 已处理 135/250 个节点
2025-07-23 22:45:15,268 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:45:15,269 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:45:15,269 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:45:15,269 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:23,433 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 纳木错
2025-07-23 22:45:23,441 - INFO - text_processor - 成功处理实体: 纳木错
2025-07-23 22:45:23,441 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:33,616 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 悠远的天空自驾车营地
2025-07-23 22:45:33,624 - INFO - text_processor - 成功处理实体: 悠远的天空自驾车营地
2025-07-23 22:45:33,624 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:47,333 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 雪江憩居鲁突
2025-07-23 22:45:47,342 - INFO - text_processor - 成功处理实体: 雪江憩居鲁突
2025-07-23 22:45:47,342 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:45:47,342 - INFO - __main__ - 已处理 138/250 个节点
2025-07-23 22:45:47,342 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:45:47,342 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:45:47,342 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:45:47,342 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:45:57,240 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑珠颇章
2025-07-23 22:45:57,248 - INFO - text_processor - 成功处理实体: 桑珠颇章
2025-07-23 22:45:57,248 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:08,382 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 千年古树
2025-07-23 22:46:08,392 - INFO - text_processor - 成功处理实体: 千年古树
2025-07-23 22:46:08,393 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:19,413 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨之眼
2025-07-23 22:46:19,422 - INFO - text_processor - 成功处理实体: 拉萨之眼
2025-07-23 22:46:19,422 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:46:19,422 - INFO - __main__ - 已处理 141/250 个节点
2025-07-23 22:46:19,422 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:46:19,423 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:46:19,423 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:46:19,423 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:28,162 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拉萨市综合展馆
2025-07-23 22:46:28,169 - INFO - text_processor - 成功处理实体: 拉萨市综合展馆
2025-07-23 22:46:28,169 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:35,727 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 顶杰寺
2025-07-23 22:46:35,737 - INFO - text_processor - 成功处理实体: 顶杰寺
2025-07-23 22:46:35,737 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:40,413 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 罗格岭
2025-07-23 22:46:40,425 - INFO - text_processor - 成功处理实体: 罗格岭
2025-07-23 22:46:40,425 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:46:40,426 - INFO - __main__ - 已处理 144/250 个节点
2025-07-23 22:46:40,426 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:46:40,426 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:46:40,427 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:46:40,427 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:47,292 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏医药文化馆
2025-07-23 22:46:47,302 - INFO - text_processor - 成功处理实体: 藏医药文化馆
2025-07-23 22:46:47,304 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:46:52,367 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日月湖
2025-07-23 22:46:52,376 - INFO - text_processor - 成功处理实体: 日月湖
2025-07-23 22:46:52,376 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:47:00,320 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水上乐园
2025-07-23 22:47:00,329 - INFO - text_processor - 成功处理实体: 水上乐园
2025-07-23 22:47:00,329 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:47:00,329 - INFO - __main__ - 已处理 147/250 个节点
2025-07-23 22:47:00,329 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:47:00,329 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:47:00,330 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:47:00,330 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:47:10,629 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白纳村
2025-07-23 22:47:10,638 - INFO - text_processor - 成功处理实体: 白纳村
2025-07-23 22:47:10,638 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:47:18,575 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 拼尤明丘
2025-07-23 22:47:18,582 - INFO - text_processor - 成功处理实体: 拼尤明丘
2025-07-23 22:47:18,582 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:47:32,596 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 驳叶
2025-07-23 22:47:32,605 - INFO - text_processor - 成功处理实体: 驳叶
2025-07-23 22:47:32,606 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:47:32,606 - INFO - __main__ - 已处理 150/250 个节点
2025-07-23 22:47:32,606 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:47:32,606 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:47:32,606 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:47:32,606 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:47:49,902 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 勒望来坰
2025-07-23 22:47:49,911 - INFO - text_processor - 成功处理实体: 勒望来坰
2025-07-23 22:47:49,911 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:01,316 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 斋囊
2025-07-23 22:48:01,325 - INFO - text_processor - 成功处理实体: 斋囊
2025-07-23 22:48:01,326 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:14,367 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 绿子
2025-07-23 22:48:14,374 - INFO - text_processor - 成功处理实体: 绿子
2025-07-23 22:48:14,375 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:48:14,375 - INFO - __main__ - 已处理 153/250 个节点
2025-07-23 22:48:14,375 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:48:14,375 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:48:14,375 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:48:14,375 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:25,743 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列玛拉
2025-07-23 22:48:25,751 - INFO - text_processor - 成功处理实体: 列玛拉
2025-07-23 22:48:25,751 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:33,981 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 阿巴吉康萨巴
2025-07-23 22:48:33,990 - INFO - text_processor - 成功处理实体: 阿巴吉康萨巴
2025-07-23 22:48:33,990 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:46,023 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 苏布列日
2025-07-23 22:48:46,033 - INFO - text_processor - 成功处理实体: 苏布列日
2025-07-23 22:48:46,033 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:48:46,033 - INFO - __main__ - 已处理 156/250 个节点
2025-07-23 22:48:46,033 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:48:46,034 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:48:46,034 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:48:46,034 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:53,328 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 松格果觉(白宫门厅)
2025-07-23 22:48:53,336 - INFO - text_processor - 成功处理实体: 松格果觉(白宫门厅)
2025-07-23 22:48:53,337 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:48:59,394 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德吉林卡
2025-07-23 22:48:59,403 - INFO - text_processor - 成功处理实体: 德吉林卡
2025-07-23 22:48:59,404 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:09,534 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 支吾康参
2025-07-23 22:49:09,541 - INFO - text_processor - 成功处理实体: 支吾康参
2025-07-23 22:49:09,542 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:49:09,542 - INFO - __main__ - 已处理 159/250 个节点
2025-07-23 22:49:09,542 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:49:09,542 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:49:09,542 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:49:09,542 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:16,275 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 塔布康参
2025-07-23 22:49:16,284 - INFO - text_processor - 成功处理实体: 塔布康参
2025-07-23 22:49:16,284 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:23,399 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰杰玛
2025-07-23 22:49:23,408 - INFO - text_processor - 成功处理实体: 恰杰玛
2025-07-23 22:49:23,408 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:33,783 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 申扎琼
2025-07-23 22:49:33,797 - INFO - text_processor - 成功处理实体: 申扎琼
2025-07-23 22:49:33,797 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:49:33,798 - INFO - __main__ - 已处理 162/250 个节点
2025-07-23 22:49:33,798 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:49:33,798 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:49:33,799 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:49:33,799 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:40,909 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达孜映休闲露营景区
2025-07-23 22:49:40,919 - INFO - text_processor - 成功处理实体: 达孜映休闲露营景区
2025-07-23 22:49:40,919 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:49:54,878 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 札利贝札囊
2025-07-23 22:49:54,888 - INFO - text_processor - 成功处理实体: 札利贝札囊
2025-07-23 22:49:54,888 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:02,898 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 思泽
2025-07-23 22:50:02,907 - INFO - text_processor - 成功处理实体: 思泽
2025-07-23 22:50:02,907 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:50:02,908 - INFO - __main__ - 已处理 165/250 个节点
2025-07-23 22:50:02,908 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:50:02,908 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:50:02,908 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:50:02,908 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:11,982 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 救八难度母像
2025-07-23 22:50:11,992 - INFO - text_processor - 成功处理实体: 救八难度母像
2025-07-23 22:50:11,992 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:21,553 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德布
2025-07-23 22:50:21,563 - INFO - text_processor - 成功处理实体: 德布
2025-07-23 22:50:21,563 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:34,418 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 藏巴吉仓
2025-07-23 22:50:34,425 - INFO - text_processor - 成功处理实体: 藏巴吉仓
2025-07-23 22:50:34,425 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:50:34,425 - INFO - __main__ - 已处理 168/250 个节点
2025-07-23 22:50:34,426 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:50:34,426 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:50:34,426 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:50:34,426 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:39,594 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 弥勒法轮像
2025-07-23 22:50:39,601 - INFO - text_processor - 成功处理实体: 弥勒法轮像
2025-07-23 22:50:39,602 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:46,770 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑顶大门
2025-07-23 22:50:46,778 - INFO - text_processor - 成功处理实体: 桑顶大门
2025-07-23 22:50:46,778 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:50:54,068 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 德玛康参
2025-07-23 22:50:54,078 - INFO - text_processor - 成功处理实体: 德玛康参
2025-07-23 22:50:54,078 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:50:54,079 - INFO - __main__ - 已处理 171/250 个节点
2025-07-23 22:50:54,079 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:50:54,079 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:50:54,079 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:50:54,079 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:02,288 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏墨龙
2025-07-23 22:51:02,295 - INFO - text_processor - 成功处理实体: 西藏墨龙
2025-07-23 22:51:02,295 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:07,453 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两路精神纪念馆
2025-07-23 22:51:07,462 - INFO - text_processor - 成功处理实体: 两路精神纪念馆
2025-07-23 22:51:07,462 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:14,825 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 陕西民俗村
2025-07-23 22:51:14,833 - INFO - text_processor - 成功处理实体: 陕西民俗村
2025-07-23 22:51:14,833 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:51:14,833 - INFO - __main__ - 已处理 174/250 个节点
2025-07-23 22:51:14,834 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:51:14,834 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:51:14,834 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:51:14,834 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:20,824 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡德康参
2025-07-23 22:51:20,832 - INFO - text_processor - 成功处理实体: 贡德康参
2025-07-23 22:51:20,832 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:37,252 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡布岗
2025-07-23 22:51:37,261 - INFO - text_processor - 成功处理实体: 贡布岗
2025-07-23 22:51:37,261 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:45,420 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 桑康吉康
2025-07-23 22:51:45,428 - INFO - text_processor - 成功处理实体: 桑康吉康
2025-07-23 22:51:45,428 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:51:45,428 - INFO - __main__ - 已处理 177/250 个节点
2025-07-23 22:51:45,429 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:51:45,429 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:51:45,429 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:51:45,429 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:51,350 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 坛城院
2025-07-23 22:51:51,358 - INFO - text_processor - 成功处理实体: 坛城院
2025-07-23 22:51:51,359 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:51:59,623 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 墨脱石锅
2025-07-23 22:51:59,633 - INFO - text_processor - 成功处理实体: 墨脱石锅
2025-07-23 22:51:59,633 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:07,285 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 城关净土
2025-07-23 22:52:07,294 - INFO - text_processor - 成功处理实体: 城关净土
2025-07-23 22:52:07,294 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:52:07,294 - INFO - __main__ - 已处理 180/250 个节点
2025-07-23 22:52:07,294 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:52:07,295 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:52:07,295 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:52:07,295 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:12,044 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 宝藏局雪造币厂
2025-07-23 22:52:12,050 - INFO - text_processor - 成功处理实体: 宝藏局雪造币厂
2025-07-23 22:52:12,051 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:17,579 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 札隆纳交
2025-07-23 22:52:17,588 - INFO - text_processor - 成功处理实体: 札隆纳交
2025-07-23 22:52:17,588 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:26,911 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西印经院
2025-07-23 22:52:26,918 - INFO - text_processor - 成功处理实体: 西印经院
2025-07-23 22:52:26,918 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:52:26,918 - INFO - __main__ - 已处理 183/250 个节点
2025-07-23 22:52:26,919 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:52:26,919 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:52:26,919 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:52:26,919 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:30,960 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 布达拉宫观景台
2025-07-23 22:52:30,970 - INFO - text_processor - 成功处理实体: 布达拉宫观景台
2025-07-23 22:52:30,970 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:38,040 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 茶文化馆
2025-07-23 22:52:38,050 - INFO - text_processor - 成功处理实体: 茶文化馆
2025-07-23 22:52:38,051 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:43,696 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏闽兴
2025-07-23 22:52:43,705 - INFO - text_processor - 成功处理实体: 西藏闽兴
2025-07-23 22:52:43,706 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:52:43,706 - INFO - __main__ - 已处理 186/250 个节点
2025-07-23 22:52:43,706 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:52:43,706 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:52:43,706 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:52:43,706 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:48,227 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 措钦大殿
2025-07-23 22:52:48,234 - INFO - text_processor - 成功处理实体: 措钦大殿
2025-07-23 22:52:48,234 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:52:54,663 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 猴爸
2025-07-23 22:52:54,672 - INFO - text_processor - 成功处理实体: 猴爸
2025-07-23 22:52:54,672 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:02,851 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 唐金寺
2025-07-23 22:53:02,859 - INFO - text_processor - 成功处理实体: 唐金寺
2025-07-23 22:53:02,859 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:53:02,859 - INFO - __main__ - 已处理 189/250 个节点
2025-07-23 22:53:02,859 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:53:02,859 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:53:02,859 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:53:02,859 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:11,783 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 巴嘎日
2025-07-23 22:53:11,791 - INFO - text_processor - 成功处理实体: 巴嘎日
2025-07-23 22:53:11,791 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:17,895 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 露野营地(城西店)
2025-07-23 22:53:17,904 - INFO - text_processor - 成功处理实体: 露野营地(城西店)
2025-07-23 22:53:17,905 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:25,583 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 冬加夏日
2025-07-23 22:53:25,594 - INFO - text_processor - 成功处理实体: 冬加夏日
2025-07-23 22:53:25,594 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:53:25,594 - INFO - __main__ - 已处理 192/250 个节点
2025-07-23 22:53:25,594 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:53:25,594 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:53:25,594 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:53:25,595 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:30,977 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-朝拜区域-
2025-07-23 22:53:30,985 - INFO - text_processor - 成功处理实体: 大昭寺-朝拜区域-
2025-07-23 22:53:30,985 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:38,408 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 曲水净土百亩连栋温室
2025-07-23 22:53:38,417 - INFO - text_processor - 成功处理实体: 曲水净土百亩连栋温室
2025-07-23 22:53:38,417 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:44,514 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 列卜廷囊
2025-07-23 22:53:44,521 - INFO - text_processor - 成功处理实体: 列卜廷囊
2025-07-23 22:53:44,521 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:53:44,522 - INFO - __main__ - 已处理 195/250 个节点
2025-07-23 22:53:44,522 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:53:44,522 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:53:44,522 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:53:44,522 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:53:51,888 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两岛街道综治网格一体化中心
2025-07-23 22:53:51,925 - INFO - text_processor - 成功处理实体: 两岛街道综治网格一体化中心
2025-07-23 22:53:51,926 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:01,908 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 堆龙德庆区旅游服务区
2025-07-23 22:54:01,916 - INFO - text_processor - 成功处理实体: 堆龙德庆区旅游服务区
2025-07-23 22:54:01,916 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:10,603 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 大昭寺-讲经场
2025-07-23 22:54:10,612 - INFO - text_processor - 成功处理实体: 大昭寺-讲经场
2025-07-23 22:54:10,612 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:54:10,612 - INFO - __main__ - 已处理 198/250 个节点
2025-07-23 22:54:10,613 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:54:10,613 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:54:10,613 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:54:10,613 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:19,045 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 历辈达赖喇嘛宝座
2025-07-23 22:54:19,053 - INFO - text_processor - 成功处理实体: 历辈达赖喇嘛宝座
2025-07-23 22:54:19,053 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:29,462 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 洛巴康参
2025-07-23 22:54:29,470 - INFO - text_processor - 成功处理实体: 洛巴康参
2025-07-23 22:54:29,471 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:37,937 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 波康参
2025-07-23 22:54:37,945 - INFO - text_processor - 成功处理实体: 波康参
2025-07-23 22:54:37,945 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:54:37,945 - INFO - __main__ - 已处理 201/250 个节点
2025-07-23 22:54:37,945 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:54:37,945 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:54:37,946 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:54:37,946 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:43,184 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 迭布吉康
2025-07-23 22:54:43,193 - INFO - text_processor - 成功处理实体: 迭布吉康
2025-07-23 22:54:43,193 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:50,699 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 水磨长廊
2025-07-23 22:54:50,708 - INFO - text_processor - 成功处理实体: 水磨长廊
2025-07-23 22:54:50,708 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:54:57,919 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 鸸鹋
2025-07-23 22:54:57,927 - INFO - text_processor - 成功处理实体: 鸸鹋
2025-07-23 22:54:57,928 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:54:57,928 - INFO - __main__ - 已处理 204/250 个节点
2025-07-23 22:54:57,928 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:54:57,928 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:54:57,929 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:54:57,929 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:02,442 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 吞米桑布扎故居
2025-07-23 22:55:02,450 - INFO - text_processor - 成功处理实体: 吞米桑布扎故居
2025-07-23 22:55:02,451 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:06,822 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 错尼拉
2025-07-23 22:55:06,831 - INFO - text_processor - 成功处理实体: 错尼拉
2025-07-23 22:55:06,831 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:12,771 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 国家网络安全宣传周西藏自治区活动展览馆
2025-07-23 22:55:12,780 - INFO - text_processor - 成功处理实体: 国家网络安全宣传周西藏自治区活动展览馆
2025-07-23 22:55:12,780 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:55:12,780 - INFO - __main__ - 已处理 207/250 个节点
2025-07-23 22:55:12,780 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:55:12,781 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:55:12,781 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:55:12,781 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:20,289 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贡如康参
2025-07-23 22:55:20,296 - INFO - text_processor - 成功处理实体: 贡如康参
2025-07-23 22:55:20,296 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:30,811 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏藏医药博物馆
2025-07-23 22:55:30,819 - INFO - text_processor - 成功处理实体: 西藏藏医药博物馆
2025-07-23 22:55:30,820 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:38,115 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 沃明心皈
2025-07-23 22:55:38,123 - INFO - text_processor - 成功处理实体: 沃明心皈
2025-07-23 22:55:38,124 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:55:38,124 - INFO - __main__ - 已处理 210/250 个节点
2025-07-23 22:55:38,124 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:55:38,124 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:55:38,124 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:55:38,125 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:44,008 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 次津扎扎
2025-07-23 22:55:44,017 - INFO - text_processor - 成功处理实体: 次津扎扎
2025-07-23 22:55:44,018 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:49,598 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 莲花宝殿
2025-07-23 22:55:49,606 - INFO - text_processor - 成功处理实体: 莲花宝殿
2025-07-23 22:55:49,606 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:55:57,251 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 接巴果热
2025-07-23 22:55:57,258 - INFO - text_processor - 成功处理实体: 接巴果热
2025-07-23 22:55:57,259 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:55:57,259 - INFO - __main__ - 已处理 213/250 个节点
2025-07-23 22:55:57,259 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:55:57,260 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:55:57,260 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:55:57,260 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:06,733 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 廊廓
2025-07-23 22:56:06,740 - INFO - text_processor - 成功处理实体: 廊廓
2025-07-23 22:56:06,740 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:12,838 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 玛拉郭
2025-07-23 22:56:12,846 - INFO - text_processor - 成功处理实体: 玛拉郭
2025-07-23 22:56:12,847 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:25,005 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 新旧西藏对比展
2025-07-23 22:56:25,013 - INFO - text_processor - 成功处理实体: 新旧西藏对比展
2025-07-23 22:56:25,013 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:56:25,013 - INFO - __main__ - 已处理 216/250 个节点
2025-07-23 22:56:25,013 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:56:25,014 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:56:25,014 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:56:25,014 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:31,815 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 擦瓦杰擦
2025-07-23 22:56:31,823 - INFO - text_processor - 成功处理实体: 擦瓦杰擦
2025-07-23 22:56:31,823 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:37,858 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 错玛
2025-07-23 22:56:37,867 - INFO - text_processor - 成功处理实体: 错玛
2025-07-23 22:56:37,867 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:50,921 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏圣坤
2025-07-23 22:56:50,929 - INFO - text_processor - 成功处理实体: 西藏圣坤
2025-07-23 22:56:50,930 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:56:50,930 - INFO - __main__ - 已处理 219/250 个节点
2025-07-23 22:56:50,930 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:56:50,930 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:56:50,930 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:56:50,930 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:56:57,574 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 荣木错拉
2025-07-23 22:56:57,580 - INFO - text_processor - 成功处理实体: 荣木错拉
2025-07-23 22:56:57,580 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:04,606 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎尔琼拉康
2025-07-23 22:57:04,614 - INFO - text_processor - 成功处理实体: 嘎尔琼拉康
2025-07-23 22:57:04,615 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:12,495 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 龙厦宅
2025-07-23 22:57:12,505 - INFO - text_processor - 成功处理实体: 龙厦宅
2025-07-23 22:57:12,505 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:57:12,506 - INFO - __main__ - 已处理 222/250 个节点
2025-07-23 22:57:12,506 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:57:12,506 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:57:12,506 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:57:12,506 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:17,327 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 格藏拉让
2025-07-23 22:57:17,334 - INFO - text_processor - 成功处理实体: 格藏拉让
2025-07-23 22:57:17,335 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:25,017 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 仁增拉康
2025-07-23 22:57:25,024 - INFO - text_processor - 成功处理实体: 仁增拉康
2025-07-23 22:57:25,026 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:32,025 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 比都康参
2025-07-23 22:57:32,034 - INFO - text_processor - 成功处理实体: 比都康参
2025-07-23 22:57:32,034 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:57:32,034 - INFO - __main__ - 已处理 225/250 个节点
2025-07-23 22:57:32,034 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:57:32,034 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:57:32,035 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:57:32,035 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:39,036 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 崩布热吉康萨巴
2025-07-23 22:57:39,046 - INFO - text_processor - 成功处理实体: 崩布热吉康萨巴
2025-07-23 22:57:39,046 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:45,483 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 则仑嘎拉
2025-07-23 22:57:45,494 - INFO - text_processor - 成功处理实体: 则仑嘎拉
2025-07-23 22:57:45,494 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:57:55,700 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 乃康鼎
2025-07-23 22:57:55,708 - INFO - text_processor - 成功处理实体: 乃康鼎
2025-07-23 22:57:55,708 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:57:55,708 - INFO - __main__ - 已处理 228/250 个节点
2025-07-23 22:57:55,708 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:57:55,709 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:57:55,709 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:57:55,709 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:04,229 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 堆龙曲
2025-07-23 22:58:04,236 - INFO - text_processor - 成功处理实体: 堆龙曲
2025-07-23 22:58:04,236 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:10,790 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 七佛殿
2025-07-23 22:58:10,797 - INFO - text_processor - 成功处理实体: 七佛殿
2025-07-23 22:58:10,797 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:17,805 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 杂朵吉康
2025-07-23 22:58:17,811 - INFO - text_processor - 成功处理实体: 杂朵吉康
2025-07-23 22:58:17,811 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:58:17,812 - INFO - __main__ - 已处理 231/250 个节点
2025-07-23 22:58:17,812 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:58:17,812 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:58:17,812 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:58:17,812 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:24,591 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 两岛生态公园
2025-07-23 22:58:24,597 - INFO - text_processor - 成功处理实体: 两岛生态公园
2025-07-23 22:58:24,598 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:33,962 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 护法殿
2025-07-23 22:58:33,971 - INFO - text_processor - 成功处理实体: 护法殿
2025-07-23 22:58:33,971 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:39,657 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 琼结崩唐
2025-07-23 22:58:39,665 - INFO - text_processor - 成功处理实体: 琼结崩唐
2025-07-23 22:58:39,665 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:58:39,665 - INFO - __main__ - 已处理 234/250 个节点
2025-07-23 22:58:39,665 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:58:39,666 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:58:39,666 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:58:39,666 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:48,204 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-07-23 22:58:48,215 - INFO - text_processor - 成功处理实体: 日喀则市乃钦康桑雪山冰川旅游区拉萨办事处
2025-07-23 22:58:48,215 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:58:55,249 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 贞丹日
2025-07-23 22:58:55,258 - INFO - text_processor - 成功处理实体: 贞丹日
2025-07-23 22:58:55,259 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:05,407 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 达孜三合农庄餐饮棋牌垂钓露营地
2025-07-23 22:59:05,416 - INFO - text_processor - 成功处理实体: 达孜三合农庄餐饮棋牌垂钓露营地
2025-07-23 22:59:05,416 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:59:05,416 - INFO - __main__ - 已处理 237/250 个节点
2025-07-23 22:59:05,416 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:59:05,418 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:59:05,418 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:59:05,418 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:12,798 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 那曲老干部退休基地
2025-07-23 22:59:12,806 - INFO - text_processor - 成功处理实体: 那曲老干部退休基地
2025-07-23 22:59:12,806 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:18,708 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 嘎藏朗
2025-07-23 22:59:18,716 - INFO - text_processor - 成功处理实体: 嘎藏朗
2025-07-23 22:59:18,716 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:31,674 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 霍钦夏玛
2025-07-23 22:59:31,683 - INFO - text_processor - 成功处理实体: 霍钦夏玛
2025-07-23 22:59:31,683 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:59:31,683 - INFO - __main__ - 已处理 240/250 个节点
2025-07-23 22:59:31,684 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:59:31,684 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:59:31,684 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:59:31,684 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:37,659 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 古格(西)
2025-07-23 22:59:37,668 - INFO - text_processor - 成功处理实体: 古格(西)
2025-07-23 22:59:37,668 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:48,935 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 西藏鼎麒
2025-07-23 22:59:48,958 - INFO - text_processor - 成功处理实体: 西藏鼎麒
2025-07-23 22:59:48,958 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 22:59:56,626 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 真木朗
2025-07-23 22:59:56,637 - INFO - text_processor - 成功处理实体: 真木朗
2025-07-23 22:59:56,637 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 22:59:56,637 - INFO - __main__ - 已处理 243/250 个节点
2025-07-23 22:59:56,637 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 22:59:56,637 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 22:59:56,638 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 22:59:56,638 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:03,975 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 轻孜林
2025-07-23 23:00:03,982 - INFO - text_processor - 成功处理实体: 轻孜林
2025-07-23 23:00:03,982 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:08,613 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 杰荣康萨
2025-07-23 23:00:08,622 - INFO - text_processor - 成功处理实体: 杰荣康萨
2025-07-23 23:00:08,622 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:14,166 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 恰琼冬让
2025-07-23 23:00:14,201 - INFO - text_processor - 成功处理实体: 恰琼冬让
2025-07-23 23:00:14,201 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 23:00:14,203 - INFO - __main__ - 已处理 246/250 个节点
2025-07-23 23:00:14,203 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 23:00:14,203 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 23:00:14,203 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 23:00:14,203 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:24,630 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 楚木查寺
2025-07-23 23:00:24,637 - INFO - text_processor - 成功处理实体: 楚木查寺
2025-07-23 23:00:24,637 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:31,952 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 彭巴热
2025-07-23 23:00:31,961 - INFO - text_processor - 成功处理实体: 彭巴热
2025-07-23 23:00:31,961 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:39,016 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 圣地香都
2025-07-23 23:00:39,027 - INFO - text_processor - 成功处理实体: 圣地香都
2025-07-23 23:00:39,028 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 23:00:39,028 - INFO - __main__ - 已处理 249/250 个节点
2025-07-23 23:00:39,028 - INFO - neo4j_crud - Neo4jCRUD 初始化完成
2025-07-23 23:00:39,028 - INFO - knowledge_graph_updater - KnowledgeGraphUpdater 初始化完成
2025-07-23 23:00:39,028 - INFO - conflict_resolution - ConflictResolver initialized
2025-07-23 23:00:39,028 - WARNING - text_processor - 描述为空，返回默认评论
2025-07-23 23:00:47,109 - INFO - knowledge_graph_updater - 成功创建/更新 Attraction 节点: 白宫门廊
2025-07-23 23:00:47,117 - INFO - text_processor - 成功处理实体: 白宫门廊
2025-07-23 23:00:47,118 - INFO - text_processor - 过滤后景点数量: 0
2025-07-23 23:00:47,118 - INFO - __main__ - 已处理 250/250 个节点
2025-07-23 23:00:47,118 - INFO - __main__ - 批次处理完成 - 大小: 250, 耗时: 2167.16秒, 成功: 250, 失败: 0
2025-07-23 23:00:49,133 - INFO - __main__ - 结果已保存到: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_batch_results.json
2025-07-23 23:00:49,148 - INFO - __main__ - 结果已保存到: D:\缓存\WeChat Files\wxid_czph6s30zbcv22\FileStorage\File\2025-07\同步\动态知识图谱的自适应演化\sync_batch_results.csv
2025-07-23 23:00:49,149 - INFO - __main__ - 
============================================================
2025-07-23 23:00:49,149 - INFO - __main__ - 同步批量处理测试结果摘要
2025-07-23 23:00:49,149 - INFO - __main__ - ============================================================
2025-07-23 23:00:49,149 - INFO - __main__ - 批次大小: 250 | 耗时: 2167.16秒 | 速度:  0.12 节点/秒 | 成功: 250 | 失败:   0
2025-07-23 23:00:49,150 - INFO - __main__ - ============================================================
2025-07-23 23:00:49,150 - INFO - neo4j_connection - Neo4j 连接已关闭
2025-07-23 23:00:49,150 - INFO - text_processor - 关闭资源
